# Sistema de Colas - Documentación DevOps

## 📋 Índice

1. [Resumen Ejecutivo](#resumen-ejecutivo)
2. [Arquitectura del Sistema](#arquitectura-del-sistema)
3. [Configuración](#configuración)
4. [Despliegue](#despliegue)
5. [Monitoreo y Diagnóstico](#monitoreo-y-diagnóstico)
6. [Troubleshooting](#troubleshooting)
7. [Comandos <PERSON>](#comandos-útiles)
8. [Logs y Alertas](#logs-y-alertas)
9. [Procedimientos de Emergencia](#procedimientos-de-emergencia)
10. [Testing](#testing)

---

## 🎯 Resumen Ejecutivo

### Problema Original

-   Los emails de verificación no se enviaban cuando los usuarios se registraban
-   Las notificaciones se encolaban pero nunca se procesaban
-   Impacto: Usuarios no podían verificar sus cuentas, afectando el flujo de registro

### Solución Implementada

-   Configuración de **queue workers** automáticos con Supervisor
-   Sistema de monitoreo y diagnóstico integrado
-   Logging específico para colas
-   Scripts de despliegue y troubleshooting

### Beneficios

-   ✅ Emails se envían automáticamente
-   ✅ Procesamiento confiable de colas
-   ✅ Monitoreo proactivo
-   ✅ Recuperación automática ante fallos

---

## 🏗️ Arquitectura del Sistema

### Componentes Principales

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Laravel App   │───▶│   Queue Table   │───▶│ Queue Workers   │
│                 │    │   (Database)    │    │  (Supervisor)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │  Email Service  │
                                              │    (Brevo)      │
                                              └─────────────────┘
```

### Flujo de Procesamiento

1. **Encolado**: Laravel encola las notificaciones en la tabla `jobs`
2. **Procesamiento**: Supervisor ejecuta workers que procesan la cola
3. **Envío**: Los workers envían emails a través de Brevo SMTP
4. **Logging**: Cada paso se registra para monitoreo

---

## ⚙️ Configuración

### Variables de Entorno

```bash
# Configuración de colas
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database-uuids

# Configuración de correo
MAIL_MAILER=smtp
MAIL_HOST=smtp-relay.brevo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=xsmtpsib-56281fc4d62a4543971d1c4eab4905517995abf39d8da4056232ac8fee0dbc3f-PCj8TARmwFrW17dD
MAIL_ENCRYPTION=tls
MAIL_FROM_NAME="Activaciones - Mulbin"
MAIL_FROM_ADDRESS="<EMAIL>"
```

### Configuración de Supervisor

**Archivo**: `docker/supervisord.conf`

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --sleep=3 --tries=3 --max-time=3600 --memory=512
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stopwaitsecs=3600
```

### Configuración de Logging

**Archivo**: `config/logging.php`

```php
'queue' => [
    'driver' => 'daily',
    'path' => storage_path('logs/queue.log'),
    'level' => env('LOG_LEVEL', 'debug'),
    'days' => 14,
    'replace_placeholders' => true,
],
```

---

## 🚀 Despliegue

### Despliegue Automático

```bash
# Ejecutar script de despliegue completo
./deploy-queue-fix.sh
```

### Despliegue Manual

```bash
# 1. Reconstruir imagen
./buildPush.prod.sh

# 2. Reiniciar servicios
docker-compose down
docker-compose up -d

# 3. Verificar estado
docker exec website2025-website2025-1 supervisorctl status
docker exec website2025-website2025-1 php artisan queue:status
```

### Verificación Post-Despliegue

```bash
# Verificar que los workers estén corriendo
docker exec website2025-website2025-1 supervisorctl status laravel-worker:*

# Probar envío de email
docker exec website2025-website2025-1 php artisan test:user-registration <EMAIL>

# Verificar logs
docker exec website2025-website2025-1 tail -f /var/log/supervisor/laravel-worker.log
```

---

## 📊 Monitoreo y Diagnóstico

### Script de Monitoreo Principal

```bash
# Monitoreo completo del sistema
./docker/monitor-queues.sh
```

**Salida esperada**:

```
=== MONITOREO DE COLAS - Thu Jan 9 05:30:00 CST 2025 ===
✅ Contenedor está corriendo

=== ESTADO DE SUPERVISOR ===
laravel-worker:laravel-worker_00   RUNNING   pid 123, uptime 1:23:45
laravel-worker:laravel-worker_01   RUNNING   pid 124, uptime 1:23:45

=== ESTADO DE LAS COLAS ===
📋 Jobs pendientes: 0
✅ Jobs fallidos: 0

✅ No hay jobs atascados
```

### Comandos de Diagnóstico

```bash
# Estado rápido de colas
docker exec website2025-website2025-1 php artisan queue:status

# Información detallada de supervisor
docker exec website2025-website2025-1 supervisorctl status

# Logs en tiempo real
docker exec website2025-website2025-1 tail -f /var/log/supervisor/laravel-worker.log

# Verificar conexión a base de datos
docker exec website2025-website2025-1 php artisan tinker --execute="DB::connection()->getPdo();"
```

---

## 🔧 Troubleshooting

### Problemas Comunes

#### 1. Workers No Están Corriendo

**Síntomas**:

-   Jobs se acumulan en la cola
-   Emails no se envían
-   `supervisorctl status` muestra workers como `STOPPED`

**Solución**:

```bash
# Reiniciar workers
docker exec website2025-website2025-1 supervisorctl restart laravel-worker:*

# Verificar logs de supervisor
docker exec website2025-website2025-1 tail -f /var/log/supervisor/supervisord.log
```

#### 2. Jobs Fallidos

**Síntomas**:

-   `php artisan queue:status` muestra jobs fallidos
-   Emails no se envían

**Solución**:

```bash
# Ver jobs fallidos
docker exec website2025-website2025-1 php artisan queue:failed

# Reintentar jobs fallidos
docker exec website2025-website2025-1 php artisan queue:retry all

# Limpiar jobs fallidos (si es necesario)
docker exec website2025-website2025-1 php artisan queue:flush
```

#### 3. Memory Leaks

**Síntomas**:

-   Workers se reinician frecuentemente
-   Logs muestran errores de memoria

**Solución**:

```bash
# Verificar configuración de memoria
docker exec website2025-website2025-1 php -i | grep memory_limit

# Ajustar límite de memoria en supervisord.conf
# --memory=512 (ya configurado)
```

#### 4. Conexión de Base de Datos

**Síntomas**:

-   Workers fallan al conectar a la base de datos
-   Logs muestran errores de conexión

**Solución**:

```bash
# Verificar conexión
docker exec website2025-website2025-1 php artisan tinker --execute="DB::connection('publiweb')->getPdo();"

# Verificar variables de entorno
docker exec website2025-website2025-1 env | grep DB_
```

---

## 📝 Comandos Útiles

### Comandos de Administración

```bash
# Estado del sistema
docker exec website2025-website2025-1 php artisan queue:status

# Procesar cola manualmente (emergencia)
docker exec website2025-website2025-1 php artisan queue:work --once

# Reiniciar workers
docker exec website2025-website2025-1 supervisorctl restart laravel-worker:*

# Limpiar cache de configuración
docker exec website2025-website2025-1 php artisan config:clear
```

### Comandos de Testing

```bash
# Probar registro completo
docker exec website2025-website2025-1 php artisan test:user-registration

# Enviar email de prueba
docker exec website2025-website2025-1 php artisan tinker --execute="
\$user = App\Models\User::first();
\$user->sendEmailVerificationNotification();
"

# Verificar configuración de colas
docker exec website2025-website2025-1 php artisan config:show queue
```

---

## 📋 Logs y Alertas

### Ubicación de Logs

```bash
# Logs del worker
/var/log/supervisor/laravel-worker.log

# Logs de supervisor
/var/log/supervisor/supervisord.log

# Logs de Laravel
/var/www/html/storage/logs/laravel.log

# Logs específicos de colas
/var/www/html/storage/logs/queue.log
```

### Monitoreo de Logs

```bash
# Logs en tiempo real
docker exec website2025-website2025-1 tail -f /var/log/supervisor/laravel-worker.log

# Buscar errores recientes
docker exec website2025-website2025-1 grep -i error /var/log/supervisor/laravel-worker.log | tail -10

# Verificar logs de Laravel
docker exec website2025-website2025-1 tail -f /var/www/html/storage/logs/laravel.log
```

### Alertas Recomendadas

1. **Jobs Atascados**: Más de 10 jobs con más de 10 minutos en cola
2. **Workers Caídos**: Supervisor reporta workers como `STOPPED`
3. **Jobs Fallidos**: Más de 5 jobs fallidos en 1 hora
4. **Memory Usage**: Workers usando más de 400MB de memoria

---

## 🚨 Procedimientos de Emergencia

### Escenario 1: Todos los Workers Están Caídos

```bash
# 1. Verificar estado
docker exec website2025-website2025-1 supervisorctl status

# 2. Reiniciar supervisor completo
docker exec website2025-website2025-1 supervisorctl restart all

# 3. Si no funciona, reiniciar contenedor
docker-compose restart website2025

# 4. Verificar recuperación
./docker/monitor-queues.sh
```

### Escenario 2: Cola Completamente Atascada

```bash
# 1. Procesar jobs manualmente
docker exec website2025-website2025-1 php artisan queue:work --timeout=60

# 2. Reiniciar workers
docker exec website2025-website2025-1 supervisorctl restart laravel-worker:*

# 3. Verificar estado
docker exec website2025-website2025-1 php artisan queue:status
```

### Escenario 3: Emails No Se Envían

```bash
# 1. Verificar configuración SMTP
docker exec website2025-website2025-1 php artisan config:show mail

# 2. Probar conexión SMTP
docker exec website2025-website2025-1 php artisan tinker --execute="
Mail::raw('Test email', function(\$message) {
    \$message->to('<EMAIL>')->subject('Test');
});
"

# 3. Verificar logs de email
docker exec website2025-website2025-1 grep -i "swift\|mail" /var/www/html/storage/logs/laravel.log
```

---

## 🧪 Testing

### Test de Funcionalidad Básica

```bash
# 1. Verificar que los workers estén corriendo
docker exec website2025-website2025-1 supervisorctl status laravel-worker:*

# 2. Probar encolado de email
docker exec website2025-website2025-1 php artisan test:user-registration <EMAIL>

# 3. Verificar que se procesó
docker exec website2025-website2025-1 php artisan queue:status
```

### Test de Carga

```bash
# Enviar múltiples emails de prueba
for i in {1..10}; do
    docker exec website2025-website2025-1 php artisan tinker --execute="
    \$user = App\Models\User::first();
    \$user->sendEmailVerificationNotification();
    "
done

# Verificar procesamiento
docker exec website2025-website2025-1 php artisan queue:status
```

### Test de Recuperación

```bash
# 1. Detener workers
docker exec website2025-website2025-1 supervisorctl stop laravel-worker:*

# 2. Enviar email (se debe encolar)
docker exec website2025-website2025-1 php artisan test:user-registration <EMAIL>

# 3. Verificar que está en cola
docker exec website2025-website2025-1 php artisan queue:status

# 4. Reiniciar workers
docker exec website2025-website2025-1 supervisorctl start laravel-worker:*

# 5. Verificar que se procesó
sleep 10
docker exec website2025-website2025-1 php artisan queue:status
```

---

## 📞 Contacto y Soporte

### Escalación de Problemas

1. **Nivel 1**: Usar scripts de monitoreo y troubleshooting
2. **Nivel 2**: Revisar logs y aplicar procedimientos de emergencia
3. **Nivel 3**: Contactar al equipo de desarrollo

### Información para Reportes

Cuando reportes un problema, incluye:

```bash
# Información del sistema
docker exec website2025-website2025-1 php artisan --version
docker exec website2025-website2025-1 supervisorctl status
docker exec website2025-website2025-1 php artisan queue:status

# Logs relevantes
docker exec website2025-website2025-1 tail -50 /var/log/supervisor/laravel-worker.log
docker exec website2025-website2025-1 tail -50 /var/www/html/storage/logs/laravel.log
```

---

## 📚 Referencias

-   [Laravel Queue Documentation](https://laravel.com/docs/10.x/queues)
-   [Supervisor Documentation](http://supervisord.org/)
-   [Docker Compose Documentation](https://docs.docker.com/compose/)
-   [Brevo SMTP Documentation](https://developers.brevo.com/docs/send-emails-with-smtp)

---

**Última actualización**: Enero 2025  
**Versión**: 1.0  
**Autor**: Equipo de Desarrollo
