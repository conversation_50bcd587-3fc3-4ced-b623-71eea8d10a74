# Documentación del Sistema

## 📁 Estructura de Documentación

### 🔧 DevOps y Operaciones

#### [SISTEMA-COLAS-DEVOPS.md](./SISTEMA-COLAS-DEVOPS.md)

**Documentación completa del sistema de colas para DevOps**

-   **Audiencia**: Equipo de DevOps, SRE, Administradores de Sistema
-   **Contenido**: Arquitectura, configuración, troubleshooting, procedimientos de emergencia
-   **Uso**: Consulta completa para operación y mantenimiento del sistema

#### [CHECKLIST-COLAS-DEVOPS.md](./CHECKLIST-COLAS-DEVOPS.md)

**Checklist rápido para verificaciones diarias y semanales**

-   **Audiencia**: Equipo de DevOps
-   **Contenido**: Listas de verificación, procedimientos de emergencia, rutinas de mantenimiento
-   **Uso**: Verificaciones rutinarias y resolución rápida de problemas

### 🛠️ Scripts y Herramientas

#### Scripts de Monitoreo

-   **`./docker/monitor-queues.sh`** - Monitoreo completo del sistema de colas
-   **`./docker/health-check.sh`** - Health check automatizado con alertas
-   **`./deploy-queue-fix.sh`** - Script de despliegue automatizado

#### Comandos Artisan

-   **`php artisan queue:status`** - Estado actual de las colas
-   **`php artisan test:user-registration`** - Prueba del flujo de registro completo

### 📋 Guías de Uso Rápido

#### Para DevOps - Verificación Diaria (2 minutos)

```bash
# Verificación rápida
./docker/health-check.sh

# Si hay problemas, diagnóstico completo
./docker/monitor-queues.sh
```

#### Para Desarrolladores - Testing

```bash
# Probar envío de emails
php artisan test:user-registration <EMAIL>

# Verificar estado
php artisan queue:status
```

#### Para Emergencias

```bash
# Reiniciar workers
docker exec website2025-website2025-1 supervisorctl restart laravel-worker:*

# Procesar cola manualmente
docker exec website2025-website2025-1 php artisan queue:work --once
```

### 📞 Escalación de Problemas

#### Nivel 1: Auto-resolución

-   Usar scripts de monitoreo y health check
-   Aplicar procedimientos del checklist
-   Reiniciar workers si es necesario

#### Nivel 2: Intervención DevOps

-   Revisar logs detallados
-   Aplicar procedimientos de emergencia
-   Coordinar con equipo de desarrollo si es necesario

#### Nivel 3: Escalación a Desarrollo

-   Problemas de configuración
-   Errores en el código
-   Cambios en la arquitectura

### 🔍 Diagnóstico de Problemas Comunes

| Problema            | Comando de Diagnóstico       | Solución Rápida                          |
| ------------------- | ---------------------------- | ---------------------------------------- |
| Emails no se envían | `php artisan queue:status`   | `supervisorctl restart laravel-worker:*` |
| Workers caídos      | `supervisorctl status`       | `supervisorctl restart laravel-worker:*` |
| Jobs atascados      | `./docker/monitor-queues.sh` | `php artisan queue:work --once`          |
| Alta memoria        | `docker stats`               | Reiniciar workers                        |

### 📊 Métricas y Alertas

#### Métricas Críticas

-   **Jobs pendientes**: < 50 (normal), > 100 (crítico)
-   **Jobs fallidos**: < 5 (normal), > 10 (crítico)
-   **Workers activos**: 2 (esperado)
-   **Memoria por worker**: < 400MB (normal), > 800MB (crítico)

#### Configuración de Alertas

```bash
# Health check con alertas por email
./docker/health-check.sh --alert --email <EMAIL>

# Agregar a crontab para monitoreo automático
*/5 * * * * /path/to/docker/health-check.sh --alert --email <EMAIL>
```

### 🔄 Rutinas de Mantenimiento

#### Diario

-   [ ] Ejecutar health check
-   [ ] Revisar alertas
-   [ ] Verificar logs de errores

#### Semanal

-   [ ] Ejecutar test de funcionalidad completo
-   [ ] Revisar métricas de rendimiento
-   [ ] Verificar configuración

#### Mensual

-   [ ] Limpiar logs antiguos
-   [ ] Revisar y actualizar documentación
-   [ ] Análisis de tendencias de uso

### 📚 Documentación Adicional

#### Archivos de Referencia

-   **`testing/modelo-*.md`** - Documentación de modelos de datos
-   **`GD_JPEG_SUPPORT.md`** - Configuración de soporte de imágenes
-   **`MeteorSyncService_Ejemplo.md`** - Integración con servicios externos

#### Enlaces Útiles

-   [Laravel Queue Documentation](https://laravel.com/docs/10.x/queues)
-   [Supervisor Documentation](http://supervisord.org/)
-   [Docker Compose Documentation](https://docs.docker.com/compose/)

### 🚀 Próximos Pasos

#### Mejoras Planificadas

-   [ ] Implementar métricas en Prometheus/Grafana
-   [ ] Agregar alertas en Slack/Teams
-   [ ] Configurar backup automático de logs
-   [ ] Implementar auto-scaling de workers

#### Consideraciones

-   Evaluar migración a Redis para mejor rendimiento
-   Implementar circuit breaker para servicios externos
-   Agregar retry exponencial para jobs fallidos

---

**Última actualización**: Enero 2025  
**Mantenido por**: Equipo de Desarrollo  
**Contacto**: <EMAIL>
