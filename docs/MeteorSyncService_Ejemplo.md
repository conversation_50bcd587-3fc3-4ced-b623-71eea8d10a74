# Servicio de Sincronización con Meteor

## Descripción

El `MeteorSyncService` permite sincronizar automáticamente los datos de usuarios entre el sistema Laravel y la plataforma Meteor, manteniendo consistencia en ambos sistemas.

## Configuración

### 1. Variables de Entorno

```env
METEOR_API_URL=https://ws-si.mulb.in/api/
METEOR_API_KEY=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

### 2. Campo `meteor_id` en SIConfig

El sistema utiliza el campo `meteor_id` en la tabla `config` para identificar usuarios en Meteor:

```sql
ALTER TABLE config ADD COLUMN meteor_id VARCHAR(255) NULL;
```

## Casos de Uso

### 1. Sincronización Automática de Avatar

```php
use App\Livewire\Traits\SyncWithMeteorTrait;

class ProfileComponent extends Component
{
    use SyncWithMeteorTrait;

    public function updateAvatar(): void
    {
        $user = Auth::user();

        // Guardar avatar localmente
        $avatarPath = $this->avatar->store('avatars', 'public');
        $user->avatar = Storage::url($avatarPath);
        $user->save();

        // Sincronizar automáticamente con Meteor
        $this->afterAvatarUpdate($user, $user->avatar);
    }
}
```

### 2. Verificar si Usuario Existe en Meteor

```php
use App\Services\MeteorSyncService;

$meteorSync = app(MeteorSyncService::class);
$user = Auth::user();

if ($meteorSync->usuarioExisteEnMeteor($user)) {
    echo "✅ Usuario existe en Meteor";
} else {
    echo "❌ Usuario NO existe en Meteor";
}
```

### 3. Crear Usuario en Meteor

```php
use App\Services\MeteorSyncService;

$meteorSync = app(MeteorSyncService::class);
$user = Auth::user();

$resultado = $meteorSync->crearUsuarioEnMeteor(
    $user,
    'password123',
    [
        'contratoId' => $user->siConfig->contrato,
        'specialties' => ['residential', 'commercial'],
        'license' => 'LIC-2024-001'
    ]
);

if ($resultado['success']) {
    echo "Usuario creado con ID: " . $resultado['meteor_id'];
} else {
    echo "Error: " . $resultado['error'];
}
```

### 4. Sincronización Selectiva de Campos

```php
use App\Services\MeteorSyncService;

$meteorSync = app(MeteorSyncService::class);
$user = Auth::user();

// Sincronizar solo campos específicos
$camposActualizados = [
    'nombre' => 'Juan Carlos',
    'empresa' => 'Nueva Inmobiliaria S.A.',
    'telefono' => '+52 33 1234 5678'
];

$meteorSync->sincronizarUsuario($user, $camposActualizados);
```

### 5. Verificar Username Disponible

```php
use App\Services\MeteorSyncService;

$meteorSync = app(MeteorSyncService::class);

$resultado = $meteorSync->verificarUsername('nuevo_corretor_2024');

if ($resultado['success']) {
    $data = $resultado['data'];

    if ($data['exists']) {
        echo "❌ Username '{$data['username']}' ya está en uso";
        echo "Usuario: {$data['user']['firstName']} {$data['user']['lastName']}";
    } else {
        echo "✅ Username '{$data['username']}' disponible";
    }
}
```

## Uso en Componentes Livewire

### Con Trait SyncWithMeteorTrait

```php
<?php

use App\Livewire\Traits\SyncWithMeteorTrait;
use Livewire\Volt\Component;

new class extends Component {
    use SyncWithMeteorTrait;

    public function updateProfile(): void
    {
        $user = Auth::user();

        // Actualizar datos localmente
        $user->update([
            'nombre' => $this->nombre,
            'empresa' => $this->empresa,
            'telefono' => $this->telefono,
        ]);

        // Sincronizar automáticamente con Meteor
        $this->afterProfileUpdate($user, ['nombre', 'empresa', 'telefono']);
    }

    public function updateAvatar(): void
    {
        $user = Auth::user();

        // Guardar avatar
        $avatarPath = $this->avatar->store('avatars', 'public');
        $avatarUrl = Storage::url($avatarPath);
        $user->avatar = $avatarUrl;
        $user->save();

        // Sincronizar específicamente el avatar
        $this->afterAvatarUpdate($user, $avatarUrl);
    }
};
```

## Flujo de Trabajo

### 1. Cuando se crea un usuario:

```php
// En CreateContratoTrait.php
$resultado = $this->crearUsuarioEnMeteor($contrato, $user, $activationData);

if ($resultado['success']) {
    // El meteor_id se guarda automáticamente en SIConfig
    Log::info('Usuario creado en Meteor', [
        'meteor_id' => $resultado['meteor_id']
    ]);
}
```

### 2. Cuando se actualiza un perfil:

```php
// El trait detecta cambios automáticamente
$this->autoSyncWithMeteor(['avatar', 'empresa', 'telefono']);
```

### 3. Cuando se cambia avatar específicamente:

```php
// Sincronización específica para avatar
$this->sincronizarAvatarConMeteor($user, $newAvatarUrl);
```

## Logs y Monitoreo

El servicio registra automáticamente todas las operaciones:

```bash
# Ver logs de sincronización
tail -f storage/logs/laravel.log | grep MeteorSync

# Ejemplos de logs:
[2024-01-01 10:30:00] INFO MeteorSync: Usuario encontrado en Meteor {"user_id":123,"meteor_id":"abc456"}
[2024-01-01 10:30:05] INFO MeteorSync: Sincronizando avatar específicamente {"user_id":123,"avatar_url":"https://..."}
[2024-01-01 10:30:10] INFO MeteorSync: Usuario sincronizado exitosamente {"user_id":123,"meteor_id":"abc456"}
```

## Manejo de Errores

El servicio está diseñado para no interrumpir el flujo principal:

```php
try {
    // Operación crítica (guardar en Laravel)
    $user->save();

    // Sincronización con Meteor (no crítica)
    $this->afterProfileUpdate($user, $camposActualizados);

} catch (Exception $e) {
    // La sincronización nunca rompe el flujo principal
    Log::error('Error en sincronización pero operación principal exitosa');
}
```

## Estructura de Datos

### Usuario en Laravel → Meteor

```php
// Laravel User
$user = [
    'nombre' => 'Juan',
    'apellidos' => 'Pérez',
    'empresa' => 'Inmobiliaria ABC',
    'telefono' => '+52 33 1234 5678',
    'estado' => 'Jalisco',
    'ciudad' => 'Guadalajara',
    'avatar' => 'https://cdn.mulb.in/avatars/juan.jpg'
];

// Se convierte a estructura Meteor
$meteorUser = [
    'profile' => [
        'firstName' => 'Juan',
        'lastName' => 'Pérez',
        'company' => 'Inmobiliaria ABC',
        'phone' => '+52 33 1234 5678',
        'location' => 'Jalisco',
        'city' => 'Guadalajara',
        'avatar' => 'https://cdn.mulb.in/avatars/juan.jpg'
    ]
];
```

## Ventajas

✅ **Sincronización Automática**: Los cambios se propagan automáticamente  
✅ **No Invasivo**: No interrumpe el flujo principal del sistema  
✅ **Logs Detallados**: Trazabilidad completa de todas las operaciones  
✅ **Manejo de Errores**: Resistente a fallos de red o API  
✅ **Selectivo**: Solo sincroniza campos específicos cuando es necesario  
✅ **Verificación**: Confirma existencia antes de intentar actualizaciones

## Casos Especiales

### Usuario sin meteor_id

```php
$user = Auth::user();

if (!$this->usuarioExisteEnMeteor($user)) {
    // Crear usuario en Meteor primero
    $resultado = $this->crearUsuarioEnMeteor($user, $password);

    if ($resultado['success']) {
        // Ahora ya se puede sincronizar
        $this->sincronizarConMeteor($user, $campos);
    }
}
```

### Avatar de URL Externa

```php
// Si el avatar viene de URL externa (Google, Facebook)
$user->avatar = 'https://lh3.googleusercontent.com/a/avatar.jpg';
$user->save();

// Sincronizar con URL externa
$this->afterAvatarUpdate($user, $user->avatar);
```

### Sincronización Manual

```php
// Ejecutar sincronización manual desde comando
php artisan tinker

$meteorSync = app(\App\Services\MeteorSyncService::class);
$user = \App\Models\User::find(123);

$meteorSync->sincronizarUsuario($user);
```

Este servicio garantiza que los usuarios mantengan consistencia entre el sistema Laravel y la red social inmobiliaria en Meteor.
