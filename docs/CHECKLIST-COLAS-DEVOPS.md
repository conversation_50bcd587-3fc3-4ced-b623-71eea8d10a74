# Checklist Sistema de Colas - DevOps

## ✅ Verificación Diaria (5 minutos)

### 1. Estado General

```bash
# Ejecutar monitoreo completo
./docker/monitor-queues.sh
```

**Verificar**:

-   [ ] Contenedor está corriendo
-   [ ] Workers están en estado `RUNNING`
-   [ ] Jobs pendientes < 50
-   [ ] Jobs fallidos < 5
-   [ ] No hay jobs atascados

### 2. Salud del Sistema

```bash
# Estado rápido
docker exec website2025-website2025-1 php artisan queue:status
```

**Esperado**:

```
📋 Jobs pendientes: 0
✅ Jobs fallidos: 0
```

---

## 🔧 Verificación Semanal (15 minutos)

### 1. Logs de Workers

```bash
# Verificar logs recientes
docker exec website2025-website2025-1 tail -50 /var/log/supervisor/laravel-worker.log
```

**Buscar**:

-   [ ] No hay errores de memoria
-   [ ] No hay errores de conexión DB
-   [ ] No hay errores de SMTP

### 2. Test de Funcionalidad

```bash
# Probar envío de email
docker exec website2025-website2025-1 php artisan test:user-registration <EMAIL>
```

**Verificar**:

-   [ ] Email se encola correctamente
-   [ ] Email se procesa automáticamente
-   [ ] No hay errores en logs

### 3. Configuración

```bash
# Verificar configuración
docker exec website2025-website2025-1 php artisan config:show queue
docker exec website2025-website2025-1 php artisan config:show mail
```

**Verificar**:

-   [ ] QUEUE_CONNECTION = database
-   [ ] MAIL_MAILER = smtp
-   [ ] Credenciales SMTP correctas

---

## 🚨 Checklist de Emergencia

### Problema: Emails No Se Envían

**Pasos**:

1. [ ] Verificar workers: `supervisorctl status laravel-worker:*`
2. [ ] Revisar cola: `php artisan queue:status`
3. [ ] Verificar logs: `tail -f /var/log/supervisor/laravel-worker.log`
4. [ ] Reiniciar workers: `supervisorctl restart laravel-worker:*`
5. [ ] Probar envío: `php artisan test:user-registration <EMAIL>`

### Problema: Workers Caídos

**Pasos**:

1. [ ] Verificar estado: `supervisorctl status`
2. [ ] Reiniciar: `supervisorctl restart laravel-worker:*`
3. [ ] Si falla, reiniciar supervisor: `supervisorctl restart all`
4. [ ] Si falla, reiniciar contenedor: `docker-compose restart website2025`
5. [ ] Verificar recuperación: `./docker/monitor-queues.sh`

### Problema: Jobs Atascados

**Pasos**:

1. [ ] Ver jobs fallidos: `php artisan queue:failed`
2. [ ] Procesar manualmente: `php artisan queue:work --once`
3. [ ] Reintentar fallidos: `php artisan queue:retry all`
4. [ ] Limpiar si es necesario: `php artisan queue:flush`
5. [ ] Verificar estado: `php artisan queue:status`

---

## 📋 Checklist Post-Despliegue

### Después de Cada Despliegue

**Verificar**:

1. [ ] Contenedor inició correctamente
2. [ ] Supervisor está corriendo
3. [ ] Workers están activos
4. [ ] Configuración es correcta
5. [ ] Test de email funciona

**Comandos**:

```bash
# 1. Estado del contenedor
docker ps | grep website2025

# 2. Estado de supervisor
docker exec website2025-website2025-1 supervisorctl status

# 3. Workers activos
docker exec website2025-website2025-1 supervisorctl status laravel-worker:*

# 4. Configuración
docker exec website2025-website2025-1 php artisan config:show queue

# 5. Test de email
docker exec website2025-website2025-1 php artisan test:user-registration <EMAIL>
```

---

## 📊 Métricas a Monitorear

### Alertas Críticas

-   [ ] Workers caídos por más de 5 minutos
-   [ ] Más de 100 jobs en cola
-   [ ] Más de 10 jobs fallidos por hora
-   [ ] Memoria de workers > 400MB

### Métricas Semanales

-   [ ] Promedio de jobs procesados por día
-   [ ] Tiempo promedio de procesamiento
-   [ ] Tasa de fallos de jobs
-   [ ] Uso de memoria de workers

---

## 🔄 Rutinas de Mantenimiento

### Diario

-   [ ] Ejecutar `./docker/monitor-queues.sh`
-   [ ] Revisar alertas

### Semanal

-   [ ] Revisar logs de workers
-   [ ] Ejecutar test de funcionalidad
-   [ ] Verificar configuración

### Mensual

-   [ ] Limpiar logs antiguos
-   [ ] Revisar métricas de rendimiento
-   [ ] Actualizar documentación si es necesario

---

## 📞 Contactos de Emergencia

### Escalación

1. **DevOps Lead**: [contacto]
2. **Equipo Desarrollo**: [contacto]
3. **CTO**: [contacto]

### Información para Reportes

```bash
# Recopilar información del sistema
docker exec website2025-website2025-1 php artisan --version
docker exec website2025-website2025-1 supervisorctl status
docker exec website2025-website2025-1 php artisan queue:status
docker exec website2025-website2025-1 tail -50 /var/log/supervisor/laravel-worker.log
```

---

## 📝 Notas Importantes

-   **Nunca** detener todos los workers en producción sin coordinación
-   **Siempre** verificar el estado después de cualquier cambio
-   **Reportar** cualquier anomalía inmediatamente
-   **Documentar** cualquier procedimiento nuevo o cambio

---

**Última actualización**: Enero 2025  
**Versión**: 1.0
