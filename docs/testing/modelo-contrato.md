# Documentación del Modelo Contrato

## Campo `pabusqueda`

El modelo `Contrato` incluye un campo especial llamado `pabusqueda` que se utiliza para facilitar las búsquedas de texto libre sobre los datos de los contratos. Este campo concatena automáticamente la información más relevante del contrato para generar un índice de búsqueda eficiente.

### Formato del campo `pabusqueda`

El campo `pabusqueda` sigue el siguiente formato:

```
contrato {numero} {usuario} {servicio} {dominio}
```

Donde:

-   `numero` - Número único del contrato
-   `usuario` - Nombre de usuario del cliente asociado al contrato
-   `servicio` - Tipo de servicio contratado
-   `dominio` - Dominio web asociado al contrato

### Funcionamiento

El campo `pabusqueda` se actualiza automáticamente cada vez que se crea o modifica un contrato, gracias al uso del evento `saving` en el método `boot()` del modelo `Contrato`. No es necesario actualizar manualmente este campo; el sistema se encarga de mantenerlo sincronizado con los datos del contrato.

```php
protected static function boot()
{
    parent::boot();

    // Antes de guardar, actualizar el campo pabusqueda
    static::saving(function ($contrato) {
        $contrato->updatePaBusqueda();
    });
}
```

El método `updatePaBusqueda()` concatena los campos relevantes en el formato especificado:

```php
protected function updatePaBusqueda()
{
    $this->pabusqueda = "contrato {$this->numero} {$this->usuario} {$this->servicio} {$this->dominio}";
}
```

## Pruebas Unitarias

Se han implementado pruebas unitarias para verificar el correcto funcionamiento del campo `pabusqueda`. Estas pruebas se encuentran en el archivo `tests/Unit/Models/ContratoTest.php`.

### Ejecutar las pruebas

Para ejecutar las pruebas específicas del modelo Contrato:

```bash
php artisan test tests/Unit/Models/ContratoTest.php
```

Para ejecutar una prueba específica:

```bash
php artisan test tests/Unit/Models/ContratoTest.php --filter=test_pabusqueda_is_updated_when_creating_contrato_object
```

### Descripción de las pruebas

1. **test_pabusqueda_is_updated_when_creating_contrato_object**: Verifica que al crear un nuevo contrato, el campo `pabusqueda` incluya correctamente todos los campos relevantes en el formato esperado.

2. **test_pabusqueda_is_updated_when_updating_contrato_object**: Verifica que al actualizar los datos de un contrato existente, el campo `pabusqueda` se actualice para reflejar los cambios.

### Estructura de las pruebas

Las pruebas utilizan Reflection para acceder al método protegido `updatePaBusqueda()` y verificar su comportamiento:

```php
// Llamar manualmente al método updatePaBusqueda
$reflectionClass = new \ReflectionClass(Contrato::class);
$method = $reflectionClass->getMethod('updatePaBusqueda');
$method->setAccessible(true);
$method->invoke($contrato);
```

## Uso en búsquedas

El campo `pabusqueda` facilita la implementación de búsquedas eficientes en la tabla de contratos. Para utilizarlo en consultas:

```php
// Ejemplo de búsqueda usando el campo pabusqueda
$query = 'dominio_test';
$contratos = Contrato::where('pabusqueda', 'like', "%{$query}%")->get();
```

Esta consulta devolverá todos los contratos cuyo campo `pabusqueda` contenga el término buscado, permitiendo así encontrar contratos por número, usuario asociado, servicio o dominio.
