# Documentación del Modelo User

## Campo `pabusqueda`

El modelo `User` incluye un campo especial llamado `pabusqueda` que se utiliza para facilitar las búsquedas de texto libre sobre los datos de los clientes. Este campo concatena automáticamente la información más relevante del usuario para generar un índice de búsqueda eficiente.

### Campos incluidos en `pabusqueda`

El campo `pabusqueda` concatena los siguientes datos del usuario:

-   `usuario` - Nombre de usuario
-   `logmail` - Email de acceso
-   `nombre` - Nombre del cliente
-   `apellidos` - Apellidos del cliente
-   `empresa` - Empresa a la que pertenece
-   `calle_numero` - Dirección (calle y número)
-   `colonia` - Colonia o barrio
-   `codigo_postal` - Código postal
-   `ciudad` - Ciudad
-   `estado` - Estado o provincia
-   `pais` - País
-   `telefono` - Teléfono fijo
-   `celular` - Teléfono móvil
-   `email` - Correo electrónico principal
-   `email_sec` - Correo electrónico secundario
-   `sitio_web` - Sitio web
-   `fact_nombre` - Nombre para facturación
-   `fact_domicilio` - Domicilio fiscal
-   `fact_rfc` - RFC para facturación

### Funcionamiento

El campo `pabusqueda` se actualiza automáticamente cada vez que se crea o modifica un usuario, gracias al uso del evento `saving` en el método `boot()` del modelo `User`. No es necesario actualizar manualmente este campo; el sistema se encarga de mantenerlo sincronizado con los datos del usuario.

```php
protected static function boot()
{
    parent::boot();

    // Antes de guardar, actualizar el campo pabusqueda
    static::saving(function ($user) {
        $user->updatePaBusqueda();
    });
}
```

## Pruebas Unitarias

Se han implementado pruebas unitarias para verificar el correcto funcionamiento del campo `pabusqueda`. Estas pruebas se encuentran en el archivo `tests/Unit/Models/UserTest.php`.

### Ejecutar las pruebas

Para ejecutar las pruebas específicas del modelo User:

```bash
php artisan test tests/Unit/Models/UserTest.php
```

Para ejecutar una prueba específica:

```bash
php artisan test tests/Unit/Models/UserTest.php --filter=test_pabusqueda_is_updated_when_creating_user_object
```

### Descripción de las pruebas

1. **test_pabusqueda_is_updated_when_creating_user_object**: Verifica que al crear un nuevo usuario, el campo `pabusqueda` incluya correctamente todos los campos relevantes.

2. **test_pabusqueda_is_updated_when_updating_user_object**: Verifica que al actualizar los datos de un usuario existente, el campo `pabusqueda` se actualice para reflejar los cambios.

### Estructura de las pruebas

Las pruebas utilizan Reflection para acceder al método protegido `updatePaBusqueda()` y verificar su comportamiento:

```php
// Llamar manualmente al método updatePaBusqueda
$reflectionClass = new \ReflectionClass(User::class);
$method = $reflectionClass->getMethod('updatePaBusqueda');
$method->setAccessible(true);
$method->invoke($user);
```

## Uso en búsquedas

El campo `pabusqueda` facilita la implementación de búsquedas eficientes en la tabla de clientes. Para utilizarlo en consultas:

```php
// Ejemplo de búsqueda usando el campo pabusqueda
$query = 'termino de búsqueda';
$usuarios = User::where('pabusqueda', 'like', "%{$query}%")->get();
```

Esta consulta devolverá todos los usuarios cuyo campo `pabusqueda` contenga el término buscado, permitiendo así encontrar usuarios por cualquiera de los campos concatenados (nombre, email, dirección, etc.).
