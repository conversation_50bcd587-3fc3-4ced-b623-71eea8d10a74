# Documentación del Modelo Cobro

## Campo `pabusqueda`

El modelo `Co<PERSON>` incluye un campo especial llamado `pabusqueda` que se utiliza para facilitar las búsquedas de texto libre sobre los datos de los cobros. Este campo concatena automáticamente la información más relevante del cobro para generar un índice de búsqueda eficiente.

### Formato del campo `pabusqueda`

El campo `pabusqueda` sigue el siguiente formato:

```
cobro {numero} {contrato} {usuario} {desde(formato: DD-MMM-YYYY)} {hasta(formato: DD-MMM-YYYY)} {descripcion} {dominio} {precio} {cantidad_pagada}
```

Donde:

-   `numero` - Número único del cobro
-   `contrato` - Número del contrato asociado al cobro (si existe)
-   `usuario` - Nombre de usuario del cliente
-   `desde` - Fecha de inicio del período de cobro en formato "07-abr-2025"
-   `hasta` - Fecha de fin del período de cobro en formato "07-may-2025"
-   `descripcion` - Descripción del cobro
-   `dominio` - Dominio web asociado al cobro
-   `precio` - Precio unitario del cobro
-   `cantidad_pagada` - Cantidad de unidades ya pagadas

### Funcionamiento

El campo `pabusqueda` se actualiza automáticamente cada vez que se crea o modifica un cobro, gracias al uso del evento `saving` en el método `boot()` del modelo `Cobro`. No es necesario actualizar manualmente este campo; el sistema se encarga de mantenerlo sincronizado con los datos del cobro.

```php
protected static function boot()
{
    parent::boot();

    // Antes de guardar, actualizar el campo pabusqueda
    static::saving(function ($cobro) {
        $cobro->updatePaBusqueda();
    });
}
```

El método `updatePaBusqueda()` concatena los campos relevantes en el formato especificado, incluyendo la conversión de fechas al formato deseado:

```php
protected function updatePaBusqueda()
{
    // Formatear fechas al estilo "07-abr-2025"
    $desdeFormateado = $this->desde ? $this->formatearFecha($this->desde) : '';
    $hastaFormateado = $this->hasta ? $this->formatearFecha($this->hasta) : '';

    $this->pabusqueda = "cobro {$this->numero} {$this->contrato} {$this->usuario} {$desdeFormateado} {$hastaFormateado} {$this->descripcion} {$this->dominio} {$this->precio} {$this->cantidad_pagada}";
}
```

### Formateo de fechas

El modelo `Cobro` incluye un método especial para formatear fechas en el estilo "DD-MMM-YYYY", donde el mes se muestra con sus tres primeras letras en minúsculas (ej: ene, feb, mar, etc.):

```php
protected function formatearFecha($fecha)
{
    if (!$fecha) {
        return '';
    }

    if (!$fecha instanceof Carbon) {
        $fecha = Carbon::parse($fecha);
    }

    $meses = [
        1 => 'ene', 2 => 'feb', 3 => 'mar', 4 => 'abr',
        5 => 'may', 6 => 'jun', 7 => 'jul', 8 => 'ago',
        9 => 'sep', 10 => 'oct', 11 => 'nov', 12 => 'dic'
    ];

    return sprintf(
        '%02d-%s-%d',
        $fecha->day,
        $meses[$fecha->month],
        $fecha->year
    );
}
```

## Pruebas Unitarias

Se han implementado pruebas unitarias para verificar el correcto funcionamiento del campo `pabusqueda`. Estas pruebas se encuentran en el archivo `tests/Unit/Models/CobroTest.php`.

### Ejecutar las pruebas

Para ejecutar las pruebas específicas del modelo Cobro:

```bash
php artisan test tests/Unit/Models/CobroTest.php
```

Para ejecutar una prueba específica:

```bash
php artisan test tests/Unit/Models/CobroTest.php --filter=test_formatearFecha_formats_dates_correctly
```

### Descripción de las pruebas

1. **test_formatearFecha_formats_dates_correctly**: Verifica que el método de formateo de fechas funcione correctamente para diferentes fechas.

2. **test_pabusqueda_format_is_correct**: Verifica que al crear un nuevo cobro, el campo `pabusqueda` incluya correctamente todos los campos relevantes en el formato esperado.

3. **test_pabusqueda_updates_when_values_change**: Verifica que al actualizar los datos de un cobro existente, el campo `pabusqueda` se actualice para reflejar los cambios.

## Uso en búsquedas

El campo `pabusqueda` facilita la implementación de búsquedas eficientes en la tabla de cobros. Para utilizarlo en consultas:

```php
// Ejemplo de búsqueda usando el campo pabusqueda
$query = 'abr-2025';
$cobros = Cobro::where('pabusqueda', 'like', "%{$query}%")->get();
```

Esta consulta devolverá todos los cobros cuyo campo `pabusqueda` contenga el término buscado, permitiendo así encontrar cobros por:

-   Número de cobro o contrato
-   Usuario asociado
-   Fechas de período (en formato "07-abr-2025")
-   Descripción o dominio
-   Precio o cantidad pagada
