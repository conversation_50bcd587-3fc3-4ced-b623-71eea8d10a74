services:
    website2025:
        image: publiweb/php82-nginx-dev:laravel-250628 # Sin cache para desarrollo
        # image: publiweb/website2025-mulbin:2025.7.10.0539 # Con cache para producción
        restart: always
        ports:
            - "8000:80"
        environment:
            # Configuración de la aplicación
            APP_NAME: "Multibolsa Inmobiliaria"
            APP_ENV: development
            APP_DOMAIN: "website2025.cft.mulbin.com"
            APP_URL: "https://website2025.cft.mulbin.com"
            MASTER_WEB_DOMAIN: "web.cft.mulbin.com"
            MASTER_PANEL_DOMAIN: "panel.cft.mulbin.com"
            MULBIN_DOMAIN: "cft.mulbin.com"
            MULBIN_SUPPORT_EMAIL: "<EMAIL>"
            APP_KEY: ${APP_KEY}
            APP_DEBUG: true
            APP_LOCALE: es
            APP_FAKER_LOCALE: es_MX
            FORCE_HTTPS: true
            FORCE_LIGHT_MODE: true
            # BCRYPT_ROUNDS: 12
            # Configuración de base de datos publiweb
            PW_DB_HOST: ${PW_DB_HOST}
            PW_DB_DATABASE: ${PW_DB_DATABASE}
            PW_DB_USERNAME: ${PW_DB_USERNAME}
            PW_DB_PASSWORD: ${PW_DB_PASSWORD}
            # Configuración de base de datos sistemainmobiliario
            SI_DB_HOST: ${SI_DB_HOST}
            SI_DB_DATABASE: ${SI_DB_DATABASE}
            SI_DB_USERNAME: ${SI_DB_USERNAME}
            SI_DB_PASSWORD: ${SI_DB_PASSWORD}
            # Configuración de base de datos por defecto (publiweb)
            DB_CONNECTION: ${DB_CONNECTION}
            DB_HOST: ${DB_HOST}
            DB_PORT: ${DB_PORT}
            DB_DATABASE: ${DB_DATABASE}
            DB_USERNAME: ${DB_USERNAME}
            DB_PASSWORD: ${DB_PASSWORD}
            # Sesiones
            SESSION_DRIVER: file
            SESSION_LIFETIME: 120
            SESSION_ENCRYPT: false
            SESSION_PATH: /
            SESSION_DOMAIN: null
            # Configuración de correo
            MAIL_MAILER: ${MAIL_MAILER}
            MAIL_SCHEME: ${MAIL_SCHEME}
            MAIL_HOST: ${MAIL_HOST}
            MAIL_PORT: ${MAIL_PORT}
            MAIL_USERNAME: ${MAIL_USERNAME}
            MAIL_PASSWORD: ${MAIL_PASSWORD}
            MAIL_ENCRYPTION: ${MAIL_ENCRYPTION}
            MAIL_FROM_NAME: "Multibolsa Inmobiliaria - MULBIN"
            MAIL_FROM_ADDRESS: "<EMAIL>"
            # Configuración de Meteor
            METEOR_API_URL: ${METEOR_API_URL}
            METEOR_API_KEY: ${METEOR_API_KEY}
            # Configuración de reCAPTCHA
            RECAPTCHA_SITE_KEY: ${RECAPTCHA_SITE_KEY}
            RECAPTCHA_SECRET_KEY: ${RECAPTCHA_SECRET_KEY}
            # Configuración de Conekta
            CONEKTA_PUBLIC_KEY: ${CONEKTA_PUBLIC_KEY}
            CONEKTA_PRIVATE_KEY: ${CONEKTA_PRIVATE_KEY}
            # Configuración de Google OAuth
            GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
            GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
            GOOGLE_REDIRECT_URI: ${GOOGLE_REDIRECT_URI}
            # Configuración de Google Maps API Key
            # GOOGLE_MAPS_API_KEY: "AIzaSyDHJ6wYwZCjQx5QaZRIZ_cjYD9NsLGcNX0"
            GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
            # Configuración de Facebook OAuth
            FACEBOOK_CLIENT_ID: ${FACEBOOK_CLIENT_ID}
            FACEBOOK_CLIENT_SECRET: ${FACEBOOK_CLIENT_SECRET}
            FACEBOOK_REDIRECT_URI: ${FACEBOOK_REDIRECT_URI}
            # Configuración de colas
            QUEUE_CONNECTION: database
            QUEUE_FAILED_DRIVER: database-uuids
            # Configuración de hashing para compatibilidad con contraseñas legacy
            # BCRYPT_VERIFY_ALGORITHM: false
        volumes:
            - /Users/<USER>/git.mulb.in/si/website2025:/var/www/html
            - /Users/<USER>/git.mulb.in/si/website2025/docker/nginx/default.conf:/etc/nginx/http.d/default.conf
        depends_on:
            - mariadb

    mariadb:
        image: mariadb:10.6.15
        restart: always
        environment:
            MARIADB_ROOT_PASSWORD: password
            MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: "no"
        ports:
            - "3306:3306"
        volumes:
            - mariadb_data:/var/lib/mysql
            - /Users/<USER>/git.mulb.in/si/website2025/database/setup-local-databases.sql:/docker-entrypoint-initdb.d/setup-local-databases.sql
        command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

    phpmyadmin:
        image: phpmyadmin/phpmyadmin
        restart: always
        depends_on:
            - mariadb
        environment:
            PMA_HOST: mariadb
            PMA_PORT: 3306
            PMA_USER: root
            PMA_PASSWORD: password
            UPLOAD_LIMIT: 50M
        ports:
            - "8081:80"

volumes:
    mariadb_data:
        driver: local
    php_session:
        external: true

networks:
    default:
        external: true
        name: shared-network
