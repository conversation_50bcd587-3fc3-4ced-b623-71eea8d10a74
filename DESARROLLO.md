# Documentación de Desarrollo - Mulbin Sistema Inmobiliario

## 📋 Información General

**Mulbin Sistema Inmobiliario** es una plataforma web Laravel que conecta profesionales inmobiliarios mediante una Multibolsa Inmobiliaria, permitiendo la creación de portales web personalizados y el intercambio de propiedades.

### Tecnologías Principales

-   **Backend**: Laravel 11 (PHP 8.2)
-   **Frontend**: Livewire + Tailwind CSS
-   **Base de Datos**: MariaDB 10.6.15
-   **Contenedores**: Docker + Docker Compose
-   **Proxy Reverso**: Cloudflare Tunnel
-   **Servidor Web**: Nginx + PHP-FPM

---

## 🐳 Arquitectura Docker

El proyecto utiliza múltiples contenedores orquestados con Docker Compose:

### Contenedores Activos

| Servicio      | Puerto | Descripción                  | Imagen                                      |
| ------------- | ------ | ---------------------------- | ------------------------------------------- |
| `website2025` | 8000   | Aplicación principal Laravel | `publiweb/php82-nginx-dev:laravel`          |
| `mariadb`     | 3306   | Base de datos principal      | `mariadb:10.6.15`                           |
| `phpmyadmin`  | 8081   | Administrador de BD          | `phpmyadmin/phpmyadmin`                     |
| `interno`     | 8080   | Sistema interno legacy       | `publiweb/nginx-phpfpm:alpine-p5.6.40-base` |
| `msi-v5`      | 8082   | MSI Versión 5                | `publiweb/phpfpm-alpine:8.2-0.0.nginx-DEV`  |
| `panel4`      | 8020   | Panel Versión 4              | `publiweb/nginx-phpfpm5:20250510-dev`       |

### URLs de Acceso

-   **Aplicación Principal**: https://website2025.cft.mulbin.com (Cloudflare Tunnel)
-   **Desarrollo Local**: http://localhost:8000
-   **phpMyAdmin**: http://localhost:8081
-   **Interno**: http://localhost:8080
-   **MSI v5**: http://localhost:8082
-   **Panel4**: http://localhost:8020

---

## ⚡ Comandos de Desarrollo

### Iniciar/Detener Servicios

```bash
# Iniciar todos los servicios
docker compose up -d

# Iniciar solo la aplicación principal
docker compose up -d website2025

# Detener todos los servicios
docker compose down

# Reiniciar un servicio específico
docker compose restart website2025

# Ver logs en tiempo real
docker compose logs -f website2025
```

### Ejecutar Comandos Laravel

**Patrón General**: `docker compose exec website2025 {comando}`

```bash
# Artisan commands
docker compose exec website2025 php artisan migrate
docker compose exec website2025 php artisan migrate:fresh --seed
docker compose exec website2025 php artisan queue:work
docker compose exec website2025 php artisan config:cache
docker compose exec website2025 php artisan route:cache
docker compose exec website2025 php artisan view:cache

# Composer commands
docker compose exec website2025 composer install
docker compose exec website2025 composer update
docker compose exec website2025 composer require paquete/nombre

# NPM/Node commands (si están disponibles)
docker compose exec website2025 npm install
docker compose exec website2025 npm run build
docker compose exec website2025 npm run dev

# Shell interactivo
docker compose exec website2025 bash
docker compose exec website2025 sh

# Tinker (REPL de Laravel)
docker compose exec website2025 php artisan tinker

# Limpiar cachés
docker compose exec website2025 php artisan config:clear
docker compose exec website2025 php artisan route:clear
docker compose exec website2025 php artisan view:clear
docker compose exec website2025 php artisan cache:clear
```

### Comandos de Base de Datos

```bash
# Conectar a MariaDB
docker compose exec mariadb mysql -u root -p

# Backup de base de datos
docker compose exec mariadb mysqldump -u root -p sistemainmobiliario > backup.sql

# Restaurar backup
docker compose exec -T mariadb mysql -u root -p sistemainmobiliario < backup.sql

# Ver logs de MariaDB
docker compose logs mariadb
```

---

## 🔧 Variables de Entorno

### Ubicación

Las variables de entorno se definen en `docker-compose.yml` en la sección `environment` del servicio `website2025`.

### Variables Críticas

#### Aplicación

```yaml
APP_NAME: "Multibolsa Inmobiliaria"
APP_ENV: development
APP_URL: "https://website2025.cft.mulbin.com"
APP_KEY: "base64:Yww4gHEHQq9bUdOLPqswPxqXrD+wdZ6RzxfcV0FTdU0="
APP_DEBUG: true
APP_LOCALE: es
FORCE_HTTPS: true
FORCE_LIGHT_MODE: true
SESSION_SECURE_COOKIE: true
```

#### Base de Datos

```yaml
# Base de datos principal (Publiweb)
PW_DB_HOST: mariadb
PW_DB_DATABASE: publiweb
PW_DB_USERNAME: root
PW_DB_PASSWORD: password

# Base de datos Sistema Inmobiliario
SI_DB_CONNECTION: sistemainmobiliario
SI_DB_HOST: mariadb
SI_DB_DATABASE: sistemainmobiliario
SI_DB_USERNAME: root
SI_DB_PASSWORD: password
```

#### Servicios Externos

```yaml
# Email (Brevo)
MAIL_HOST: smtp-relay.brevo.com
MAIL_USERNAME: "<EMAIL>"
MAIL_PASSWORD: "xsmtpsib-56281fc4d62a4543971d1c4eab4905517995abf39d8da4056232ac8fee0dbc3f-PCj8TARmwFrW17dD"

# reCAPTCHA
RECAPTCHA_SITE_KEY: "6Lc1sR4rAAAAAKTeVT-VZkCkhglT1n6kpJ4oJsiw"
RECAPTCHA_SECRET_KEY: "6Lc1sR4rAAAAAFcZFQ7WksS5CPll-U-KfOzGLEmz"

# Google OAuth
GOOGLE_CLIENT_ID: "491454724906-6f8fkpoqpboagtqd7futu91r1vclr42v.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET: "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"
GOOGLE_REDIRECT_URI: "https://website2025.cft.mulbin.com/auth/google/callback"

# Facebook OAuth
FACEBOOK_CLIENT_ID: "650491671146325"
FACEBOOK_CLIENT_SECRET: "********************************"
FACEBOOK_REDIRECT_URI: "https://website2025.cft.mulbin.com/auth/facebook/callback"

# Conekta (Pagos)
CONEKTA_PUBLIC_KEY: "key_HK8PyepcfRNxp5WdrzfsWwQ"
CONEKTA_PRIVATE_KEY: "key_neq5cxH5UREuP8Tcg0VB7lZ"
```

### Modificar Variables de Entorno

1. Editar `docker-compose.yml`
2. Reiniciar el contenedor: `docker compose restart website2025`
3. Limpiar cache de configuración: `docker compose exec website2025 php artisan config:cache`

---

## 🌐 Configuración HTTPS y Proxy

### Problema de Mixed Content

El proyecto está configurado para funcionar detrás de Cloudflare Tunnel, lo que requiere configuración especial para detectar HTTPS correctamente.

### Solución Implementada

#### 1. Trusted Proxies en Laravel (`app/Providers/AppServiceProvider.php`)

```php
private function configureTrustedProxies(): void
{
    request()->setTrustedProxies(
        ['*'], // Trust all IPs (Cloudflare puede usar diferentes IPs)
        \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR |
        \Illuminate\Http\Request::HEADER_X_FORWARDED_HOST |
        \Illuminate\Http\Request::HEADER_X_FORWARDED_PORT |
        \Illuminate\Http\Request::HEADER_X_FORWARDED_PROTO |
        \Illuminate\Http\Request::HEADER_X_FORWARDED_AWS_ELB
    );
}
```

#### 2. Configuración Nginx (`docker/nginx/default.conf`)

Headers de proxy configurados para detectar HTTPS:

```nginx
fastcgi_param HTTP_X_FORWARDED_PROTO $http_x_forwarded_proto;
fastcgi_param HTTP_X_FORWARDED_FOR $proxy_add_x_forwarded_for;
fastcgi_param HTTP_X_FORWARDED_HOST $http_x_forwarded_host;
fastcgi_param HTTP_X_FORWARDED_PORT $http_x_forwarded_port;
```

### Verificar Configuración HTTPS

```bash
docker compose exec website2025 php artisan tinker --execute="
echo 'APP_URL: ' . config('app.url') . PHP_EOL;
echo 'Request is secure: ' . (request()->isSecure() ? 'YES' : 'NO') . PHP_EOL;
echo 'Livewire upload URL: ' . route('livewire.upload-file') . PHP_EOL;
"
```

#### 2. Livewire Upload 401 (Unauthorized)

**Síntoma**:

-   `POST https://website2025.cft.mulbin.com/livewire/upload-file 401 (Unauthorized)` al subir archivos
-   `GET https://website2025.cft.mulbin.com/livewire/preview-file/filename.png 401 (Unauthorized)` al mostrar preview

**Causa**: Validación de firma de URL (`hasValidSignature()`) falla cuando Laravel está detrás de proxies HTTPS como Cloudflare Tunnel

**Solución Implementada**:

1. **Controladores personalizados**:
    - `CustomFileUploadController` - Maneja subida de archivos
    - `CustomFilePreviewController` - Maneja preview de archivos temporales
2. **Validación condicional**: En desarrollo, solo verifica presencia de parámetros `expires` y `signature`
3. **Rutas personalizadas**: Reemplazan las rutas por defecto de Livewire

```bash
# Verificar que los controladores personalizados estén activos
docker compose exec website2025 php artisan route:list | grep livewire

# Debería mostrar:
# POST livewire/upload-file › CustomFileUploadController@handle
# GET  livewire/preview-file/{filename} › CustomFilePreviewController@handle
```

**Errores específicos resueltos**:

-   `abort_unless(request()->hasValidSignature(), 401)` en `FileUploadController:24`
-   `abort_unless(request()->hasValidSignature(), 401)` en `FilePreviewController:20`

**Archivos creados/modificados**:

-   `app/Http/Controllers/CustomFileUploadController.php` - Controlador personalizado de upload sin validación estricta de firma
-   `app/Http/Controllers/CustomFilePreviewController.php` - Controlador personalizado de preview sin validación estricta de firma
-   `routes/web.php` - Rutas personalizadas que reemplazan las de Livewire
-   `app/Http/Middleware/ExcludeVerifyCsrfToken.php` - Excluye `livewire/*` de CSRF
-   `bootstrap/app.php` - Reemplaza middleware CSRF por defecto
-   `config/livewire.php` - Middleware temporal sin throttling

---

## 🔍 Debugging y Troubleshooting

### Logs de Aplicación

```bash
# Ver logs de Laravel
docker compose exec website2025 tail -f storage/logs/laravel.log

# Logs del contenedor
docker compose logs -f website2025

# Logs de nginx dentro del contenedor
docker compose exec website2025 tail -f /var/log/nginx/error.log
```

### Problemas Comunes

#### 1. Mixed Content (HTTP/HTTPS)

**Síntoma**: `Mixed Content: The page at 'https://...' was loaded over HTTPS, but requested an insecure XMLHttpRequest endpoint 'http://...'`

**Solución**:

-   Verificar `FORCE_HTTPS: true`
-   Verificar configuración de trusted proxies
-   Reiniciar contenedor: `docker compose restart website2025`

#### 2. Livewire Upload 401 (Unauthorized)

**Síntoma**:

-   `POST https://website2025.cft.mulbin.com/livewire/upload-file 401 (Unauthorized)` al subir archivos
-   `GET https://website2025.cft.mulbin.com/livewire/preview-file/filename.png 401 (Unauthorized)` al mostrar preview

**Causa**: Validación de firma de URL (`hasValidSignature()`) falla cuando Laravel está detrás de proxies HTTPS como Cloudflare Tunnel

**Solución Implementada**:

1. **Controladores personalizados**:
    - `CustomFileUploadController` - Maneja subida de archivos
    - `CustomFilePreviewController` - Maneja preview de archivos temporales
2. **Validación condicional**: En desarrollo, solo verifica presencia de parámetros `expires` y `signature`
3. **Rutas personalizadas**: Reemplazan las rutas por defecto de Livewire

```bash
# Verificar que los controladores personalizados estén activos
docker compose exec website2025 php artisan route:list | grep livewire

# Debería mostrar:
# POST livewire/upload-file › CustomFileUploadController@handle
# GET  livewire/preview-file/{filename} › CustomFilePreviewController@handle
```

**Errores específicos resueltos**:

-   `abort_unless(request()->hasValidSignature(), 401)` en `FileUploadController:24`
-   `abort_unless(request()->hasValidSignature(), 401)` en `FilePreviewController:20`

**Archivos creados/modificados**:

-   `app/Http/Controllers/CustomFileUploadController.php` - Controlador personalizado de upload sin validación estricta de firma
-   `app/Http/Controllers/CustomFilePreviewController.php` - Controlador personalizado de preview sin validación estricta de firma
-   `routes/web.php` - Rutas personalizadas que reemplazan las de Livewire
-   `app/Http/Middleware/ExcludeVerifyCsrfToken.php` - Excluye `livewire/*` de CSRF
-   `bootstrap/app.php` - Reemplaza middleware CSRF por defecto
-   `config/livewire.php` - Middleware temporal sin throttling

#### 3. Base de Datos no Conecta

**Verificar conexión**:

```bash
docker compose exec website2025 php artisan tinker --execute="
try {
    DB::connection()->getPdo();
    echo 'DB Connection: OK' . PHP_EOL;
} catch(Exception \$e) {
    echo 'DB Error: ' . \$e->getMessage() . PHP_EOL;
}
"
```

#### 4. Cache Problems

**Limpiar todas las cachés**:

```bash
docker compose exec website2025 php artisan optimize:clear
# O individualmente:
docker compose exec website2025 php artisan config:clear
docker compose exec website2025 php artisan route:clear
docker compose exec website2025 php artisan view:clear
docker compose exec website2025 php artisan cache:clear
```

#### 5. Permisos de Archivos

```bash
# Dentro del contenedor
docker compose exec website2025 chown -R www-data:www-data storage bootstrap/cache
docker compose exec website2025 chmod -R 755 storage bootstrap/cache
```

---

## 📁 Estructura de Directorios

```
/
├── app/                      # Aplicación Laravel
│   ├── Livewire/            # Componentes Livewire
│   ├── Models/              # Modelos Eloquent
│   ├── Providers/           # Service Providers
│   └── Services/            # Servicios personalizados
├── config/                  # Configuraciones Laravel
├── database/                # Migraciones y seeders
│   └── setup-local-databases.sql
├── docker/                  # Configuración Docker
│   └── nginx/
│       └── default.conf     # Configuración Nginx
├── resources/               # Vistas, CSS, JS
│   ├── views/
│   ├── css/
│   └── js/
├── routes/                  # Definición de rutas
├── storage/                 # Almacenamiento (logs, cache, uploads)
├── docker-compose.yml       # Configuración Docker Compose
└── DESARROLLO.md           # Esta documentación
```

---

## 🛠️ Herramientas de Desarrollo

### Acceso a Bases de Datos

-   **phpMyAdmin**: http://localhost:8081
    -   Usuario: `root`
    -   Contraseña: `password`
    -   Bases de datos: `publiweb`, `sistemainmobiliario`

### IDEs y Editores

-   El proyecto está montado en `/Users/<USER>/git.mulb.in/si/website2025`
-   Usar cualquier IDE (VS Code, PhpStorm, etc.) para editar archivos
-   Los cambios se reflejan inmediatamente en el contenedor

### Debugging con Tinker

```bash
# Explorar modelos
docker compose exec website2025 php artisan tinker --execute="
User::count();
User::first();
"

# Verificar configuraciones
docker compose exec website2025 php artisan tinker --execute="
echo 'Environment: ' . app()->environment() . PHP_EOL;
echo 'Debug mode: ' . (config('app.debug') ? 'ON' : 'OFF') . PHP_EOL;
"
```

---

## 🚀 Flujo de Desarrollo

### 1. Configuración Inicial

```bash
# Clonar repositorio (si aplica)
git clone [repo-url]
cd website2025

# Iniciar servicios
docker compose up -d

# Instalar dependencias (si es necesario)
docker compose exec website2025 composer install

# Ejecutar migraciones
docker compose exec website2025 php artisan migrate
```

### 2. Desarrollo Diario

```bash
# Iniciar día de trabajo
docker compose up -d

# Ver logs si hay problemas
docker compose logs -f website2025

# Ejecutar comandos según necesidad
docker compose exec website2025 php artisan [comando]

# Finalizar día
docker compose down
```

### 3. Deploy/Testing

```bash
# Limpiar cachés
docker compose exec website2025 php artisan optimize:clear

# Caché de producción
docker compose exec website2025 php artisan config:cache
docker compose exec website2025 php artisan route:cache
docker compose exec website2025 php artisan view:cache

# Verificar que todo funciona
docker compose exec website2025 php artisan about
```

---

## 📞 Contacto y Soporte

-   **Desarrollo**: Carlos Mata
-   **Email**: [email de contacto]
-   **Repo**: `/Users/<USER>/git.mulb.in/si/website2025`

---

## 📝 Notas Adicionales

-   **Cloudflare Tunnel** maneja el HTTPS público en `website2025.cft.mulbin.com`
-   **Desarrollo local** disponible en `localhost:8000` (HTTP)
-   **Archivos de configuración críticos**:
    -   `docker-compose.yml` (variables de entorno)
    -   `docker/nginx/default.conf` (configuración web server)
    -   `app/Providers/AppServiceProvider.php` (configuración Laravel)
-   **Bases de datos**: Usar siempre el prefijo del contenedor en host (`mariadb`)
-   **Volúmenes Docker**: Los datos de MariaDB persisten en `mariadb_data`

---

_Última actualización: Junio 2025_
