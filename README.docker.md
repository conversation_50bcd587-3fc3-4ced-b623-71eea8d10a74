# Configuración de Docker para el Proyecto

Este proyecto cuenta con dos configuraciones Docker: una para desarrollo y otra para producción.

## Entorno de Desarrollo

La configuración de desarrollo está diseñada para ofrecer un entorno PHP optimizado para Laravel.

### Características

-   PHP 8.2 con extensiones necesarias para Laravel
-   Nginx como servidor web con configuración optimizada
-   Configuración de PHP optimizada para desarrollo (errores visibles, mayor lí<PERSON> de memoria)
-   Montaje de volúmenes para ver cambios en tiempo real

### Uso en Desarrollo

1. Inicia el entorno de desarrollo:

```bash
docker-compose -f docker-compose.dev.yml up
```

2. Accede a la aplicación en `http://localhost:8000`

### Notas sobre el Frontend

Esta configuración no incluye herramientas de frontend (Node.js, Vite, etc.) ya que se gestionarán en un workload separado.

## Entorno de Producción

La configuración de producción utiliza un enfoque multi-etapa para crear una imagen optimizada y segura.

### Características

-   Construcción multi-etapa para minimizar el tamaño de la imagen
-   Nginx optimizado con gzip, caché y cabeceras de seguridad
-   PHP-FPM con OPcache y configuraciones optimizadas para rendimiento
-   Supervisord para gestionar los procesos

### Configuraciones Optimizadas

-   **PHP**: Memoria limitada, sin errores visibles, OPcache configurado para máximo rendimiento
-   **Nginx**: Compresión gzip, caché para archivos estáticos, cabeceras de seguridad, timeouts optimizados

### Construcción para Producción

1. Construye la imagen de producción:

```bash
docker build -t miproyecto/app:latest -f Dockerfile.production .
```

2. Sube la imagen a Docker Hub:

```bash
docker push miproyecto/app:latest
```

## Despliegue en Rancher

Para desplegar en Rancher, necesitarás:

1. Tener la imagen disponible en Docker Hub o un registro accesible desde Rancher

2. En Rancher, crea un nuevo despliegue con la imagen construida

3. Configura las variables de entorno necesarias:

    - `APP_ENV=production`
    - `APP_DEBUG=false`
    - Configuración de base de datos
    - Otras variables específicas de la aplicación

4. Configura el volumen persistente para `/var/www/html/storage` si es necesario

5. Configura el health check y estrategia de implementación según necesites

## Variables de Entorno

Asegúrate de configurar correctamente las siguientes variables en tu entorno de Rancher:

-   `APP_KEY`: Clave de la aplicación Laravel
-   `APP_URL`: URL pública de la aplicación
-   `DB_HOST`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD`: Configuración de base de datos
-   Otras variables específicas de tu aplicación

## Optimizaciones Avanzadas

Para un entorno de producción a gran escala, considera:

1. Implementar un CDN para archivos estáticos
2. Configurar un sistema de caché como Redis
3. Utilizar un balanceador de carga si necesitas escalado horizontal
