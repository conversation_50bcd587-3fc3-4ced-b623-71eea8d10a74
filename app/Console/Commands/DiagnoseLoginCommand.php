<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class DiagnoseLoginCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diagnose:login {email} {password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnosticar problemas de login con un usuario específico';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');

        $this->info("🔍 DIAGNÓSTICO DE LOGIN");
        $this->info("Email/Usuario: {$email}");
        $this->info("==========================================");

        // 1. Verificar configuración de la aplicación
        $this->info("\n1. CONFIGURACIÓN DE LA APLICACIÓN");
        $this->line("APP_ENV: " . config('app.env'));
        $this->line("APP_DEBUG: " . (config('app.debug') ? 'true' : 'false'));
        $this->line("DB_CONNECTION: " . config('database.default'));
        $this->line("SESSION_DRIVER: " . config('session.driver'));
        $this->line("CACHE_DRIVER: " . config('cache.default'));

        // 2. Verificar conexión a base de datos
        $this->info("\n2. CONEXIÓN A BASE DE DATOS");
        try {
            DB::connection('publiweb')->getPdo();
            $this->line("✅ Conexión a base de datos 'publiweb' exitosa");
        } catch (\Exception $e) {
            $this->error("❌ Error de conexión a base de datos: " . $e->getMessage());
            return Command::FAILURE;
        }

        // 3. Buscar usuario
        $this->info("\n3. BÚSQUEDA DE USUARIO");

        // Buscar por logmail
        $userByLogmail = User::where('logmail', $email)->first();
        if ($userByLogmail) {
            $this->line("✅ Usuario encontrado por logmail");
            $user = $userByLogmail;
        } else {
            $this->line("❌ Usuario NO encontrado por logmail");
        }

        // Buscar por usuario
        $userByUsername = User::where('usuario', $email)->first();
        if ($userByUsername) {
            $this->line("✅ Usuario encontrado por usuario");
            $user = $userByUsername ?? $userByUsername;
        } else {
            $this->line("❌ Usuario NO encontrado por usuario");
        }

        // Buscar por email
        $userByEmail = User::where('email', $email)->first();
        if ($userByEmail) {
            $this->line("✅ Usuario encontrado por email");
            $user = $user ?? $userByEmail;
        } else {
            $this->line("❌ Usuario NO encontrado por email");
        }

        if (!isset($user)) {
            $this->error("❌ Usuario no encontrado en ningún campo");
            return Command::FAILURE;
        }

        // 4. Información del usuario
        $this->info("\n4. INFORMACIÓN DEL USUARIO");
        $this->line("ID: " . $user->id);
        $this->line("Nombre: " . $user->name);
        $this->line("Email: " . $user->email);
        $this->line("Logmail: " . $user->logmail);
        $this->line("Usuario: " . $user->usuario);
        $this->line("Activo: " . $user->activo);
        $this->line("Email verificado: " . ($user->email_verified_at ? 'Sí' : 'No'));
        $this->line("Creado: " . $user->created_at);

        // 5. Análisis de contraseña
        $this->info("\n5. ANÁLISIS DE CONTRASEÑA");
        $this->line("Longitud de hash: " . strlen($user->password));
        $this->line("Formato de hash: " . (str_starts_with($user->password, '$2y$') ? 'Bcrypt' : 'Legacy'));

        if (str_contains($user->password, ':')) {
            $this->line("Formato legacy detectado (salt:hash)");
        }

        // 6. Pruebas de autenticación
        $this->info("\n6. PRUEBAS DE AUTENTICACIÓN");

        // Prueba 1: Auth::attempt con logmail
        $attempt1 = Auth::attempt(['logmail' => $email, 'password' => $password]);
        $this->line("Auth::attempt con logmail: " . ($attempt1 ? '✅ Éxito' : '❌ Fallo'));

        // Prueba 2: Auth::attempt con usuario
        $attempt2 = Auth::attempt(['usuario' => $email, 'password' => $password]);
        $this->line("Auth::attempt con usuario: " . ($attempt2 ? '✅ Éxito' : '❌ Fallo'));

        // Prueba 3: Hash::check
        $hashCheck = Hash::check($password, $user->password);
        $this->line("Hash::check: " . ($hashCheck ? '✅ Éxito' : '❌ Fallo'));

        // Prueba 4: checkLegacyPassword
        $legacyCheck = $user->checkLegacyPassword($password);
        $this->line("checkLegacyPassword: " . ($legacyCheck ? '✅ Éxito' : '❌ Fallo'));

        // 7. Verificar estado del usuario
        $this->info("\n7. VERIFICACIONES ADICIONALES");

        if ($user->activo !== 'Si') {
            $this->error("❌ Usuario no está activo");
        } else {
            $this->line("✅ Usuario está activo");
        }

        // Verificar contratos
        $contratos = $user->contratos()->count();
        $this->line("Contratos: " . $contratos);

        // 8. Información del entorno
        $this->info("\n8. INFORMACIÓN DEL ENTORNO");
        $this->line("PHP Version: " . phpversion());
        $this->line("Laravel Version: " . app()->version());
        $this->line("Bcrypt Rounds: " . config('hashing.bcrypt.rounds', 10));

        // Verificar extensiones PHP
        $this->line("Extensión Hash: " . (extension_loaded('hash') ? '✅' : '❌'));
        $this->line("Extensión OpenSSL: " . (extension_loaded('openssl') ? '✅' : '❌'));

        // 9. Recomendaciones
        $this->info("\n9. RECOMENDACIONES");

        if (!$attempt1 && !$attempt2 && !$hashCheck && $legacyCheck) {
            $this->warn("⚠️  El usuario tiene contraseña legacy que funciona, pero Auth::attempt falla");
            $this->warn("⚠️  Esto puede indicar un problema con la configuración de autenticación");
        }

        if (!$attempt1 && !$attempt2 && !$hashCheck && !$legacyCheck) {
            $this->error("❌ Ningún método de autenticación funciona - contraseña incorrecta");
        }

        if ($user->activo !== 'Si') {
            $this->error("❌ Usuario debe ser activado para poder iniciar sesión");
        }

        // 10. Comandos útiles
        $this->info("\n10. COMANDOS ÚTILES");
        $this->line("• Resetear contraseña: php artisan tinker");
        $this->line("  User::find({$user->id})->update(['password' => Hash::make('nueva_password')]);");
        $this->line("• Activar usuario: php artisan tinker");
        $this->line("  User::find({$user->id})->update(['activo' => 'Si']);");
        $this->line("• Ver logs: tail -f storage/logs/laravel.log");

        return Command::SUCCESS;
    }
}
