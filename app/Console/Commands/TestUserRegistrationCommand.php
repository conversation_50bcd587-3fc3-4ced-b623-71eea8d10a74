<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TestUserRegistrationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:user-registration {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Probar el flujo completo de registro de usuario y verificación de email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? $this->ask('¿Qué email quieres usar para la prueba?');

        if (!$email) {
            $this->error('Email es requerido');
            return Command::FAILURE;
        }

        // Verificar si el usuario ya existe
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            $this->warn("El usuario con email {$email} ya existe.");
            $useExisting = $this->confirm('¿Quieres usar el usuario existente para la prueba?');

            if (!$useExisting) {
                return Command::FAILURE;
            }

            $user = $existingUser;
        } else {
            // Crear usuario de prueba
            $this->info("Creando usuario de prueba con email: {$email}");

            $user = User::create([
                'logmail' => $email,
                'email' => $email,
                'name' => 'Usuario de Prueba',
                'nombre' => 'Usuario',
                'apellidos' => 'de Prueba',
                'usuario' => 'test_' . Str::random(8),
                'password' => Hash::make('password123'),
                'activo' => 'Si',
                'quien_registro' => 'test-command',
            ]);

            $this->info("✅ Usuario creado exitosamente con ID: {$user->id}");
        }

        // Verificar estado antes del envío
        $this->info("\n=== ESTADO ANTES DEL ENVÍO ===");
        $this->line("Email verificado: " . ($user->hasVerifiedEmail() ? '✅ Sí' : '❌ No'));
        $this->line("Email_verified_at: " . ($user->email_verified_at ?? 'NULL'));

        // Verificar estado de las colas antes
        $this->call('queue:status');

        // Enviar email de verificación
        $this->info("\n=== ENVIANDO EMAIL DE VERIFICACIÓN ===");
        $user->sendEmailVerificationNotification();
        $this->info("✅ Email de verificación enviado/encolado");

        // Verificar estado de las colas después
        $this->info("\n=== ESTADO DESPUÉS DEL ENVÍO ===");
        $this->call('queue:status');

        // Información adicional
        $this->info("\n=== INFORMACIÓN DEL USUARIO ===");
        $this->line("ID: {$user->id}");
        $this->line("Email: {$user->email}");
        $this->line("Usuario: {$user->usuario}");
        $this->line("Nombre: {$user->name}");
        $this->line("Activo: {$user->activo}");

        $this->info("\n💡 Para procesar la cola manualmente: php artisan queue:work --once");
        $this->info("💡 Para monitorear: php artisan queue:status");

        return Command::SUCCESS;
    }
}
