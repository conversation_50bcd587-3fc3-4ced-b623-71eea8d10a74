<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class QueueStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mostrar el estado actual de las colas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== ESTADO DE LAS COLAS ===');

        // Contar jobs pendientes
        $pendingJobs = DB::table('jobs')->count();
        $this->line("📋 Jobs pendientes: {$pendingJobs}");

        // Contar jobs fallidos
        $failedJobs = DB::table('failed_jobs')->count();
        if ($failedJobs > 0) {
            $this->error("❌ Jobs fallidos: {$failedJobs}");
        } else {
            $this->line("✅ Jobs fallidos: {$failedJobs}");
        }

        // Mostrar últimos jobs pendientes
        if ($pendingJobs > 0) {
            $this->newLine();
            $this->info('📝 Últimos jobs pendientes:');
            $jobs = DB::table('jobs')
                ->select('queue', 'payload', 'created_at')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            foreach ($jobs as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                $this->line("  • {$jobClass} (Cola: {$job->queue}) - {$job->created_at}");
            }
        }

        // Mostrar últimos jobs fallidos
        if ($failedJobs > 0) {
            $this->newLine();
            $this->error('❌ Últimos jobs fallidos:');
            $failed = DB::table('failed_jobs')
                ->select('queue', 'payload', 'exception', 'failed_at')
                ->orderBy('failed_at', 'desc')
                ->limit(3)
                ->get();

            foreach ($failed as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                $this->line("  • {$jobClass} (Cola: {$job->queue}) - {$job->failed_at}");
                $this->line("    Error: " . substr($job->exception, 0, 100) . "...");
            }
        }

        $this->newLine();
        $this->info('💡 Comandos útiles:');
        $this->line('  • php artisan queue:work - Procesar colas manualmente');
        $this->line('  • php artisan queue:failed - Ver jobs fallidos');
        $this->line('  • php artisan queue:retry all - Reintentar jobs fallidos');
        $this->line('  • php artisan queue:flush - Limpiar jobs fallidos');

        return Command::SUCCESS;
    }
}
