<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Password;
use App\Models\User;

class TestPasswordReset extends Command
{
    protected $signature = 'password:test {email}';
    protected $description = 'Prueba el envío de correo de recuperación de contraseña';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Buscando usuario con email: {$email}");
        
        // Buscar usuario por logmail
        $user = User::where('logmail', $email)->first();
        
        if (!$user) {
            $this->error("No se encontró ningún usuario con el email: {$email}");
            return 1;
        }
        
        $this->info("Usuario encontrado: {$user->name} (ID: {$user->id})");
        $this->info("Email real del usuario: {$user->email}");
        
        $this->info("Enviando correo de recuperación de contraseña...");
        
        try {
            $status = Password::broker('clientes')->sendResetLink(['email' => $user->email]);
            
            if ($status === Password::RESET_LINK_SENT) {
                $this->info("¡Correo enviado correctamente!");
            } else {
                $this->error("Error al enviar el correo: {$status}");
            }
        } catch (\Exception $e) {
            $this->error("Excepción al enviar el correo: " . $e->getMessage());
            $this->line("Traza de la excepción:");
            $this->line($e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
