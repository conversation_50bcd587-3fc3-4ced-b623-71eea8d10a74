<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class TestGuestVerifyEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:guest-verify-email {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a guest verification URL for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // Buscar el usuario por email
        $user = User::where('email', $email)->orWhere('logmail', $email)->first();

        if (!$user) {
            $this->error("No se encontró un usuario con el email: {$email}");
            return 1;
        }

        // Generar la URL de verificación
        $url = route(
            'verification.verify.guest',
            [
                'id' => $user->id,
                'hash' => sha1($user->getEmailForVerification()),
            ]
        );

        $this->info("URL de verificación para {$user->email}:");
        $this->line($url);

        $this->info("\nEstado del usuario:");
        $this->line("- ID: {$user->id}");
        $this->line("- Email: {$user->email}");
        $this->line("- Email verificado: " . ($user->hasVerifiedEmail() ? 'Sí' : 'No'));
        $this->line("- Creado hace: " . $user->created_at->diffForHumans());

        return 0;
    }
}
