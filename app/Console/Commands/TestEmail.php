<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

class TestEmail extends Command
{
    protected $signature = 'email:test {email}';
    protected $description = 'Envía un correo electrónico de prueba';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Enviando correo de prueba a {$email}...");
        
        try {
            Mail::raw('Este es un correo de prueba desde Laravel.', function (Message $message) use ($email) {
                $message->to($email)
                        ->subject('Correo de prueba');
            });
            
            $this->info('¡Correo enviado correctamente!');
        } catch (\Exception $e) {
            $this->error('Error al enviar el correo: ' . $e->getMessage());
        }
    }
}
