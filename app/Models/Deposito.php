<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;

class Deposito extends PWModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'depositos';

    /**
     * La clave primaria asociada con la tabla.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Los atributos que deberían ser mutados a fechas.
     *
     * @var array
     */
    protected $dates = [
        'fecha_deposito',
        'fecha_registro',
    ];

    /**
     * Indica si el modelo debe ser timestamped.
     * La tabla no usa timestamps estándar.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * La conexión que se utilizará para este modelo.
     *
     * @var string
     */
    protected $connection;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_usuario',
        'banco',
        'cantidad',
        'fecha_deposito',
        'fecha_registro',
        'observaciones',
        'verificado',
        'item_paypal',
        'movimiento',
        'user_admin',
        'factura_mbi',
        'api_response'
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id_usuario' => 'integer',
        'cantidad' => 'float',
        'fecha_deposito' => 'datetime',
        'fecha_registro' => 'datetime',
        'verificado' => 'string',
        'item_paypal' => 'integer',
        'factura_mbi' => 'integer',
    ];

    /**
     * Obtiene el usuario (cliente) asociado con este depósito.
     *
     * @return BelongsTo
     */
    public function cliente(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_usuario', 'id');
    }

    /**
     * Obtiene los cobros relacionados con este depósito.
     *
     * @return BelongsToMany
     */
    public function cobros(): BelongsToMany
    {
        return $this->belongsToMany(
            Cobro::class,
            'dep_rel',
            'id_deposito',
            'id_cobro'
        );
    }

    /**
     * Determina si el depósito está verificado.
     *
     * @return bool
     */
    public function estaVerificado(): bool
    {
        return $this->verificado === 'Si';
    }

    /**
     * Obtiene el monto disponible (no asociado a cobros).
     *
     * @return float
     */
    public function montoDisponible(): float
    {
        $totalAsignado = $this->cobros()->sum('precio');
        return $this->cantidad - $totalAsignado;
    }

    /**
     * Scope para filtrar depósitos verificados.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerificados($query)
    {
        return $query->where('verificado', 'Si');
    }

    /**
     * Scope para filtrar depósitos pendientes de verificar.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePendientes($query)
    {
        return $query->where('verificado', 'No');
    }

    /**
     * Scope para filtrar depósitos por banco.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $banco
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePorBanco($query, string $banco)
    {
        return $query->where('banco', $banco);
    }

    /**
     * Scope para filtrar depósitos por rango de fechas.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Carbon|string $desde
     * @param Carbon|string|null $hasta
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePorRangoFechas($query, $desde, $hasta = null)
    {
        $query = $query->where('fecha_deposito', '>=', $desde);

        if ($hasta) {
            $query = $query->where('fecha_deposito', '<=', $hasta);
        }

        return $query;
    }

    /**
     * Scope para filtrar depósitos de PayPal.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePaypal($query)
    {
        return $query->whereNotNull('item_paypal');
    }
}
