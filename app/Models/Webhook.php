<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Webhook extends Model
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'webhooks';

    /**
     * Indica si el modelo debe ser timestamped.
     * La tabla solo tiene created_at, no updated_at.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cliente_ids',
        'cobro_id',
        'origin',
        'type',
        'id_item',
        'data',
        'created_at'
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'data' => 'array', // Convertirá automáticamente JSON a array y viceversa
    ];

    /**
     * Obtiene el cliente asociado con este webhook.
     */
    public function cliente()
    {
        return $this->belongsTo(User::class, 'cliente_ids', 'usuario');
    }

    /**
     * Obtiene el cobro asociado con este webhook.
     */
    public function cobro()
    {
        return $this->belongsTo(Cobro::class, 'cobro_id', 'numero');
    }
}
