<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConektaCustomer extends Model
{
    use HasFactory;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'conekta_id',
        'payment_sources',
        'default_payment_source_id',
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payment_sources' => 'array',
        'user_id' => 'integer',
    ];

    /**
     * Obtiene el usuario asociado al cliente de Conekta.
     * 
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Determina si el cliente tiene métodos de pago guardados.
     *
     * @return bool
     */
    public function hasPaymentSources(): bool
    {
        return !empty($this->payment_sources);
    }
}
