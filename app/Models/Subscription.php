<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'conekta_id',
        'conekta_plan',
        'conekta_customer',
        'status',
        'trial_ends_at',
        'ends_at',
        'billing_period',
        'plan_price',
        'total_price',
        'personalization',
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array
     */
    protected $casts = [
        'trial_ends_at' => 'datetime',
        'ends_at' => 'datetime',
        'personalization' => 'boolean',
    ];

    /**
     * Obtiene el usuario que posee la suscripción.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Determina si la suscripción está activa.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status === 'active' || $this->onTrial();
    }

    /**
     * Determina si la suscripción está en período de prueba.
     *
     * @return bool
     */
    public function onTrial()
    {
        return $this->status === 'in_trial' && $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Determina si la suscripción está cancelada.
     *
     * @return bool
     */
    public function isCancelled()
    {
        return $this->status === 'canceled';
    }

    /**
     * Determina si la suscripción está pausada.
     *
     * @return bool
     */
    public function isPaused()
    {
        return $this->status === 'paused';
    }

    /**
     * Determina si la suscripción está vencida o con pagos pendientes.
     *
     * @return bool
     */
    public function isPastDue()
    {
        return $this->status === 'past_due';
    }
}
