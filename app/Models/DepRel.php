<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DepRel extends PWModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'dep_rel';

    /**
     * Indica si el modelo debe ser timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * La conexión que se utilizará para este modelo.
     *
     * @var string
     */
    protected $connection;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_deposito',
        'id_cobro',
    ];

    /**
     * Obtiene el depósito relacionado.
     *
     * @return BelongsTo
     */
    public function deposito(): BelongsTo
    {
        return $this->belongsTo(Deposito::class, 'id_deposito');
    }

    /**
     * Obtiene el cobro relacionado.
     *
     * @return BelongsTo
     */
    public function cobro(): BelongsTo
    {
        return $this->belongsTo(Cobro::class, 'id_cobro', 'numero');
    }
}
