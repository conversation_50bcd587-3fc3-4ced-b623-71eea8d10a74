<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Inmueble extends SistemaInmobiliarioModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'propiedades';

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // Aquí puedes añadir los campos que quieras que sean asignables masivamente
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // Aquí puedes definir casteos de tipos, por ejemplo:
        // 'fecha_publicacion' => 'datetime',
    ];

    /**
     * Obtiene la configuración asociada al inmueble.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function siconfig()
    {
        return $this->belongsTo(SIConfig::class, 'contrato', 'contrato');
    }
} 
