<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Carbon;

class Cobro extends PWModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'por_cobrar';

    /**
     * La clave primaria asociada con la tabla.
     *
     * @var string
     */
    protected $primaryKey = 'numero';

    /**
     * Los atributos que deberían ser mutados a fechas.
     *
     * @var array
     */
    protected $dates = [
        'fecha',
        'desde',
        'hasta',
        'vencimiento',
        'ultima_notificacion',
        'notificacion_pago',
        'autorizacion_de_pago',
    ];

    /**
     * Indica si el modelo debe ser timestamped.
     * La tabla usa campos personalizados para timestamps.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'contrato',
        'fecha',
        'usuario',
        'servicio',
        'cantidad',
        'descripcion',
        'dominio',
        'adicional',
        'precio',
        'desde',
        'hasta',
        'pagado',
        'factura',
        'notificacion_pago',
        'forma_pago',
        'observaciones_pago',
        'vencimiento',
        'ultima_notificacion',
        'detener',
        'cantidad_pagada',
        'autorizacion_de_pago',
        'creado_por',
        'modificado_por',
        'pabusqueda'
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'fecha' => 'datetime',
        'desde' => 'date',
        'hasta' => 'date',
        'vencimiento' => 'date',
        'ultima_notificacion' => 'date',
        'notificacion_pago' => 'datetime',
        'autorizacion_de_pago' => 'datetime',
        'real_creado' => 'datetime',
        'real_modificado' => 'datetime',
        'cantidad' => 'integer',
        'precio' => 'float',
        'cantidad_pagada' => 'integer',
    ];

    /**
     * Obtiene el contrato asociado con este cobro (si existe).
     */
    public function contrato()
    {
        return $this->belongsTo(Contrato::class, 'contrato', 'numero');
    }

    /**
     * Obtiene el usuario (cliente) asociado con este cobro.
     */
    public function cliente()
    {
        return $this->belongsTo(User::class, 'usuario', 'usuario');
    }

    /**
     * Obtiene el servicio asociado con este cobro.
     */
    public function servicio()
    {
        return $this->belongsTo(Servicio::class, 'servicio', 'servicio');
    }

    /**
     * Determina si el cobro está pagado.
     *
     * @return bool
     */
    public function estaPagado(): bool
    {
        return $this->pagado === 'Si';
    }

    /**
     * Determina si el cobro está detenido.
     *
     * @return bool
     */
    public function estaDetenido(): bool
    {
        return $this->detener === 'Si';
    }

    /**
     * Determina si el cobro está vencido.
     *
     * @return bool
     */
    public function estaVencido(): bool
    {
        return $this->vencimiento !== null && $this->vencimiento < now()->startOfDay();
    }

    /**
     * Calcula los días restantes hasta el vencimiento del cobro.
     *
     * @return int|null Número de días restantes o null si no hay fecha de vencimiento
     */
    public function diasHastaVencimiento(): ?int
    {
        if ($this->vencimiento === null) {
            return null;
        }

        return now()->startOfDay()->diffInDays($this->vencimiento, false);
    }

    /**
     * Calcula el importe total del cobro.
     *
     * @return float
     */
    public function importeTotal(): float
    {
        return $this->precio * $this->cantidad;
    }

    /**
     * Calcula el importe pendiente de pago.
     *
     * @return float
     */
    public function importePendiente(): float
    {
        if ($this->estaPagado()) {
            return 0;
        }

        $cantidadPendiente = $this->cantidad - $this->cantidad_pagada;
        return $this->precio * $cantidadPendiente;
    }

    /**
     * Determina si el cobro es independiente (no está asociado a un contrato).
     *
     * @return bool
     */
    public function esIndependiente(): bool
    {
        return $this->contrato === null;
    }

    /**
     * Determina si el cobro tiene notificación de pago.
     *
     * @return bool
     */
    public function tieneNotificacionPago(): bool
    {
        return $this->notificacion_pago !== null;
    }

    /**
     * Determina si el cobro tiene autorización de pago.
     *
     * @return bool
     */
    public function tieneAutorizacionPago(): bool
    {
        return $this->autorizacion_de_pago !== null;
    }

    /**
     * Obtiene la duración del período de cobro en días.
     *
     * @return int|null
     */
    public function duracionPeriodo(): ?int
    {
        if ($this->desde === null || $this->hasta === null) {
            return null;
        }

        return Carbon::parse($this->desde)->diffInDays(Carbon::parse($this->hasta)) + 1;
    }

    /**
     * Obtiene el creador del cobro.
     */
    public function creador()
    {
        return $this->belongsTo(User::class, 'creado_por', 'usuario');
    }

    /**
     * Obtiene el último modificador del cobro.
     */
    public function modificador()
    {
        return $this->belongsTo(User::class, 'modificado_por', 'usuario');
    }

    /**
     * Scope para filtrar cobros pendientes de pago.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePendientes($query)
    {
        return $query->where('pagado', 'No');
    }

    /**
     * Scope para filtrar cobros pagados.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePagados($query)
    {
        return $query->where('pagado', 'Si');
    }

    /**
     * Scope para filtrar cobros vencidos.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVencidos($query)
    {
        return $query->where('vencimiento', '<', now()->startOfDay())
                     ->where('pagado', 'No');
    }

    /**
     * Scope para filtrar cobros por vencer en los próximos N días.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $dias
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePorVencer($query, int $dias = 7)
    {
        return $query->where('vencimiento', '>=', now()->startOfDay())
                     ->where('vencimiento', '<=', now()->addDays($dias)->endOfDay())
                     ->where('pagado', 'No');
    }

    /**
     * Scope para filtrar cobros independientes (sin contrato asociado).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIndependientes($query)
    {
        return $query->whereNull('contrato');
    }

    /**
     * Scope para filtrar cobros asociados a contratos.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeConContrato($query)
    {
        return $query->whereNotNull('contrato');
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Antes de guardar, actualizar el campo pabusqueda
        static::saving(function ($cobro) {
            $cobro->updatePaBusqueda();
        });
    }

    /**
     * Actualiza el campo pabusqueda con los datos relevantes del cobro
     *
     * @return void
     */
    protected function updatePaBusqueda()
    {
        // Formatear fechas al estilo "07-abr-2025"
        $desdeFormateado = $this->desde ? $this->formatearFecha($this->desde) : '';
        $hastaFormateado = $this->hasta ? $this->formatearFecha($this->hasta) : '';

        $total = $this->precio * $this->cantidad;

        $this->pabusqueda = "cobro {$this->numero} {$this->contrato} {$this->usuario} {$desdeFormateado} {$hastaFormateado} {$this->descripcion} {$this->dominio} {$this->precio} {$total}";

        // // Quito espacios dobles y hago trim
        // $this->pabusqueda = preg_replace('/\s+/', ' ', $this->pabusqueda);
        // $this->pabusqueda = trim($this->pabusqueda);
    }

    /**
     * Formatea una fecha al estilo "07-abr-2025"
     *
     * @param \Carbon\Carbon|string $fecha
     * @return string
     */
    protected function formatearFecha($fecha)
    {
        if (!$fecha) {
            return '';
        }

        if (!$fecha instanceof Carbon) {
            $fecha = Carbon::parse($fecha);
        }

        $meses = [
            1 => 'ene', 2 => 'feb', 3 => 'mar', 4 => 'abr',
            5 => 'may', 6 => 'jun', 7 => 'jul', 8 => 'ago',
            9 => 'sep', 10 => 'oct', 11 => 'nov', 12 => 'dic'
        ];

        return sprintf(
            '%02d-%s-%d',
            $fecha->day,
            $meses[$fecha->month],
            $fecha->year
        );
    }

    /**
     * Obtiene los depósitos asociados con este cobro.
     */
    public function depositos()
    {
        return $this->belongsToMany(
            Deposito::class,
            'dep_rel',
            'id_cobro',
            'id_deposito'
        );
    }
}
