<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Servicio extends PWModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'servicios';

    /**
     * La clave primaria asociada con la tabla.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indica si la clave primaria es un incremento automático.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * El tipo de dato de la clave primaria.
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * Indica si el modelo debe ser timestamped.
     * La tabla ya tiene campos created_at y updated_at.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * La conexión que se utilizará para este modelo.
     *
     * @var string
     */
    protected $connection;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'servicio',
        'descripcion',
        'precio',
        'observaciones',
        'notifica_vencimiento',
        'notifica_cada',
        'cobro_automatico',
        'cobro_mensual',
        'desc_trimestral',
        'desc_semestral',
        'desc_anual',
        'renovacion_cada',
        'inmuebles',
        'publicado',
        'acepta_prorrogas',
        'NotificandoVencidos',
        'QueHacerVencido',
        'tipo'
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'precio' => 'float',
        'notifica_vencimiento' => 'integer',
        'notifica_cada' => 'integer',
        'cobro_automatico' => 'string',
        'cobro_mensual' => 'string',
        'desc_trimestral' => 'float',
        'desc_semestral' => 'float',
        'desc_anual' => 'float',
        'renovacion_cada' => 'integer',
        'inmuebles' => 'integer',
        'acepta_prorrogas' => 'string',
        'NotificandoVencidos' => 'integer',
        'QueHacerVencido' => 'integer'
    ];

    /**
     * Obtiene los contratos asociados con este servicio.
     */
    public function contratos()
    {
        return $this->hasMany(Contrato::class, 'servicio', 'servicio');
    }

    /**
     * Determina si el servicio acepta prorrogas.
     *
     * @return bool
     */
    public function aceptaProrrogas(): bool
    {
        return $this->acepta_prorrogas === 'Si';
    }

    /**
     * Determina si el servicio tiene cobro automático.
     *
     * @return bool
     */
    public function tieneCobroAutomatico(): bool
    {
        return $this->cobro_automatico === 'Si';
    }

    /**
     * Determina si el servicio tiene cobro mensual.
     *
     * @return bool
     */
    public function tieneCobroMensual(): bool
    {
        return $this->cobro_mensual === 'Si';
    }

    /**
     * Calcula el precio con descuento según el período de contratación.
     *
     * @param string $periodo 'trimestral', 'semestral', 'anual'
     * @return float
     */
    public function precioConDescuento(string $periodo): float
    {
        switch ($periodo) {
            case 'trimestral':
                return $this->precio * (1 - ($this->desc_trimestral / 100));
            case 'semestral':
                return $this->precio * (1 - ($this->desc_semestral / 100));
            case 'anual':
                return $this->precio * (1 - ($this->desc_anual / 100));
            default:
                return $this->precio;
        }
    }

    /**
     * Obtiene los cobros asociados con este servicio.
     */
    public function cobros()
    {
        return $this->hasMany(Cobro::class, 'servicio', 'servicio');
    }

    /**
     * Obtiene los cobros pendientes asociados con este servicio.
     */
    public function cobrosPendientes()
    {
        return $this->cobros()->where('pagado', 'No');
    }

    /**
     * Obtiene los cobros pagados asociados con este servicio.
     */
    public function cobrosPagados()
    {
        return $this->cobros()->where('pagado', 'Si');
    }
}
