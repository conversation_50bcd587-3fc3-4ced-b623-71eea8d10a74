<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use SoftDeletes;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'conekta_plan_id',
        'name',
        'amount',
        'currency',
        'interval',
        'frequency',
        'service_id',
        'is_active',
        'description',
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'integer',
        'frequency' => 'integer',
        'service_id' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Obtiene el servicio asociado con este plan.
     *
     * @return BelongsTo
     */
    public function servicio(): BelongsTo
    {
        return $this->belongsTo(Servicio::class, 'service_id');
    }

    /**
     * Obtiene el monto formateado como decimal.
     *
     * @return float
     */
    public function getAmountDecimalAttribute(): float
    {
        return $this->amount / 100;
    }

    /**
     * Obtiene el monto formateado como moneda.
     *
     * @return string
     */
    public function getAmountFormattedAttribute(): string
    {
        return number_format($this->amount_decimal, 2);
    }

    /**
     * Obtiene la información del intervalo de facturación.
     *
     * @return string
     */
    public function getIntervalInfoAttribute(): string
    {
        $intervals = [
            'day' => 'día(s)',
            'week' => 'semana(s)',
            'month' => 'mes(es)',
            'year' => 'año(s)',
        ];

        $intervalText = $intervals[$this->interval] ?? $this->interval;
        return "$this->frequency $intervalText";
    }

    /**
     * Scope para filtrar planes activos.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActivos(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para filtrar planes por moneda.
     *
     * @param Builder $query
     * @param string $currency
     * @return Builder
     */
    public function scopePorMoneda(Builder $query, string $currency): Builder
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope para filtrar planes por intervalo.
     *
     * @param Builder $query
     * @param string $interval
     * @return Builder
     */
    public function scopePorIntervalo(Builder $query, string $interval): Builder
    {
        return $query->where('interval', $interval);
    }

    /**
     * Scope para filtrar planes por servicio.
     *
     * @param Builder $query
     * @param int $serviceId
     * @return Builder
     */
    public function scopePorServicio(Builder $query, int $serviceId): Builder
    {
        return $query->where('service_id', $serviceId);
    }

    /**
     * Determina si el plan está disponible en Conekta.
     *
     * @return bool
     */
    public function disponibleEnConekta(): bool
    {
        return !empty($this->conekta_plan_id);
    }
}
