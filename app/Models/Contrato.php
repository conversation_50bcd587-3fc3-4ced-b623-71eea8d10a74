<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class Contrato extends PWModel
{
    use HasFactory;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'contratos';

    /**
     * La clave primaria asociada con la tabla.
     *
     * @var string
     */
    protected $primaryKey = 'numero';

    /**
     * Indica si el modelo debe ser timestamped.
     * La tabla ya tiene campos created_at y updated_at.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'usuario',
        'status',
        'servicio',
        'dominio',
        's_usuario',
        's_password',
        'observaciones',
        'forma_pago',
        'en_precio',
        'por_adelantado',
        'dias_tregua',
        'cobro_dia',
        'cobro_siguiente',
        'pago_automatizado',
        'cobranza_generada',
        'tipo',
        'fecha',
        'pagado_hasta',
        'prorroga',
        'history',
        'pabusqueda'
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
        'en_precio' => 'float',
        'dias_tregua' => 'integer',
        'cobro_dia' => 'integer',
        'cobro_siguiente' => 'integer',
        'pago_automatizado' => 'boolean',
        'fecha' => 'datetime',
        'pagado_hasta' => 'datetime',
        'prorroga' => 'datetime',
        'cobranza_generada' => 'date',
    ];

    /**
     * Obtiene el usuario (cliente) asociado con este contrato.
     */
    public function cliente()
    {
        return $this->belongsTo(User::class, 'usuario', 'usuario');
    }

    /**
     * Obtiene el servicio asociado con este contrato.
     */
    public function servicioRelacion(): BelongsTo
    {
        return $this->belongsTo(Servicio::class, 'servicio', 'servicio');
    }

    /**
     * Determina si el contrato está activo.
     *
     * @return bool
     */
    public function estaActivo(): bool
    {
        return $this->status === 1;
    }

    /**
     * Determina si el contrato está pagado hasta la fecha actual.
     *
     * @return bool
     */
    public function estaPagado()
    {
        return $this->pagado_hasta !== null && $this->pagado_hasta > now();
    }

    /**
     * Calcula la fecha del próximo cobro.
     *
     * @return Carbon|null
     */
    public function proximoCobro()
    {
        if ($this->pagado_hasta === null) {
            return null;
        }

        // Si cobro_siguiente es positivo, el cobro se hace X días antes del vencimiento
        if ($this->cobro_siguiente > 0) {
            return $this->pagado_hasta->subDays($this->cobro_siguiente);
        }

        // Si cobro_siguiente es 0, el cobro se hace el día del vencimiento
        if ($this->cobro_siguiente === 0) {
            return $this->pagado_hasta;
        }

        // Si cobro_siguiente es negativo, el cobro se hace después del vencimiento
        return $this->pagado_hasta->addDays(abs($this->cobro_siguiente));
    }

    /**
     * Determina si el contrato tiene pago por adelantado.
     *
     * @return bool
     */
    public function esPorAdelantado(): bool
    {
        return $this->por_adelantado === 'Si';
    }

    /**
     * Determina si el contrato es de tipo empresarial.
     *
     * @return bool
     */
    public function esEmpresarial(): bool
    {
        return $this->tipo === 'empresarial';
    }

    /**
     * Determina si el contrato tiene pago automatizado.
     *
     * @return bool
     */
    public function tienePagoAutomatizado(): bool
    {
        return $this->pago_automatizado === 1;
    }

    /**
     * Calcula los días restantes hasta el vencimiento del contrato.
     *
     * @return int|null Número de días restantes o null si no hay fecha de pago
     */
    public function diasHastaVencimiento(): ?int
    {
        if ($this->pagado_hasta === null) {
            return null;
        }

        return now()->diffInDays($this->pagado_hasta, false);
    }

    /**
     * Determina si el contrato está en período de gracia (dentro de los días de tregua).
     *
     * @return bool
     */
    public function estaEnPeriodoDeGracia(): bool
    {
        if ($this->pagado_hasta === null || $this->dias_tregua <= 0) {
            return false;
        }

        $diasVencido = now()->diffInDays($this->pagado_hasta, false);
        return $diasVencido < 0 && abs($diasVencido) <= $this->dias_tregua;
    }

    //    /**
    //     * Obtiene el servicio asociado con este contrato (alias más descriptivo).
    //     */
    //    public function servicio()
    //    {
    //        return $this->servicioRelacion();
    //    }

    /**
     * Obtiene los cobros asociados con este contrato.
     */
    public function cobros()
    {
        return $this->hasMany(Cobro::class, 'contrato', 'numero');
    }

    /**
     * Obtiene los cobros pendientes asociados con este contrato.
     */
    public function cobrosPendientes()
    {
        return $this->cobros()->where('pagado', 'No');
    }

    /**
     * Obtiene los cobros pagados asociados con este contrato.
     */
    public function cobrosPagados()
    {
        return $this->cobros()->where('pagado', 'Si');
    }

    /**
     * Obtiene los cobros vencidos asociados con este contrato.
     */
    public function cobrosVencidos()
    {
        return $this->cobros()->where('vencimiento', '<', now()->startOfDay())
            ->where('pagado', 'No');
    }

    /**
     * Recupera la fecha de vigencia del contrato basándose en el último registro de cobro.
     * Esta información es útil para determinar hasta cuándo es válido el servicio actual,
     * especialmente en casos de cambios de plan donde se debe respetar la vigencia pagada.
     *
     * @return Carbon|null Fecha de vigencia o null si no hay cobros pagados
     */
    public function recuperarVigencia(): ?Carbon
    {
        // Obtener el último cobro pagado del contrato
        $ultimoCobro = $this->cobrosPagados()
            ->orderBy('numero', 'desc')
            ->first();

        if (!$ultimoCobro) {
            return null;
        }

        // Devolver la fecha "hasta" del último cobro,
        // que representa hasta cuándo está pagado el servicio
        return $ultimoCobro->hasta;
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Antes de guardar, actualizar el campo pabusqueda
        static::saving(function ($contrato) {
            $contrato->updatePaBusqueda();
        });
    }

    /**
     * Actualiza el campo pabusqueda con los datos relevantes del contrato
     *
     * @return void
     */
    protected function updatePaBusqueda()
    {
        $this->pabusqueda = "contrato {$this->numero} {$this->usuario} {$this->servicio} {$this->dominio}";

        // // Quito espacios dobles y hago trim
        // $this->pabusqueda = preg_replace('/\s+/', ' ', $this->pabusqueda);
        // $this->pabusqueda = trim($this->pabusqueda);
    }
}
