<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Forzar HTTPS si la variable de entorno está configurada
        if (env('FORCE_HTTPS', false) && !app()->runningInConsole()) {
            URL::forceScheme('https');
        }

        // Configurar trusted proxies para Docker/Cloudflare
        if (!app()->runningInConsole()) {
            $this->configureTrustedProxies();
        }
    }

    /**
     * Configure trusted proxies for reverse proxy setup
     */
    private function configureTrustedProxies(): void
    {
        // Trust all proxies - usar con cuidado en producción
        // Para Cloudflare y Docker, confiamos en todos los proxies
        request()->setTrustedProxies(
            ['*'], // Trust all IPs (Cloudflare puede usar diferentes IPs)
            \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR |
            \Illuminate\Http\Request::HEADER_X_FORWARDED_HOST |
            \Illuminate\Http\Request::HEADER_X_FORWARDED_PORT |
            \Illuminate\Http\Request::HEADER_X_FORWARDED_PROTO |
            \Illuminate\Http\Request::HEADER_X_FORWARDED_AWS_ELB
        );
    }
}
