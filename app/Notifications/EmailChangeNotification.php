<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class EmailChangeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The new email address.
     *
     * @var string
     */
    protected $newEmail;

    /**
     * Create a new notification instance.
     *
     * @param string $newEmail
     * @return void
     */
    public function __construct(string $newEmail)
    {
        $this->newEmail = $newEmail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $verificationUrl = $this->verificationUrl($notifiable);

        // Enviar a la nueva dirección de correo, no a la actual del usuario
        return (new MailMessage)
            ->cc($this->newEmail)
            ->subject('Verificación de Cambio de Correo Electrónico - Mulbin')
            ->greeting('Estimado(a) Profesional Inmobiliario,')
            ->line(
                'Hemos recibido una solicitud para cambiar la dirección de correo electrónico asociada a su cuenta en Mulbin - Multibolsa Inmobiliaria S.A. de C.V.'
            )
            ->line(
                'La nueva dirección de correo electrónico solicitada es: ' . $this->newEmail
            )
            ->line(
                'Para completar este proceso y confirmar que tiene acceso a esta nueva dirección de correo, por favor haga clic en el botón de abajo:'
            )
            ->action('Verificar Nueva Dirección de Correo', $verificationUrl)
            ->line(
                'Si usted no solicitó este cambio, no es necesario realizar ninguna acción. Su dirección de correo actual seguirá siendo la misma.'
            )
            ->line(
                'Este enlace de verificación expirará en 60 minutos.'
            )
            ->salutation("Atentamente,\n \nEl equipo de Mulbin - Multibolsa Inmobiliaria S.A. de C.V.");
    }

    /**
     * Get the verification URL for the given notifiable.
     *
     * @param mixed $notifiable
     * @return string
     */
    protected function verificationUrl($notifiable): string
    {
        return URL::temporarySignedRoute(
            'verification.verify-email-change',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60)),
            [
                'id' => $notifiable->getKey(),
                'hash' => sha1($this->newEmail),
                'email' => $this->newEmail
            ]
        );
    }
}
