<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailChangeRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The new email address.
     *
     * @var string
     */
    protected $newEmail;

    /**
     * Create a new notification instance.
     *
     * @param string $newEmail
     * @return void
     */
    public function __construct(string $newEmail)
    {
        $this->newEmail = $newEmail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Solicitud de Cambio de Correo Electrónico - Mulbin')
            ->greeting('Estimado(a) Profesional Inmobiliario,')
            ->line(
                'Hemos recibido una solicitud para cambiar la dirección de correo electrónico asociada a su cuenta en Mulbin - Multibolsa Inmobiliaria S.A. de C.V.'
            )
            ->line(
                'La nueva dirección de correo electrónico solicitada es: ' . $this->newEmail
            )
            ->line(
                'Hemos enviado un correo de verificación a la nueva dirección para confirmar el cambio. Si usted no solicitó este cambio, por favor contacte a nuestro equipo de soporte inmediatamente o cambie su contraseña como medida de seguridad.'
            )
            ->line(
                'Si usted solicitó este cambio, puede ignorar este mensaje. El cambio se completará una vez que verifique la nueva dirección de correo electrónico.'
            )
            ->salutation("Atentamente,\n \nEl equipo de Mulbin - Multibolsa Inmobiliaria S.A. de C.V.");
    }
}
