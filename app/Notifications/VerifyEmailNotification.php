<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;

class VerifyEmailNotification extends VerifyEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Build the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        Log::info('VerifyEmailNotification', [
            'notifiable' => $notifiable,
        ]);

        $verificationUrl = $this->verificationUrl($notifiable);

        return (new MailMessage())
            ->subject('Verificación de Correo Electrónico - Mulbin')
            ->greeting('Estimado(a) Profesional Inmobiliario,')
            ->line(
                'Gracias por registrarse en Mulbin - Multibolsa Inmobiliaria S.A. de C.V., LA RED del sector inmobiliario.'
            )
            ->line(
                'Para garantizar la seguridad, profesionalismo y sobre todo la confianza de nuestra red, necesitamos verificar su dirección de correo electrónico antes de activar su perfil.'
            )
            ->action('Verificar Correo Electrónico', $verificationUrl)
            ->line(
                'Esta verificación es un paso esencial para formar parte de nuestra comunidad de profesionales inmobiliarios y acceder a todas las herramientas y servicios exclusivos que ofrecemos.'
            )
            ->line('Si usted no creó esta cuenta, no es necesario realizar ninguna acción.')
            ->salutation("Atentamente,\n \nEl equipo de Mulbin - Multibolsa Inmobiliaria S.A. de C.V.");
    }

    /**
     * Get the verification URL for the given notifiable.
     *
     * @param mixed $notifiable
     * @return string
     */
    protected function verificationUrl($notifiable): string
    {
        // Usar ruta simple sin firma ya que el controlador maneja la seguridad internamente
        return route(
            'verification.verify.guest',
            [
                'id' => $notifiable->getKey(),
                'hash' => sha1($notifiable->getEmailForVerification()),
            ]
        );
    }
}
