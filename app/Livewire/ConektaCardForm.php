<?php

namespace App\Livewire;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class ConektaCardForm extends Component
{
    public ?string $conektaTokenId = null;

    protected function getListeners(): array
    {
        return [
            'tokenCreated' => 'handleTokenCreated',
        ];
    }

//    public function mount()
//    {
//        // Si ya hay un token, emitir evento para notificar al componente padre
//        if ($this->conektaTokenId) {
//            $this->dispatch('card-validated', tokenId: $this->conektaTokenId);
//        }
//    }

    /**
     * Maneja el evento cuando se crea un token de Conekta
     */
    public function handleTokenCreated($tokenId): void
    {
        // Asegurarse de que tokenId sea un string
        if (is_array($tokenId)) {
            $this->conektaTokenId = $tokenId[0] ?? '';
        } else {
            $this->conektaTokenId = $tokenId;
        }

        // Emitir evento para notificar al componente padre
        $this->dispatch('card-validated', tokenId: $this->conektaTokenId);
    }

    public function render(): View
    {
        return view('livewire.conekta-card-form');
    }
} 