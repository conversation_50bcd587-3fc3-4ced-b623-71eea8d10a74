<?php

namespace App\Livewire;

use App\Livewire\Traits\UpdateContratoYCobroTrait;
use App\Models\ConektaCustomer;
use App\Models\Contrato;
use App\Models\Servicio;
use App\Models\Subscription;
use App\Services\PaymentService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Livewire\Component;

class ChangePlanForm extends Component
{
    use UpdateContratoYCobroTrait;

    // Variables para el plan seleccionado
    public $selectedPlan = null;
    public $billingPeriod = 'monthly';
    public $planPrice = 0;
    public $totalPrice = 0;
    public $addMsComplement = false;
    public $msComplementPrice = 0;
    public $msComplementName = '';
    public $msComplementDescription = '';
    public $msComplementFeatures = [];

    // Variables para tarjeta de crédito/débito (solo para UI)
    public $cardNumber;
    public $cardName;
    public $cardExpiry;
    public $cardCvc;

    // Token de Conekta
    public ?string $conektaTokenId = null;

    // Estado del formulario
    public int $currentStep = 1; // 1: Selección de plan, 2: Datos de pago (si es necesario)
    public bool $isChangingPlan = true;
    public bool $processingPayment = false;
    public bool $hasValidatedCard = false;

    // Mensajes de estado
    public string $successMessage = '';
    public string $errorMessage = '';

    /**
     * Listeners para eventos
     */
    protected function getListeners(): array
    {
        return [
            'planSelected' => 'handlePlanSelected',
            'tokenCreated' => 'handleTokenCreated',
            'card-validated' => 'handleCardValidated'
        ];
    }

    /**
     * Inicializa el componente
     */
    public function mount()
    {
        Log::info('ChangePlanForm › mount: -------');

        $user = Auth::user();

        // Verificar si el usuario tiene un sitio web activado - abordaje simplificado
        if ($user->website_activated_at === null) {
            Log::warning('ChangePlanForm › mount: Usuario sin sitio web activado', ['user_id' => $user->id]);
            return redirect()->route('activar');
        }

        // Verificar si el usuario ya tiene una tarjeta validada
        $this->hasValidatedCard = $this->hasValidatedCard();

        // Registrar información sobre el usuario y su plan
        Log::info('ChangePlanForm › mount: Usuario con sitio web activado', [
            'user_id' => $user->id,
            'has_subscription' => $user->subscription ? 'Sí' : 'No',
            'website_activated_at' => $user->website_activated_at,
            'has_validated_card' => $this->hasValidatedCard ? 'Sí' : 'No'
        ]);

        // Si tiene una suscripción de pago, registramos los detalles
        if ($user->subscription) {
            Log::info('ChangePlanForm › mount: Detalles de la suscripción', [
                'subscription_id' => $user->subscription->id,
                'conekta_plan' => $user->subscription->conekta_plan,
                'billing_period' => $user->subscription->billing_period,
                'status' => $user->subscription->status
            ]);
        } else {
            // Si no tiene suscripción, probablemente tiene el plan gratuito
            Log::info('ChangePlanForm › mount: Usuario probablemente con plan gratuito');
        }
    }

    /**
     * Maneja el evento cuando se selecciona un plan
     */
    public function handlePlanSelected($data): void
    {
        $this->selectedPlan = $data['plan'];
        $this->billingPeriod = $data['billingPeriod'];
        $this->planPrice = $data['price'];
        $this->totalPrice = $data['totalPrice'];

        // Manejar el complemento SI-MS si está presente
        $this->addMsComplement = $data['addMsComplement'] ?? false;
        $this->msComplementPrice = $data['msComplementPrice'] ?? 0;
        $this->msComplementName = $data['msComplementName'] ?? 'Complemento Micrositio';
        $this->msComplementDescription = $data['msComplementDescription'] ?? '';
        $this->msComplementFeatures = $data['msComplementFeatures'] ?? [];

        // Avanzamos al siguiente paso para solicitar datos de pago o mostrar el mensaje de tarjeta validada
        $this->nextStep();

        // Emitimos un evento adicional para activar el scroll una vez cargado el formulario
        $this->dispatch('planActivationStep');
    }

    /**
     * Maneja el evento cuando se crea un token de Conekta
     */
    public function handleTokenCreated($tokenId): void
    {
        // Asegurarse de que tokenId sea un string
        if (is_array($tokenId)) {
            $this->conektaTokenId = $tokenId[0] ?? '';
        } else {
            $this->conektaTokenId = $tokenId;
        }
    }

    /**
     * Maneja el evento cuando el componente ConektaCardForm valida la tarjeta
     */
    public function handleCardValidated($tokenId): void
    {
        $this->conektaTokenId = $tokenId;
    }

    /**
     * Avanza al siguiente paso del formulario
     */
    public function nextStep(): void
    {
        $this->currentStep++;
        $this->dispatch('formStepReady');
    }

    /**
     * Verifica si el usuario ya tiene una tarjeta validada
     */
    public function hasValidatedCard(): bool
    {
        $user = Auth::user();
        return $user->conektaCustomer && $user->conektaCustomer->hasPaymentSources();
    }

    /**
     * Procesa el cambio de plan
     */
    public function processChangePlan(): void
    {
        Log::info('ChangePlanForm › processChangePlan: -------');

        $this->processingPayment = true;
        $this->errorMessage = '';
        $this->successMessage = '';

        $user = Auth::user();
        $subscription = $user->subscription;

        // Verificar si el usuario tiene un sitio web activado - abordaje simplificado
        if ($user->website_activated_at === null) {
            $this->errorMessage = 'Su sitio web no está activado. Por favor active su sitio primero.';
            $this->processingPayment = false;
            return;
        }

        // Si el usuario no tiene una suscripción, asumimos que tiene el plan gratuito
        $currentPlanId = 'SI-FREE';
        $currentBillingPeriod = 'monthly';

        if ($subscription) {
            // Si tiene una suscripción, obtenemos el plan actual de Conekta
            $conektaPlan = $subscription->conekta_plan;
            $parts = explode('-', $conektaPlan);

            if (count($parts) >= 3) {
                if (count($parts) == 4) {
                    // Si tiene 4 partes, el ID del plan es la segunda y tercera parte juntas
                    $currentPlanId = $parts[1] . '-' . $parts[2]; // SI-PRO o SI-PLUS
                    $currentBillingPeriod = $parts[3]; // monthly, quarterly, etc.
                } else {
                    if (count($parts) == 3 && $parts[1] == 'SI') {
                        // Si tiene 3 partes y la segunda es 'SI', el ID del plan es SI-FREE
                        $currentBillingPeriod = $parts[2]; // monthly, quarterly, etc.
                    }
                }
            }
        }

        // Mapear los ID's de planes según corresponda en Conekta
        // Si es plan FREE con complemento SI-MS, usamos el ID del plan SI-MS
        if ($this->selectedPlan === 'SI-FREE' && $this->addMsComplement) {
            $planConektaId = 'plan-SI-MS-' . $this->billingPeriod;
            // Aseguramos que para la lógica interna usamos SI-MS y no SI-FREE
            $selectedPlanForLogic = 'SI-MS';
        } else {
            $planConektaId = 'plan-' . $this->selectedPlan . '-' . $this->billingPeriod;
            $selectedPlanForLogic = $this->selectedPlan;
        }

        Log::info('ChangePlanForm › processChangePlan: Procesando cambio de plan', [
            'user_id' => $user->id,
            'current_plan_id' => $currentPlanId,
            'current_billing_period' => $currentBillingPeriod,
            'new_plan' => $planConektaId,
            'selected_plan' => $this->selectedPlan,
            'selected_plan_for_logic' => $selectedPlanForLogic,
            'billing_period' => $this->billingPeriod,
            'add_ms_complement' => $this->addMsComplement ? 'Sí' : 'No'
        ]);

        // Si el plan seleccionado es el mismo que ya tiene, no hacemos nada
        if ($currentPlanId === $selectedPlanForLogic && $currentBillingPeriod === $this->billingPeriod) {
            Log::info('ChangePlanForm › processChangePlan: El usuario ya tiene este plan activo');
            $this->successMessage = 'Ya tiene este plan activo.';
            $this->processingPayment = false;
            return;
        }

        try {
            $paymentService = app(PaymentService::class);

            // Caso especial: Si el usuario tiene plan gratuito y quiere cambiar a un plan de pago o añadir el complemento SI-MS
            if ($currentPlanId === 'SI-FREE' && ($selectedPlanForLogic !== 'SI-FREE' || $this->addMsComplement)) {
                Log::info('ChangePlanForm › processChangePlan: Cambio de plan gratuito a plan de pago');

                // Verificar que haya proporcionado una tarjeta
                if (empty($this->conektaTokenId) && !$this->hasValidatedCard()) {
                    $this->errorMessage = 'Por favor valide su tarjeta primero.';
                    $this->processingPayment = false;
                    return;
                }

                // Si el usuario no tiene un cliente en Conekta pero proporcionó un token, creamos el cliente
                if (!$user->conektaCustomer && !empty($this->conektaTokenId)) {
                    $customerResult = $paymentService->createCustomer(
                        $this->conektaTokenId,
                        $user->email,
                        $user->name
                    );

                    if (isset($customerResult['error'])) {
                        $this->errorMessage = 'Error al procesar la tarjeta: ' . $customerResult['error'];
                        $this->processingPayment = false;
                        return;
                    }

                    // Crear el registro de ConektaCustomer en nuestra base de datos
                    // Verificar si el resultado es un objeto o un array
                    if (is_object($customerResult)) {
                        // Si es un objeto, accedemos a sus propiedades directamente
                        $conektaCustomer = ConektaCustomer::create([
                            'user_id' => $user->id,
                            'conekta_id' => $customerResult->id,
                            'payment_sources' => [
                                [
                                    'id' => $customerResult->payment_sources->data[0]->id ?? null,
                                    'brand' => $customerResult->payment_sources->data[0]->brand ?? '',
                                    'last4' => $customerResult->payment_sources->data[0]->last4 ?? '',
                                    'exp_month' => $customerResult->payment_sources->data[0]->exp_month ?? '',
                                    'exp_year' => $customerResult->payment_sources->data[0]->exp_year ?? '',
                                    'name' => $customerResult->payment_sources->data[0]->name ?? '',
                                ]
                            ],
                            'default_payment_source_id' => $customerResult->payment_sources->data[0]->id ?? null,
                        ]);
                    } else {
                        // Si es un array, accedemos a sus elementos con notación de array
                        $conektaCustomer = ConektaCustomer::create([
                            'user_id' => $user->id,
                            'conekta_id' => $customerResult['id'],
                            'payment_sources' => [
                                [
                                    'id' => $customerResult['payment_sources']['data'][0]['id'] ?? null,
                                    'brand' => $customerResult['payment_sources']['data'][0]['brand'] ?? '',
                                    'last4' => $customerResult['payment_sources']['data'][0]['last4'] ?? '',
                                    'exp_month' => $customerResult['payment_sources']['data'][0]['exp_month'] ?? '',
                                    'exp_year' => $customerResult['payment_sources']['data'][0]['exp_year'] ?? '',
                                    'name' => $customerResult['payment_sources']['data'][0]['name'] ?? '',
                                ]
                            ],
                            'default_payment_source_id' => $customerResult['payment_sources']['data'][0]['id'] ?? null,
                        ]);
                    }

                    Log::info('ChangePlanForm › processChangePlan: Cliente Conekta creado en base de datos', [
                        'conekta_id' => $conektaCustomer->conekta_id,
                        'default_payment_source_id' => $conektaCustomer->default_payment_source_id
                    ]);

                    // Refrescar el usuario para obtener el cliente de Conekta recién creado
                    $user = Auth::user();

                    // Esperar un momento para asegurar que la relación conektaCustomer esté disponible
                    sleep(1);

                    // Verificar si el cliente de Conekta está disponible
                    if (!$user->conektaCustomer) {
                        Log::warning(
                            'ChangePlanForm › processChangePlan: Cliente Conekta no disponible después de crearlo. Recargando usuario...'
                        );
                        // Intentar cargar el usuario nuevamente
                        $user = Auth::user();

                        // Si aún no está disponible, intentar buscar el cliente directamente
                        if (!$user->conektaCustomer) {
                            Log::warning(
                                'ChangePlanForm › processChangePlan: Buscando cliente Conekta directamente en la base de datos'
                            );
                            $conektaCustomerModel = ConektaCustomer::where('user_id', $user->id)->first();

                            if (!$conektaCustomerModel) {
                                $this->errorMessage = 'Error al cargar los datos del cliente de pago. Por favor intente nuevamente.';
                                $this->processingPayment = false;
                                return;
                            }

                            // Usar directamente el ID del cliente de Conekta
                            $conektaCustomerId = $conektaCustomerModel->conekta_id;
                        } else {
                            $conektaCustomerId = $user->conektaCustomer->conekta_id;
                        }
                    } else {
                        $conektaCustomerId = $user->conektaCustomer->conekta_id;
                    }
                }

                // Crear nueva suscripción
                $result = $paymentService->createSubscription(
                    $user->conektaCustomer ? $user->conektaCustomer->conekta_id : $conektaCustomerId,
                    $planConektaId
                );

                if (isset($result['error'])) {
                    $this->errorMessage = 'Error al cambiar el plan: ' . $result['error'];
                    $this->processingPayment = false;
                    return;
                }

                // Crear un nuevo registro de suscripción
                $subscription = new Subscription([
                    'user_id' => $user->id,
                    'conekta_id' => $result['Subscription']->id,
                    'conekta_plan' => $planConektaId,
                    'conekta_customer' => $user->conektaCustomer ? $user->conektaCustomer->conekta_id : $conektaCustomerId,
                    'status' => 'active',
                    'billing_period' => $this->billingPeriod,
                    'plan_price' => $this->planPrice,
                    'total_price' => $this->totalPrice,
                    'personalization' => false,
                ]);

                $subscription->save();
                Log::info(
                    'ChangePlanForm › processChangePlan: Nueva suscripción creada',
                    ['subscription_id' => $subscription->id]
                );

                // Actualizar el contrato existente y crear un cobro prorrateado
                $this->updateContratoYCobro($user, $currentPlanId, $currentBillingPeriod, $subscription);
            } //

            // Caso: Si el usuario tiene un plan de pago y quiere cambiar a otro plan de pago
            elseif ($currentPlanId !== 'SI-FREE' && $selectedPlanForLogic !== 'SI-FREE' && $subscription) {
                Log::info('ChangePlanForm › processChangePlan: Cambio entre planes de pago');

                // Verificar si es un upgrade o downgrade
                $isUpgrade = $this->esPlanSuperior($selectedPlanForLogic, $currentPlanId);

                // Caso: Si es un downgrade, no cancelamos la suscripción actual
                if (!$isUpgrade) {
                    Log::info(
                        'ChangePlanForm › processChangePlan: Downgrade de plan - manteniendo cobro actual hasta su vencimiento'
                    );

                    // Solo actualizamos la suscripción en Conekta y en la base de datos
                    $result = $paymentService->updateSubscriptionPlan(
                        $subscription->conekta_customer,
                        $subscription->conekta_id,
                        $planConektaId
                    );

                    if (isset($result['error'])) {
                        $this->errorMessage = 'Error al cambiar el plan: ' . $result['error'];
                        $this->processingPayment = false;
                        return;
                    }

                    // Actualizar la suscripción en la base de datos
                    $subscription->conekta_plan = $planConektaId;
                    $subscription->billing_period = $this->billingPeriod;
                    $subscription->plan_price = $this->planPrice;
                    $subscription->total_price = $this->totalPrice;
                    $subscription->save();
                    Log::info(
                        'ChangePlanForm › processChangePlan: Suscripción actualizada',
                        ['subscription_id' => $subscription->id]
                    );

                    // Actualizar el contrato existente
                    $contrato = Contrato::where('usuario', $user->usuario)
                        ->where('servicio', $currentPlanId)
                        ->where('status', 1)
                        ->whereHas('servicioRelacion', function ($query) {
                            $query->where('tipo', 'PLAN-SI');
                        })
                        ->first();
                    if ($contrato) {
                        $contrato->servicio = $selectedPlanForLogic;
                        $contrato->forma_pago = [
                            'monthly' => 1,
                            'quarterly' => 3,
                            'biannual' => 6,
                            'annual' => 12
                        ][$this->billingPeriod];
                        $contrato->save();
                        Log::info(
                            'ChangePlanForm › processChangePlan: Contrato actualizado',
                            ['contrato_id' => $contrato->numero]
                        );
                    } else {
                        Log::error('ChangePlanForm › processChangePlan: No se encontró el contrato para actualizar');
                    }
                } //

                // Caso: Si es un upgrade, cancelamos la suscripción actual y creamos una nueva
                else {
                    // Cancelar la suscripción actual
                    $cancelResult = $paymentService->cancelSubscription(
                        $subscription->conekta_customer,
                        $subscription->conekta_id
                    );

                    if (!$cancelResult) {
                        Log::warning('No se pudo cancelar la suscripción anterior, pero continuamos con el proceso');
                    }

                    // Asegurarse de que tenemos un ID de cliente de Conekta válido
                    if ($user->conektaCustomer) {
                        $conektaCustomerId = $user->conektaCustomer->conekta_id;
                    } else {
                        // Buscar el cliente de Conekta directamente
                        $conektaCustomerModel = ConektaCustomer::where('user_id', $user->id)->first();
                        if (!$conektaCustomerModel) {
                            $this->errorMessage = 'Error al cargar los datos del cliente de pago. Por favor intente nuevamente.';
                            $this->processingPayment = false;
                            return;
                        }
                        $conektaCustomerId = $conektaCustomerModel->conekta_id;
                    }

                    // Crear nueva suscripción
                    $result = $paymentService->createSubscription(
                        $conektaCustomerId,
                        $planConektaId
                    );

                    if (isset($result['error'])) {
                        $this->errorMessage = 'Error al cambiar el plan: ' . $result['error'];
                        $this->processingPayment = false;
                        return;
                    }

                    // Actualizar la suscripción en la base de datos
                    $subscription->conekta_plan = $planConektaId;
                    $subscription->conekta_id = $result['Subscription']->id;
                    $subscription->status = 'active';
                    $subscription->billing_period = $this->billingPeriod;
                    $subscription->plan_price = $this->planPrice;
                    $subscription->total_price = $this->totalPrice;
                    $subscription->save();
                    Log::info(
                        'ChangePlanForm › processChangePlan: Suscripción actualizada',
                        ['subscription_id' => $subscription->id]
                    );

                    // Actualizar el contrato existente y crear un cobro prorrateado
                    $this->updateContratoYCobro($user, $currentPlanId, $currentBillingPeriod, $subscription);
                }
            } //

            // Caso: Si el usuario tiene un plan de pago y quiere cambiar al plan gratuito
            elseif ($currentPlanId !== 'SI-FREE' && $selectedPlanForLogic === 'SI-FREE' && !$this->addMsComplement && $subscription) {
                Log::info('ChangePlanForm › processChangePlan: Cambio de plan de pago a plan gratuito');

                // Cancelar la suscripción actual
                $cancelResult = $paymentService->cancelSubscription(
                    $subscription->conekta_customer,
                    $subscription->conekta_id
                );

                if (!$cancelResult) {
                    Log::warning('No se pudo cancelar la suscripción anterior');
                }

                // Marcar la suscripción como cancelada
                $subscription->status = 'canceled';
                $subscription->ends_at = now();
                $subscription->save();
                Log::info(
                    'ChangePlanForm › processChangePlan: Suscripción cancelada',
                    ['subscription_id' => $subscription->id]
                );

                // También actualizamos el contrato para reflejar el cambio al plan gratuito
                $this->updateContratoYCobro($user, $currentPlanId, $currentBillingPeriod, $subscription);
            }

            // Mensaje de éxito personalizado según el plan seleccionado
            if ($this->selectedPlan === 'SI-FREE' && $this->addMsComplement) {
                $this->successMessage = 'Plan cambiado exitosamente. Su nuevo plan es: Freemium con ' . $this->msComplementName;
            } else {
                $this->successMessage = 'Plan cambiado exitosamente. Su nuevo plan es: ' .
                    ($this->selectedPlan === 'SI-FREE' ? 'Freemium' :
                        ($this->selectedPlan === 'SI-PRO' ? 'PRO' : 'PLUS'));
            }

            // Redirigir al dashboard después de un breve retraso
            $this->dispatch('refreshPage');
        } catch (Exception $e) {
            Log::error('Error al cambiar el plan: ' . $e->getMessage());
            $this->errorMessage = 'Error al cambiar el plan: ' . $e->getMessage();
        }

        $this->processingPayment = false;
    }

    /**
     * Determina si el plan nuevo es superior o igual al plan actual
     * En el caso de plan igual, se podría estar tratando de un cambio en el periodo de facturación
     *
     * @param string $nuevoPlan
     * @param string $planActual
     * @return bool
     */
    protected function esPlanSuperior(string $nuevoPlan, string $planActual): bool
    {
        // Obtener nivel del plan nuevo desde la base de datos
        $nivelNuevo = Servicio::where('servicio', $nuevoPlan)->value('level') ?? 0;

        // Obtener nivel del plan actual desde la base de datos
        $nivelActual = Servicio::where('servicio', $planActual)->value('level') ?? 0;

        Log::info('ChangePlanForm › esPlanSuperior: Evaluando si es upgrade usando niveles de DB', [
            'plan_actual' => $planActual,
            'nivel_actual' => $nivelActual,
            'plan_nuevo' => $nuevoPlan,
            'nivel_nuevo' => $nivelNuevo,
            'es_upgrade' => $nivelNuevo > $nivelActual ? 'Sí' : 'No',
            'posible_cambio_periodo' => $nivelNuevo === $nivelActual ? 'Sí' : 'No',
            'add_ms_complement' => $this->addMsComplement ? 'Sí' : 'No'
        ]);

        return $nivelNuevo >= $nivelActual;
    }

    /**
     * Renderiza el componente
     */
    public function render(): View
    {
        return view('livewire.change-plan-form');
    }
}
