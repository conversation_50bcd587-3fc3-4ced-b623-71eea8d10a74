<?php

namespace App\Livewire;

use App\Models\SIConfig;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Livewire\Component;

class DashboardAndWebsiteAccess extends Component
{
    public string $websiteUrl = '';
    public string $dashboardUrl = '';
    public string $usuario = '';
    public string $authToken = '';

    public function mount(): void
    {
        // Cargar la relación SIConfig para evitar consultas adicionales
        $user = auth()->user()->load('siConfig');

        $this->websiteUrl = $user->getWebsiteUrl();
        $this->dashboardUrl = $user->getPanelUrl();
        $this->usuario = $user->usuario ?? '';

        // Generar y guardar el token de autenticación
        $this->generateAndSaveAuthToken($user);
    }

    /**
     * Genera un nuevo token de autenticación y lo guarda en SIConfig
     */
    private function generateAndSaveAuthToken($user): void
    {
        if (empty($user->usuario)) {
            return;
        }

        // Generar un token único
        $token = Str::random(64);

        // Buscar la configuración del usuario (debe existir)
        $siConfig = SIConfig::where('usuario', $user->usuario)->first();

        if ($siConfig) {
            // Actualizar el token existente
            try {
                $siConfig->update(['auth_token' => $token]);
            } catch (Exception $e) {
                Log::error('Error al guardar token de autenticación: ' . $e->getMessage());
                // En caso de error (como en tests), simplemente continuar
                // El token se generará pero no se persistirá
            }
        }

        $this->authToken = $token;
    }

    public function render(): View
    {
        return view('livewire.dashboard-and-website-access');
    }
}
