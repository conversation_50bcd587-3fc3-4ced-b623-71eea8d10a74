<?php

namespace App\Livewire;

use App\Models\Servicio;
use App\Models\SIConfig;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class PlanSelector extends Component
{
    public $selectedPlan = null;
    public $isChangingPlan = false;
    public $addMsComplement = false;
    public $msComplement = null;
    public $msComplementName = null;
    public $msComplementDescription = null;
    public $msComplementFeatures = null;
    public array $plans = [];
    public $billingPeriod = 'monthly';
    public $billingOptions = [
        'monthly' => [
            'id' => 'monthly',
            'name' => 'Mensual',
            'discount' => 0
        ],
        'quarterly' => [
            'id' => 'quarterly',
            'name' => 'Trimestral',
            'discount' => 5
        ],
        'biannual' => [
            'id' => 'biannual',
            'name' => 'Semestral',
            'discount' => 10
        ],
        'annual' => [
            'id' => 'annual',
            'name' => 'Anual',
            'discount' => 15
        ]
    ];

    public function mount($preselectedPlan = null, $isChangingPlan = false)
    {
        // Si estamos cambiando de plan, marcamos la bandera
        $this->isChangingPlan = $isChangingPlan;

        // Si el usuario ya tiene un plan seleccionado previamente, lo cargamos
        $user = Auth::user();

        // Recupero los planes principales del model Servicio
        $planes = Servicio::whereIn('servicio', ['SI-FREE', 'SI-PRO', 'SI-PLUS'])
            ->orderBy('orden', 'asc')
            ->get();

        // Recupero también el plan SI-MS (complemento)
        $planMS = Servicio::where('servicio', 'SI-MS')->first();

        // Almaceno la información del complemento SI-MS para usarla después
        $this->msComplement = null;
        if ($planMS) {
            $ms_public_info = json_decode($planMS->public_info, true);
            $this->msComplement = [
                'id' => 'SI-MS',
                'name' => $ms_public_info['name'] ?? 'Complemento Micrositio',
                'price' => $planMS->precio,
                'description' => $ms_public_info['description'] ?? 'Mejore su presencia en redes sociales',
                'features' => $ms_public_info['features'] ?? [],
            ];
        }

        // Proceso los planes principales
        foreach ($planes as $plan) {
            $planId = $plan->servicio;
            $public_info = json_decode($plan->public_info, true);

            $this->plans[$planId] = [
                'id' => $planId,
                'name' => $public_info['name'] ?? $planId,
                'price' => $plan->precio,
                'description' => $public_info['description'] ?? '',
                'features' => $public_info['features'] ?? [],
                'color' => $public_info['color'] ?? 'gray',
                'popular' => $public_info['popular'] ?? false
            ];
        }

        // Si hay un plan preseleccionado, lo usamos
        if ($preselectedPlan && array_key_exists($preselectedPlan, $this->plans)) {
            $this->selectedPlan = $preselectedPlan;
        } else {
            // Por defecto seleccionamos el plan web que es el más popular
            $this->selectedPlan = 'SI-PRO';
        }

        // Si estamos cambiando de plan, intentamos determinar el plan actual del usuario
        if ($this->isChangingPlan) {
            // Verificamos si el usuario tiene una suscripción activa
            if ($user->subscription) {
                $currentPlan = $user->subscription->conekta_plan;
                Log::info(
                    'PlanSelector › mount: Plan actual del usuario (desde suscripción)',
                    ['conekta_plan' => $currentPlan]
                );

                // Extraer el ID del plan de Conekta (formato: plan-SI-PRO-monthly)
                $parts = explode('-', $currentPlan);
                Log::info('PlanSelector › mount: Partes del plan', ['parts' => $parts]);

                if (count($parts) >= 3) {
                    // Caso especial: plan-SI-MS-<periodo>
                    if (count($parts) == 4 && $parts[1] === 'SI' && $parts[2] === 'MS') {
                        // Es el plan SI-MS (Micrositio) como complemento del FREE
                        $planId = 'SI-FREE';
                        $this->addMsComplement = true;
                        $this->msComplementName = $this->msComplement['name'] ?? 'Complemento Micrositio';
                        $this->msComplementDescription = $this->msComplement['description'] ?? '';
                        $this->msComplementFeatures = $this->msComplement['features'] ?? [];
                        $this->billingPeriod = $parts[3];
                    } else {
                        if (count($parts) == 4) {
                            // Si tiene 4 partes, el ID del plan es la segunda y tercera parte juntas
                            $planId = $parts[1] . '-' . $parts[2]; // SI-PRO o SI-PLUS
                        } else {
                            if (count($parts) == 3 && $parts[1] == 'SI') {
                                // Si tiene 3 partes y la segunda es 'SI', el ID del plan es SI-FREE
                                $planId = 'SI-FREE';
                                // Verificar si tiene el complemento SI-MS activo
                                $siConfig = SIConfig::where('usuario', $user->usuario)->first();
                                if ($siConfig && $siConfig->custom_data) {
                                    $customData = json_decode($siConfig->custom_data, true);
                                    if (isset($customData['addMsComplement']) && $customData['addMsComplement']) {
                                        $this->addMsComplement = true;
                                        $this->msComplementName = $customData['msComplementName'] ?? 'Complemento Micrositio';
                                        $this->msComplementDescription = $customData['msComplementDescription'] ?? '';
                                        $this->msComplementFeatures = $customData['msComplementFeatures'] ?? [];
                                    }
                                }
                            } else {
                                // Por defecto, usamos la segunda parte
                                $planId = $parts[1];
                            }
                        }
                    }

                    Log::info('PlanSelector › mount: ID del plan extraído', ['planId' => $planId]);

                    if (array_key_exists($planId, $this->plans)) {
                        $this->selectedPlan = $planId;
                        Log::info('PlanSelector › mount: Plan seleccionado', ['selectedPlan' => $this->selectedPlan]);
                    } else {
                        Log::warning(
                            'PlanSelector › mount: El plan extraído no existe en la lista de planes',
                            ['planId' => $planId, 'availablePlans' => array_keys($this->plans)]
                        );
                    }

                    // También preseleccionamos el período de facturación
                    $lastPart = end($parts); // Obtenemos la última parte (monthly, quarterly, etc.)
                    if (array_key_exists($lastPart, $this->billingOptions)) {
                        $this->billingPeriod = $lastPart;
                        Log::info(
                            'PlanSelector › mount: Período de facturación seleccionado',
                            ['billingPeriod' => $this->billingPeriod]
                        );
                    } else {
                        Log::warning(
                            'PlanSelector › mount: El período extraído no existe en las opciones de facturación',
                            ['period' => $lastPart, 'availableOptions' => array_keys($this->billingOptions)]
                        );
                    }
                } else {
                    Log::warning(
                        'PlanSelector › mount: Formato de plan no reconocido',
                        ['conekta_plan' => $currentPlan]
                    );
                }
            } else {
                // Si el usuario no tiene una suscripción pero tiene el sitio web activado, asumimos que tiene el plan gratuito
                if ($user->website_activated_at !== null) {
                    Log::info(
                        'PlanSelector › mount: Usuario con sitio web activado pero sin suscripción, asumiendo plan gratuito'
                    );
                    $this->selectedPlan = 'SI-FREE';
                }
            }
        }
    }

    public function selectPlan($planId)
    {
        if (array_key_exists($planId, $this->plans)) {
            $this->selectedPlan = $planId;
        }
    }

    public function selectBillingPeriod($period)
    {
        if (array_key_exists($period, $this->billingOptions)) {
            $this->billingPeriod = $period;
        }
    }

    public function getDiscountedPrice()
    {
        if (!$this->selectedPlan || $this->plans[$this->selectedPlan]['price'] == 0) {
            return 0;
        }

        $basePrice = $this->plans[$this->selectedPlan]['price'];
        $discount = $this->billingOptions[$this->billingPeriod]['discount'];

        return $basePrice * (1 - ($discount / 100));
    }

    public function getTotalPrice()
    {
        $monthlyPrice = $this->getDiscountedPrice();

        switch ($this->billingPeriod) {
            case 'quarterly':
                return $monthlyPrice * 3;
            case 'biannual':
                return $monthlyPrice * 6;
            case 'annual':
                return $monthlyPrice * 12;
            default:
                return $monthlyPrice;
        }
    }

    public function getSavingsAmount()
    {
        if (!$this->selectedPlan || $this->plans[$this->selectedPlan]['price'] == 0) {
            return 0;
        }

        $basePrice = $this->plans[$this->selectedPlan]['price'];
        $discountedTotal = $this->getTotalPrice();

        switch ($this->billingPeriod) {
            case 'quarterly':
                return ($basePrice * 3) - $discountedTotal;
            case 'biannual':
                return ($basePrice * 6) - $discountedTotal;
            case 'annual':
                return ($basePrice * 12) - $discountedTotal;
            default:
                return 0;
        }
    }

    public function continueToPlanActivation()
    {
        // Preparamos los datos del plan seleccionado
        $planData = [
            'plan' => $this->selectedPlan,
            'billingPeriod' => $this->billingPeriod,
            'price' => $this->getDiscountedPrice(),
            'totalPrice' => $this->getTotalPrice(),
            'isChangingPlan' => $this->isChangingPlan,
            'addMsComplement' => false
        ];

        // Si es plan FREE y se seleccionó el complemento SI-MS
        if ($this->selectedPlan === 'SI-FREE' && $this->addMsComplement && $this->msComplement) {
            $planData['addMsComplement'] = true;
            $planData['msComplementPrice'] = $this->msComplement['price'];
            $planData['msComplementName'] = $this->msComplement['name'];
            $planData['msComplementDescription'] = $this->msComplement['description'];
            $planData['msComplementFeatures'] = $this->msComplement['features'];
            $planData['totalPrice'] = $this->msComplement['price']; // El precio total es solo el del complemento ya que el plan FREE es gratuito
        }

        // Emitimos un evento para que el componente padre (ActivationForm o ChangePlanForm) reciba el plan seleccionado
        $this->dispatch('planSelected', $planData);

        // Emitimos un evento adicional para activar el scroll una vez cargado el formulario
        $this->dispatch('planActivationStep');
    }

    /**
     * Verifica si el usuario ya tiene una tarjeta validada
     */
    public function hasValidatedCard()
    {
        $user = Auth::user();
        return $user->conektaCustomer && $user->conektaCustomer->hasPaymentSources();
    }

    /**
     * Obtiene el plan actual del usuario
     */
    public function getCurrentPlan()
    {
        $user = Auth::user();
        $currentPlanId = 'SI-FREE';
        $currentBillingPeriod = 'monthly';

        // Si el usuario tiene una suscripción activa, obtenemos su plan actual
        if ($user->subscription) {
            $currentPlan = $user->subscription->conekta_plan;
            $parts = explode('-', $currentPlan);

            if (count($parts) >= 3) {
                if (count($parts) == 4 && $parts[1] === 'SI' && $parts[2] === 'MS') {
                    // Si el plan es SI-MS, lo identificamos específicamente
                    $currentPlanId = 'SI-MS';
                } else {
                    if (count($parts) == 4) {
                        // Si tiene 4 partes, el ID del plan es la segunda y tercera parte juntas
                        $currentPlanId = $parts[1] . '-' . $parts[2]; // SI-PRO o SI-PLUS
                    } else {
                        if (count($parts) == 3 && $parts[1] == 'SI') {
                            // Si tiene 3 partes y la segunda es 'SI', el ID del plan es SI-FREE
                            $currentPlanId = 'SI-FREE';
                        }
                    }
                }

                // También obtenemos el período de facturación
                $lastPart = end($parts); // Obtenemos la última parte (monthly, quarterly, etc.)
                if (array_key_exists($lastPart, $this->billingOptions)) {
                    $currentBillingPeriod = $lastPart;
                }
            }
        } else {
            if ($user->website_activated_at !== null) {
                // Si el usuario no tiene una suscripción pero tiene el sitio web activado, asumimos que tiene el plan gratuito
                $currentPlanId = 'SI-FREE';
            }
        }

        return [
            'planId' => $currentPlanId,
            'billingPeriod' => $currentBillingPeriod
        ];
    }

    /**
     * Verifica si el plan seleccionado es diferente al plan actual
     */
    public function isPlanDifferent()
    {
        $currentPlan = $this->getCurrentPlan();

        // Caso especial: SI-MS es equivalente a SI-FREE + complemento
        if ($currentPlan['planId'] === 'SI-MS' &&
            $this->selectedPlan === 'SI-FREE' &&
            $this->addMsComplement &&
            $currentPlan['billingPeriod'] === $this->billingPeriod) {
            return false; // No hay diferencia, son equivalentes
        }

        // Caso especial: SI-FREE con complemento y cambia a otro plan o periodo
        if ($currentPlan['planId'] === 'SI-MS' &&
            ($this->selectedPlan !== 'SI-FREE' || !$this->addMsComplement || $currentPlan['billingPeriod'] !== $this->billingPeriod)) {
            return true; // Hay diferencia, quiere cambiar de plan o periodo
        }

        // Caso especial: SI-FREE sin complemento y quiere añadirlo
        if ($currentPlan['planId'] === 'SI-FREE' &&
            $this->selectedPlan === 'SI-FREE' &&
            $this->addMsComplement) {
            return true; // Hay diferencia, quiere añadir el complemento
        }

        // Caso normal: Comparación directa de plan y periodo
        return $this->selectedPlan !== $currentPlan['planId'] || $this->billingPeriod !== $currentPlan['billingPeriod'];
    }

    public function render()
    {
        $currentPlan = $this->getCurrentPlan();

        return view('livewire.plan-selector', [
            'discountedPrice' => $this->getDiscountedPrice(),
            'totalPrice' => $this->getTotalPrice(),
            'savings' => $this->getSavingsAmount(),
            'hasValidatedCard' => $this->hasValidatedCard(),
            'isChangingPlan' => $this->isChangingPlan,
            'currentPlanId' => $currentPlan['planId'],
            'currentBillingPeriod' => $currentPlan['billingPeriod'],
            'isPlanDifferent' => $this->isPlanDifferent()
        ]);
    }
}
