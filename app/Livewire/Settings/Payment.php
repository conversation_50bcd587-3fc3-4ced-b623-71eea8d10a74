<?php

namespace App\Livewire\Settings;

use App\Models\ConektaCustomer;
use App\Models\Subscription;
use App\Models\User;
use App\Services\PaymentService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class Payment extends Component
{
    // Datos de facturación
    public $fact_nombre;
    public $fact_domicilio;
    public $fact_rfc;
    
    // Datos de tarjeta (solo para UI)
    public $cardNumber;
    public $cardName;
    public $cardExpiry;
    public $cardCvc;
    
    // Token de Conekta
    public $conektaTokenId;
    
    // Estado de la interfaz
    public $activeTab = 'subscription';
    public $showAddCardForm = false;
    public $showCancelSubscriptionModal = false;
    public $cancelReason = '';
    
    // Mensajes de estado
    public $successMessage = '';
    public $errorMessage = '';
    
    /**
     * Reglas de validación para los datos de facturación
     */
    protected $rules = [
        'fact_nombre' => 'required|string|max:100',
        'fact_domicilio' => 'required|string|max:150',
        'fact_rfc' => 'required|string|min:12|max:13',
    ];
    
    /**
     * Mensajes personalizados para las validaciones
     */
    protected $messages = [
        'fact_nombre.required' => 'El nombre o razón social es obligatorio',
        'fact_domicilio.required' => 'El domicilio fiscal es obligatorio',
        'fact_rfc.required' => 'El RFC es obligatorio',
        'fact_rfc.min' => 'El RFC debe tener al menos 12 caracteres',
        'fact_rfc.max' => 'El RFC no puede tener más de 13 caracteres',
    ];
    
    /**
     * Inicializa el componente con los datos del usuario
     */
    public function mount()
    {
        $user = Auth::user();
        
        // Cargar datos de facturación
        $this->fact_nombre = $user->fact_nombre;
        $this->fact_domicilio = $user->fact_domicilio;
        $this->fact_rfc = $user->fact_rfc;
    }
    
    /**
     * Cambia la pestaña activa
     */
    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }
    
    /**
     * Muestra u oculta el formulario para agregar tarjeta
     */
    public function toggleAddCardForm()
    {
        $this->showAddCardForm = !$this->showAddCardForm;
        $this->resetCardFields();
    }
    
    /**
     * Reinicia los campos del formulario de tarjeta
     */
    private function resetCardFields()
    {
        $this->cardNumber = '';
        $this->cardName = '';
        $this->cardExpiry = '';
        $this->cardCvc = '';
        $this->conektaTokenId = '';
    }
    
    /**
     * Maneja la creación del token de tarjeta desde Conekta
     */
    public function handleTokenCreated($tokenId)
    {
        $this->conektaTokenId = $tokenId;
    }
    
    /**
     * Guarda una nueva tarjeta
     */
    public function saveCard()
    {
        if (empty($this->conektaTokenId)) {
            $this->errorMessage = 'Por favor valide su tarjeta primero';
            return;
        }
        
        $user = Auth::user();
        $paymentService = app(PaymentService::class);
        
        try {
            // Si el usuario ya tiene un cliente en Conekta, actualizamos su método de pago
            if ($user->conektaCustomer) {
                $result = $paymentService->updateCustomerPaymentSource(
                    $user->conektaCustomer->conekta_id,
                    $this->conektaTokenId
                );
                
                if (isset($result['error'])) {
                    $this->errorMessage = 'Error al actualizar la tarjeta: ' . $result['error'];
                    return;
                }
                
                // Actualizar la información del cliente en nuestra base de datos
                $this->updateConektaCustomerInfo($user, $result);
            } else {
                // Si no tiene un cliente en Conekta, lo creamos
                $result = $paymentService->createCustomer(
                    $this->conektaTokenId,
                    $user->email,
                    $user->name
                );
                
                if (isset($result['error'])) {
                    $this->errorMessage = 'Error al guardar la tarjeta: ' . $result['error'];
                    return;
                }
                
                // Crear el registro del cliente en nuestra base de datos
                $this->createConektaCustomerRecord($user, $result);
            }
            
            $this->successMessage = 'Tarjeta guardada correctamente';
            $this->showAddCardForm = false;
            $this->resetCardFields();
            
        } catch (\Exception $e) {
            Log::error('Error al guardar tarjeta: ' . $e->getMessage());
            $this->errorMessage = 'Error al procesar la tarjeta: ' . $e->getMessage();
        }
    }
    
    /**
     * Actualiza la información del cliente de Conekta en la base de datos
     */
    private function updateConektaCustomerInfo($user, $result)
    {
        if (!isset($result['payment_sources']) || empty($result['payment_sources'])) {
            return;
        }
        
        $paymentSource = $result['payment_sources'][0];
        
        // Actualizar el registro existente
        $conektaCustomer = $user->conektaCustomer;
        
        // Agregar la nueva fuente de pago al array existente
        $paymentSources = $conektaCustomer->payment_sources ?? [];
        $newSource = [
            'id' => $paymentSource['id'],
            'brand' => $paymentSource['brand'] ?? '',
            'last4' => $paymentSource['last4'] ?? '',
            'exp_month' => $paymentSource['exp_month'] ?? '',
            'exp_year' => $paymentSource['exp_year'] ?? '',
            'name' => $paymentSource['name'] ?? '',
        ];
        
        // Verificar si ya existe una fuente con el mismo ID
        $exists = false;
        foreach ($paymentSources as $key => $source) {
            if ($source['id'] === $newSource['id']) {
                $paymentSources[$key] = $newSource;
                $exists = true;
                break;
            }
        }
        
        if (!$exists) {
            $paymentSources[] = $newSource;
        }
        
        $conektaCustomer->payment_sources = $paymentSources;
        $conektaCustomer->default_payment_source_id = $paymentSource['id'];
        $conektaCustomer->save();
    }
    
    /**
     * Crea un nuevo registro de cliente de Conekta en la base de datos
     */
    private function createConektaCustomerRecord($user, $result)
    {
        if (!isset($result['id']) || !isset($result['payment_sources']) || empty($result['payment_sources'])) {
            return;
        }
        
        $paymentSource = $result['payment_sources'][0];
        
        ConektaCustomer::create([
            'user_id' => $user->id,
            'conekta_id' => $result['id'],
            'payment_sources' => [
                [
                    'id' => $paymentSource['id'],
                    'brand' => $paymentSource['brand'] ?? '',
                    'last4' => $paymentSource['last4'] ?? '',
                    'exp_month' => $paymentSource['exp_month'] ?? '',
                    'exp_year' => $paymentSource['exp_year'] ?? '',
                    'name' => $paymentSource['name'] ?? '',
                ]
            ],
            'default_payment_source_id' => $paymentSource['id'],
        ]);
    }
    
    /**
     * Establece una tarjeta como predeterminada
     */
    public function setDefaultCard($sourceId)
    {
        $user = Auth::user();
        $conektaCustomer = $user->conektaCustomer;
        
        if (!$conektaCustomer) {
            $this->errorMessage = 'No se encontró información de pago';
            return;
        }
        
        try {
            $paymentService = app(PaymentService::class);
            $result = $paymentService->updateDefaultPaymentSource(
                $conektaCustomer->conekta_id,
                $sourceId
            );
            
            if (isset($result['error'])) {
                $this->errorMessage = 'Error al actualizar la tarjeta predeterminada: ' . $result['error'];
                return;
            }
            
            // Actualizar en nuestra base de datos
            $conektaCustomer->default_payment_source_id = $sourceId;
            $conektaCustomer->save();
            
            $this->successMessage = 'Tarjeta predeterminada actualizada correctamente';
            
        } catch (\Exception $e) {
            Log::error('Error al establecer tarjeta predeterminada: ' . $e->getMessage());
            $this->errorMessage = 'Error al actualizar la tarjeta predeterminada: ' . $e->getMessage();
        }
    }
    
    /**
     * Elimina una tarjeta
     */
    public function deleteCard($sourceId)
    {
        $user = Auth::user();
        $conektaCustomer = $user->conektaCustomer;
        
        if (!$conektaCustomer) {
            $this->errorMessage = 'No se encontró información de pago';
            return;
        }
        
        try {
            $paymentService = app(PaymentService::class);
            $result = $paymentService->deletePaymentSource(
                $conektaCustomer->conekta_id,
                $sourceId
            );
            
            if (isset($result['error'])) {
                $this->errorMessage = 'Error al eliminar la tarjeta: ' . $result['error'];
                return;
            }
            
            // Actualizar en nuestra base de datos
            $paymentSources = $conektaCustomer->payment_sources ?? [];
            $updatedSources = array_filter($paymentSources, function($source) use ($sourceId) {
                return $source['id'] !== $sourceId;
            });
            
            $conektaCustomer->payment_sources = array_values($updatedSources);
            
            // Si la tarjeta eliminada era la predeterminada, actualizar
            if ($conektaCustomer->default_payment_source_id === $sourceId) {
                $conektaCustomer->default_payment_source_id = count($updatedSources) > 0 
                    ? $updatedSources[0]['id'] 
                    : null;
            }
            
            $conektaCustomer->save();
            
            $this->successMessage = 'Tarjeta eliminada correctamente';
            
        } catch (\Exception $e) {
            Log::error('Error al eliminar tarjeta: ' . $e->getMessage());
            $this->errorMessage = 'Error al eliminar la tarjeta: ' . $e->getMessage();
        }
    }
    
    /**
     * Actualiza los datos de facturación
     */
    public function updateBillingInfo()
    {
        $this->validate();
        
        $user = Auth::user();
        $user->fact_nombre = $this->fact_nombre;
        $user->fact_domicilio = $this->fact_domicilio;
        $user->fact_rfc = $this->fact_rfc;
        $user->save();
        
        $this->successMessage = 'Información de facturación actualizada correctamente';
    }
    
    /**
     * Muestra el modal de cancelación de suscripción
     */
    public function showCancelSubscriptionConfirmation()
    {
        $this->showCancelSubscriptionModal = true;
    }
    
    /**
     * Oculta el modal de cancelación de suscripción
     */
    public function hideCancelSubscriptionModal()
    {
        $this->showCancelSubscriptionModal = false;
        $this->cancelReason = '';
    }
    
    /**
     * Cancela la suscripción actual
     */
    public function cancelSubscription()
    {
        $user = Auth::user();
        $subscription = $user->subscription;
        
        if (!$subscription) {
            $this->errorMessage = 'No se encontró una suscripción activa';
            $this->hideCancelSubscriptionModal();
            return;
        }
        
        try {
            $paymentService = app(PaymentService::class);
            $result = $paymentService->cancelSubscription(
                $subscription->conekta_customer,
                $subscription->conekta_id
            );
            
            if (!$result) {
                $this->errorMessage = 'Error al cancelar la suscripción';
                $this->hideCancelSubscriptionModal();
                return;
            }
            
            // Actualizar el estado de la suscripción en nuestra base de datos
            $subscription->status = 'canceled';
            $subscription->ends_at = now();
            $subscription->save();
            
            // Guardar el motivo de cancelación en algún lugar si es necesario
            if (!empty($this->cancelReason)) {
                Log::info('Motivo de cancelación de suscripción: ' . $this->cancelReason, [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id
                ]);
                
                // Aquí podrías guardar el motivo en una tabla de feedback o similar
            }
            
            $this->successMessage = 'Suscripción cancelada correctamente';
            $this->hideCancelSubscriptionModal();
            
        } catch (\Exception $e) {
            Log::error('Error al cancelar suscripción: ' . $e->getMessage());
            $this->errorMessage = 'Error al cancelar la suscripción: ' . $e->getMessage();
            $this->hideCancelSubscriptionModal();
        }
    }
    
    /**
     * Renderiza el componente
     */
    public function render()
    {
        $user = Auth::user();
        
        return view('livewire.settings.payment', [
            'user' => $user,
            'subscription' => $user->subscription,
            'paymentSources' => $user->conektaCustomer ? $user->conektaCustomer->payment_sources : [],
            'defaultSourceId' => $user->conektaCustomer ? $user->conektaCustomer->default_payment_source_id : null,
            'invoices' => [], // Aquí podrías cargar las facturas del usuario si tienes un modelo para eso
        ]);
    }
}
