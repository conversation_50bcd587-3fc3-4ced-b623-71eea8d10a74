<?php

namespace App\Livewire\Traits;

use App\Models\Cobro;
use App\Models\Contrato;
use App\Models\SIConfig;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Webhook;
use App\Services\CobroService;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

trait WebhookGestionTrait
{
    use DepositoGestionTrait;
    
    /**
     * Obtiene una instancia del servicio de cobros
     *
     * @return CobroService
     */
    protected function getCobroService(): CobroService
    {
        return App::make(CobroService::class);
    }
    
    /**
     * Guarda el webhook en la base de datos
     *
     * @param array $payload Los datos del webhook
     * @param string $event El tipo de evento del webhook
     * @param string $origin El origen del webhook (conekta, paypal, etc.)
     * @return void
     */
    protected function guardarWebhook(array $payload, string $event, string $origin = 'conekta'): void
    {
        try {
            // Extraer información relevante del payload
            $object = $payload['object'] ?? ($payload['data']['object'] ?? null);
            $idItem = $object['id'] ?? null;

            // Buscar el cliente_ids y cobro_id si es posible
            $clienteIds = null;
            $cobroId = null;

            // Si es un evento de suscripción, intentar encontrar el cliente
            if (strpos($event, 'subscription.') === 0 && $idItem) {
                $subscription = Subscription::where('conekta_id', $idItem)->first();
                if ($subscription && $subscription->user) {
                    $clienteIds = $subscription->user->usuario ?? null;
                }
            }

            // Guardar el webhook completo
            Webhook::create([
                'cliente_ids' => $clienteIds,
                'cobro_id' => $cobroId,
                'origin' => $origin,
                'type' => $event,
                'id_item' => $idItem,
                'data' => $payload, // Guardar el payload completo
                'created_at' => now()
            ]);

            Log::info('WebhookGestionTrait: Webhook guardado en la base de datos');
        } catch (\Exception $e) {
            Log::error('WebhookGestionTrait: Error al guardar webhook en la base de datos: ' . $e->getMessage());
        }
    }

    /**
     * Extrae el objeto de suscripción del payload del webhook
     * Maneja diferentes estructuras de payload que puede enviar la plataforma de pago
     *
     * @param array $payload
     * @param string $eventType
     * @return array|null
     */
    protected function extraerObjetoSuscripcion(array $payload, string $eventType): ?array
    {
        // Caso 1: El objeto está directamente en la raíz del payload
        if (isset($payload['object']) && is_array($payload['object']) && isset($payload['object']['id'])) {
            return $payload['object'];
        }

        // Caso 2: El objeto está dentro de data.object (formato de webhook más reciente)
        if (isset($payload['data']) && is_array($payload['data']) &&
            isset($payload['data']['object']) && is_array($payload['data']['object']) &&
            isset($payload['data']['object']['id'])) {
            return $payload['data']['object'];
        }

        // Si llegamos aquí, no pudimos encontrar un objeto de suscripción válido
        Log::error('WebhookGestionTrait: Estructura de payload inválida en ' . $eventType . ' webhook', ['payload' => $payload]);
        return null;
    }

    /**
     * Procesa un pago exitoso recibido por webhook
     *
     * @param User $user El usuario asociado al pago
     * @param string $subscriptionId El ID de la suscripción
     * @param array $object El objeto de suscripción del webhook
     * @param string $plataforma La plataforma de pago (Conekta, PayPal, etc.)
     * @return void
     */
    protected function procesarPagoExitoso(User $user, string $subscriptionId, array $object, string $plataforma = 'Conekta'): void
    {
        // Buscar cobros pendientes que tengan este ID de suscripción
        $cobroService = $this->getCobroService();
        $cobros = $cobroService->obtenerPorUsuario($user->usuario)
            ->filter(function($cobro) use ($subscriptionId) {
                $adicional = json_decode($cobro->adicional, true);
                return isset($adicional['subscription_id']) && $adicional['subscription_id'] === $subscriptionId;
            });

        if ($cobros->isEmpty()) {
            Log::warning('WebhookGestionTrait: No se encontraron cobros pendientes asociados a la suscripción', [
                'usuario' => $user->usuario,
                'subscription_id' => $subscriptionId
            ]);
            return;
        }

        foreach ($cobros as $cobro) {
            // Crear un depósito para respaldar el pago
            $deposito = $this->crearDeposito($user, $cobro, $object, $subscriptionId, $plataforma);

            // Marcar el cobro como pagado
            $cobroService->marcarComoPagado(
                $cobro->numero,
                'Tarjeta',
                "Pago confirmado vía webhook de {$plataforma}",
                'sistema'
            );

            // Asociar el cobro con el depósito
            $this->asociarDepositoConCobro($deposito, $cobro);

            Log::info('WebhookGestionTrait: Cobro marcado como pagado vía webhook', [
                'cobro_id' => $cobro->numero,
                'subscription_id' => $subscriptionId,
                'usuario' => $user->usuario,
                'deposito_id' => $deposito->id
            ]);

            // Actualizar el contrato asociado
            $this->actualizarContratoYConfiguracion($cobro);
        }
    }

    /**
     * Actualiza el contrato y la configuración del sitio
     *
     * @param Cobro $cobro El cobro que se acaba de pagar
     * @return void
     */
    protected function actualizarContratoYConfiguracion(Cobro $cobro): void
    {
        // Obtener el contrato
        $contratoId = $cobro->contrato;
        $contrato = Contrato::find($contratoId);

        if (!$contrato) {
            Log::warning('WebhookGestionTrait: No se encontró el contrato asociado al cobro', [
                'cobro_id' => $cobro->numero,
                'contrato_id' => $contratoId
            ]);
            return;
        }

        // Verificar el tipo de dato de $cobro->hasta
        $fechaHasta = $cobro->hasta;
        if (is_string($fechaHasta)) {
            $fechaHasta = Carbon::parse($fechaHasta);
        }

        // Actualizar el contrato
        $contrato->pagado_hasta = $fechaHasta;
        $contrato->save();

        Log::info('WebhookGestionTrait: Contrato actualizado con fecha de pago', [
            'contrato_id' => $contrato->numero,
            'pagado_hasta' => $fechaHasta->format('Y-m-d')
        ]);

        // Actualizar la configuración del sitio si existe
        $siConfig = SIConfig::where('contrato', $contrato->numero)->first();
        if ($siConfig) {
            // Usar la misma fecha convertida
            $siConfig->pagado_hasta = $fechaHasta;
            $siConfig->vencimiento = $fechaHasta;
            $siConfig->save();

            Log::info('WebhookGestionTrait: SIConfig actualizado con fecha de pago', [
                'contrato_id' => $contrato->numero,
                'pagado_hasta' => $fechaHasta->format('Y-m-d')
            ]);
        }
    }

    /**
     * Crea un depósito de reembolso sin asociarlo a un cobro.
     * Se usa cuando se recibe un reembolso por webhook pero no se encuentra un cobro correspondiente.
     *
     * @param string $referenciaReembolso ID del reembolso
     * @param string $idItem ID del objeto original (orden, suscripción, etc.)
     * @param float $monto Monto del reembolso (debe ser positivo, se convertirá a negativo)
     * @param string $plataforma Plataforma de pago (Conekta, PayPal, etc.)
     * @param array|null $datosApi Respuesta completa de la API
     * @return void
     */
    protected function crearReembolsoSinAsociar(
        string $referenciaReembolso,
        string $idItem,
        float $monto,
        string $plataforma = 'Conekta',
        ?array $datosApi = null
    ): void {
        // Intentar encontrar el usuario a través del customer ID de Conekta en el objeto recibido
        $usuario = null;
        $customerId = null;
        
        // Extraer el customer_id del objeto de reembolso si está disponible
        if (isset($datosApi['customer_info']) && isset($datosApi['customer_info']['customer_id'])) {
            $customerId = $datosApi['customer_info']['customer_id'];
        } elseif (isset($datosApi['customer_id'])) {
            $customerId = $datosApi['customer_id'];
        }
        
        // Si tenemos un customer_id, buscar en la tabla conekta_customers
        if ($customerId) {
            Log::info("WebhookGestionTrait: Buscando usuario por Conekta Customer ID", [
                'customer_id' => $customerId
            ]);
            
            // Buscar por conekta_id en ConektaCustomer
            $conektaCustomer = \App\Models\ConektaCustomer::where('conekta_id', $customerId)->first();
            
            if ($conektaCustomer) {
                $usuario = $conektaCustomer->user;
                
                if ($usuario) {
                    Log::info("WebhookGestionTrait: Usuario encontrado por Conekta Customer ID", [
                        'usuario_id' => $usuario->id,
                        'nombre' => $usuario->nombre
                    ]);
                }
            }
        }
        
        // Si no encontramos el usuario por customer_id, intentar por depósitos anteriores
        if (!$usuario) {
            $depositoService = $this->getDepositoService();
            $depositosRelacionados = $depositoService->buscarPorObservaciones($idItem);
            
            if ($depositosRelacionados->isNotEmpty()) {
                // Buscar el primer depósito con un usuario válido
                foreach ($depositosRelacionados as $deposito) {
                    if ($deposito->id_usuario) {
                        $usuario = \App\Models\User::find($deposito->id_usuario);
                        if ($usuario) {
                            Log::info("WebhookGestionTrait: Usuario encontrado por depósito relacionado", [
                                'usuario_id' => $usuario->id,
                                'deposito_id' => $deposito->id
                            ]);
                            break;
                        }
                    }
                }
            }
        }
        
        // Si todavía no encontramos usuario, usar un usuario por defecto para administrador
        if (!$usuario) {
            // Buscamos un usuario administrador o el primero disponible
            $usuario = \App\Models\User::where('id', 1)->first();
            
            if (!$usuario) {
                Log::error("WebhookGestionTrait: No se pudo encontrar un usuario para el reembolso sin asociar", [
                    'referencia_reembolso' => $referenciaReembolso,
                    'id_item' => $idItem
                ]);
                return;
            }
            
            Log::warning("WebhookGestionTrait: Usando usuario administrador por defecto para reembolso sin asociar", [
                'usuario_id' => $usuario->id
            ]);
        }
        
        // Crear el depósito negativo sin asociación con cobro
        $depositoService = $this->getDepositoService();
        $deposito = $depositoService->crearReembolsoAutomatico(
            $usuario,
            abs($monto), // Asegurar que sea positivo, el servicio lo convierte a negativo
            $plataforma,
            $referenciaReembolso,
            $idItem, // Usar el ID del item como referencia original
            $datosApi
        );
        
        Log::info("WebhookGestionTrait: Depósito negativo creado sin asociación a cobro", [
            'deposito_id' => $deposito->id,
            'monto' => $deposito->cantidad,
            'usuario_id' => $usuario->id,
            'referencia_reembolso' => $referenciaReembolso,
            'id_item' => $idItem
        ]);
    }

    /**
     * Procesa un reembolso recibido por webhook
     *
     * @param array $object Objeto del reembolso recibido
     * @param string $referenciaReembolso ID del reembolso
     * @param bool $esTotal Si es un reembolso total o parcial
     * @param string $plataforma La plataforma de pago (Conekta, PayPal, etc.)
     * @return void
     */
    protected function procesarReembolso(array $object, string $referenciaReembolso, bool $esTotal, string $plataforma = 'Conekta'): void
    {
        // Usar la información de reembolso reconstruida si está disponible
        $refundInfo = $object['_refund_info'] ?? null;
        
        $idItem = $object['id'] ?? '';
        // Manejar el monto según la estructura disponible
        if ($refundInfo) {
            $monto = ($refundInfo['amount'] ?? 0) / 100; // Conekta maneja los montos en centavos
        } else {
            $monto = ($object['amount'] ?? 0) / 100; // Fallback
        }
        
        $referenciaOriginal = $object['order_id'] ?? ($object['parent_id'] ?? '');
        // Si no tenemos una referencia original pero tenemos refundInfo, intentar obtenerla de ahí
        if (empty($referenciaOriginal) && $refundInfo && isset($refundInfo['order_id'])) {
            $referenciaOriginal = $refundInfo['order_id'];
        }
        
        Log::info("WebhookGestionTrait: Procesando reembolso reconstruido", [
            'order_id' => $referenciaOriginal,
            'refund_id' => $referenciaReembolso,
            'amount' => $monto
        ]);
        
        Log::info("WebhookGestionTrait: Procesando reembolso de {$plataforma}", [
            'referencia_reembolso' => $referenciaReembolso,
            'id_item' => $idItem,
            'monto' => $monto,
            'es_total' => $esTotal ? 'Sí' : 'No',
            'referencia_original' => $referenciaOriginal
        ]);
        
        // Obtener servicio de cobros
        $cobroService = $this->getCobroService();
        
        // Buscar el cobro de devolución asociado a esta referencia
        $cobro = $cobroService->marcarDevolucionReembolsada(
            $referenciaReembolso,
            "Reembolso " . ($esTotal ? "total" : "parcial") . " confirmado vía webhook de {$plataforma}",
            'sistema'
        );
        
        if ($cobro) {
            Log::info("WebhookGestionTrait: Devolución marcada como reembolsada", [
                'cobro_id' => $cobro->numero,
                'monto' => $cobro->cantidad,
                'referencia_reembolso' => $referenciaReembolso
            ]);
            
            // Buscar el usuario asociado al cobro
            $usuario = $cobro->cliente;
            
            if ($usuario) {
                // Crear también un registro de depósito negativo
                $depositoService = $this->getDepositoService();
                $deposito = $depositoService->crearReembolsoAutomatico(
                    $usuario,
                    abs($cobro->cantidad), // Pasamos el valor absoluto, el método se encargará de hacerlo negativo
                    $plataforma,
                    $referenciaReembolso,
                    $referenciaOriginal,
                    $object
                );
                
                // Asociar el depósito con el cobro de devolución
                $depositoService->asociarCobro($deposito->id, $cobro->numero);
                
                Log::info("WebhookGestionTrait: Depósito negativo creado y asociado a la devolución", [
                    'cobro_id' => $cobro->numero,
                    'deposito_id' => $deposito->id,
                    'monto' => $deposito->cantidad
                ]);
            } else {
                Log::warning("WebhookGestionTrait: No se pudo encontrar el usuario asociado al cobro", [
                    'cobro_id' => $cobro->numero,
                    'usuario' => $cobro->usuario
                ]);
            }
        } else {
            // Si no encontramos un cobro para esta referencia específica, es posible que
            // la referencia de reembolso sea generada (por truncamiento) y debamos buscar por orden_id
            if (isset($refundInfo) && $referenciaReembolso == $object['id'] . '_refund') {
                Log::warning("WebhookGestionTrait: Usando reembolso reconstruido, intentando buscar cobros por orden_id", [
                    'orden_id' => $idItem,
                    'monto' => $monto
                ]);
                
                // Obtener los cobros pendientes para este usuario/monto
                $cobros = Cobro::where('pagado', 'No')
                    ->whereRaw("JSON_CONTAINS(adicional, '\"devolucion\"', '$.tipo')")
                    ->get();
                
                if ($cobros->isNotEmpty()) {
                    $cobroEncontrado = false;
                    
                    foreach ($cobros as $cobroCandidate) {
                        // Si el monto coincide aproximadamente (puede haber pequeñas diferencias)
                        if (abs(abs($cobroCandidate->cantidad) - $monto) < 1) {
                            $cobroReembolsado = $cobroService->marcarComoPagado(
                                $cobroCandidate->numero,
                                'Tarjeta',
                                "Reembolso " . ($esTotal ? "total" : "parcial") . " asociado por monto. Order ID: {$idItem}",
                                'sistema'
                            );
                            
                            if ($cobroReembolsado) {
                                $cobroEncontrado = true;
                                $usuario = $cobroReembolsado->cliente;
                                
                                if ($usuario) {
                                    // Crear depósito negativo
                                    $depositoService = $this->getDepositoService();
                                    $deposito = $depositoService->crearReembolsoAutomatico(
                                        $usuario,
                                        abs($cobroReembolsado->cantidad),
                                        $plataforma,
                                        $referenciaReembolso,
                                        $referenciaOriginal,
                                        $object
                                    );
                                    
                                    // Asociar el depósito con el cobro
                                    $depositoService->asociarCobro($deposito->id, $cobroReembolsado->numero);
                                    
                                    Log::info("WebhookGestionTrait: Cobro encontrado por monto y asociado al reembolso", [
                                        'cobro_id' => $cobroReembolsado->numero,
                                        'deposito_id' => $deposito->id,
                                        'monto' => $deposito->cantidad
                                    ]);
                                }
                                
                                break; // Terminar el bucle una vez que encontramos un cobro válido
                            }
                        }
                    }
                    
                    if (!$cobroEncontrado) {
                        Log::warning("WebhookGestionTrait: No se pudo asociar automáticamente ningún cobro por monto", [
                            'monto_reembolso' => $monto
                        ]);
                        
                        // Crear un depósito sin asociar a cobro
                        $this->crearReembolsoSinAsociar($referenciaReembolso, $idItem, $monto, $plataforma, $object);
                    }
                } else {
                    Log::warning("WebhookGestionTrait: No se encontraron cobros pendientes de tipo devolución", [
                        'orden_id' => $idItem
                    ]);
                    
                    // Crear un depósito sin asociar a cobro
                    $this->crearReembolsoSinAsociar($referenciaReembolso, $idItem, $monto, $plataforma, $object);
                }
            } else {
                Log::warning("WebhookGestionTrait: No se encontró registro de devolución para el reembolso", [
                    'referencia_reembolso' => $referenciaReembolso,
                    'id_item' => $idItem
                ]);
                
                // Crear un depósito sin asociar a cobro
                $this->crearReembolsoSinAsociar($referenciaReembolso, $idItem, $monto, $plataforma, $object);
            }
        }
    }

    /**
     * Procesa múltiples reembolsos de un objeto de orden
     *
     * @param array $object Objeto de orden con reembolsos
     * @param bool $esTotal Si es un reembolso total o parcial
     * @param string $plataforma La plataforma de pago (Conekta, PayPal, etc.)
     * @return void
     */
    protected function procesarReembolsosDeOrden(array $object, bool $esTotal, string $plataforma = 'Conekta'): void
    {
        $orderId = $object['id'] ?? '';
        
        // Verificar si hay reembolsos reconstruidos en la estructura
        if (isset($object['_refund_info'])) {
            $refundInfo = $object['_refund_info'];
            $refundId = $refundInfo['id'] ?? ($orderId . '_refund');
            
            Log::info("WebhookGestionTrait: Procesando reembolso reconstruido", [
                'order_id' => $orderId,
                'refund_id' => $refundId,
                'amount' => ($refundInfo['amount'] ?? 0) / 100
            ]);
            
            // Procesar el reembolso reconstruido
            $this->procesarReembolso($object, $refundId, $esTotal, $plataforma);
            return;
        }
        
        // Caso normal: verificar si hay reembolsos en la estructura estándar
        $refunds = $object['refunds']['data'] ?? [];
        
        if (empty($refunds)) {
            Log::warning("WebhookGestionTrait: No se encontraron detalles de reembolso en la orden", [
                'order_id' => $orderId
            ]);
            return;
        }
        
        foreach ($refunds as $refund) {
            $refundId = $refund['id'] ?? null;
            
            if (!$refundId) {
                continue;
            }
            
            // Procesar cada reembolso individualmente
            $this->procesarReembolso($refund, $refundId, $esTotal, $plataforma);
        }
    }
} 