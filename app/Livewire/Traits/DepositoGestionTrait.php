<?php

namespace App\Livewire\Traits;

use App\Models\Cobro;
use App\Models\Deposito;
use App\Models\User;
use App\Services\DepositoService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

trait DepositoGestionTrait
{
    /**
     * Obtiene una instancia del servicio de depósitos
     *
     * @return DepositoService
     */
    protected function getDepositoService(): DepositoService
    {
        return App::make(DepositoService::class);
    }

    /**
     * Crea un depósito para respaldar el pago de un cobro
     *
     * @param User $user El usuario que realiza el pago
     * @param Cobro $cobro El cobro que se está pagando
     * @param array $object El objeto de suscripción o pago externo
     * @param string $referenciaExterna El ID de la suscripción o transacción externa
     * @param string $plataforma La plataforma de pago (Conekta, PayPal, etc.)
     * @return Deposito El depósito creado
     */
    protected function crearDeposito(
        User $user, 
        Cobro $cobro, 
        array $object, 
        string $referenciaExterna,
        string $plataforma = 'Conekta'
    ): Deposito {
        // Extraer información relevante del objeto
        $monto = $cobro->precio;
        $movimiento = 'Pago de suscripción';

        Log::info("DepositoGestionTrait: Creando depósito para pago vía {$plataforma}", [
            'usuario_id' => $user->id,
            'cobro_id' => $cobro->numero,
            'monto' => $monto,
            'referencia_externa' => $referenciaExterna
        ]);

        // Usar DepositoService para crear el depósito
        $depositoService = $this->getDepositoService();
        $deposito = $depositoService->crearPagoAutomatico(
            $user,
            $monto,
            $plataforma,
            $referenciaExterna,
            $movimiento,
            $object
        );

        Log::info("DepositoGestionTrait: Depósito creado exitosamente", [
            'deposito_id' => $deposito->id,
            'monto' => $monto,
            'usuario_id' => $user->id
        ]);

        return $deposito;
    }

    /**
     * Asocia un depósito con un cobro
     *
     * @param Deposito $deposito El depósito a asociar
     * @param Cobro $cobro El cobro a asociar
     * @return bool Si la asociación fue exitosa
     */
    protected function asociarDepositoConCobro(Deposito $deposito, Cobro $cobro): bool
    {
        Log::info("DepositoGestionTrait: Asociando depósito con cobro", [
            'deposito_id' => $deposito->id,
            'cobro_id' => $cobro->numero
        ]);

        $depositoService = $this->getDepositoService();
        $resultado = $depositoService->asociarCobro($deposito->id, $cobro->numero);

        if ($resultado) {
            Log::info("DepositoGestionTrait: Depósito asociado exitosamente con cobro");
        } else {
            Log::warning("DepositoGestionTrait: No se pudo asociar el depósito con el cobro");
        }

        return $resultado;
    }

    /**
     * Registra un depósito manual y lo asocia con un cobro
     *
     * @param array $datosDeposito Los datos del depósito a crear
     * @param int|null $numeroCobro El número del cobro a asociar (opcional)
     * @return array Contiene el depósito creado y un booleano indicando si se asoció
     */
    protected function registrarDepositoManual(array $datosDeposito, ?int $numeroCobro = null): array
    {
        Log::info("DepositoGestionTrait: Registrando depósito manual", [
            'datos' => array_diff_key($datosDeposito, ['api_response' => '']), // No loguear la respuesta API completa
            'cobro_id' => $numeroCobro
        ]);

        $depositoService = $this->getDepositoService();
        $resultado = $depositoService->registrarYAsociar($datosDeposito, $numeroCobro);

        Log::info("DepositoGestionTrait: Depósito manual registrado", [
            'deposito_id' => $resultado['deposito']->id,
            'asociado' => $resultado['asociado'] ? 'Sí' : 'No'
        ]);

        return $resultado;
    }

    /**
     * Obtiene depósitos por usuario
     *
     * @param int $idUsuario ID del usuario
     * @param bool $soloVerificados Si solo se deben obtener depósitos verificados
     * @return \Illuminate\Support\Collection Colección de depósitos
     */
    protected function obtenerDepositosPorUsuario(int $idUsuario, bool $soloVerificados = false)
    {
        $depositoService = $this->getDepositoService();
        return $depositoService->obtenerPorUsuario($idUsuario, $soloVerificados);
    }

    /**
     * Calcula el monto disponible de un depósito
     *
     * @param int $idDeposito ID del depósito
     * @return float Monto disponible
     */
    protected function calcularMontoDisponible(int $idDeposito): float
    {
        $depositoService = $this->getDepositoService();
        return $depositoService->calcularMontoDisponible($idDeposito);
    }

    /**
     * Obtiene los cobros asociados a un depósito
     *
     * @param int $idDeposito ID del depósito
     * @return \Illuminate\Support\Collection Colección de cobros
     */
    protected function obtenerCobrosDeDeposito(int $idDeposito)
    {
        $depositoService = $this->getDepositoService();
        return $depositoService->obtenerCobrosAsociados($idDeposito);
    }
} 