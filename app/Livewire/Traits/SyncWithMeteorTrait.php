<?php

namespace App\Livewire\Traits;

use App\Models\User;
use App\Services\MeteorSyncService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

trait SyncWithMeteorTrait
{
    protected MeteorSyncService $meteorSyncService;

    /**
     * Inicializa el servicio de sincronización con Meteor
     */
    public function bootSyncWithMeteorTrait(): void
    {
        $this->meteorSyncService = app(MeteorSyncService::class);
    }

    /**
     * Sincroniza automáticamente el usuario después de actualizar su perfil
     *
     * @param User $user Usuario actualizado
     * @param array $camposActualizados Campos que se actualizaron
     * @return bool True si la sincronización fue exitosa
     */
    protected function sincronizarConMeteor(User $user, array $camposActualizados = []): bool
    {
        try {
            Log::info('SyncWithMeteor: Iniciando sincronización automática', [
                'user_id' => $user->id,
                'usuario' => $user->usuario,
                'campos' => $camposActualizados
            ]);

            $result = $this->meteorSyncService->sincronizarUsuario($user, $camposActualizados);

            if ($result) {
                Log::info('SyncWithMeteor: Sincronización exitosa', [
                    'user_id' => $user->id
                ]);
            } else {
                Log::warning('SyncWithMeteor: Sincronización falló pero no crítico', [
                    'user_id' => $user->id
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('SyncWithMeteor: Error en sincronización automática', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // No lanzamos la excepción para no interrumpir el flujo principal
            return false;
        }
    }

    /**
     * Sincroniza específicamente el avatar con Meteor
     *
     * @param User $user Usuario con avatar actualizado
     * @param string $avatarUrl URL del nuevo avatar
     * @return bool True si la sincronización fue exitosa
     */
    protected function sincronizarAvatarConMeteor(User $user, string $avatarUrl): bool
    {
        try {
            Log::info('SyncWithMeteor: Sincronizando avatar específicamente', [
                'user_id' => $user->id,
                'avatar_url' => $avatarUrl
            ]);

            return $this->meteorSyncService->sincronizarAvatar($user, $avatarUrl);

        } catch (\Exception $e) {
            Log::error('SyncWithMeteor: Error sincronizando avatar', [
                'user_id' => $user->id,
                'avatar_url' => $avatarUrl,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Verifica si el usuario existe en Meteor
     *
     * @param User $user Usuario a verificar
     * @return bool True si existe en Meteor
     */
    protected function usuarioExisteEnMeteor(User $user): bool
    {
        try {
            return $this->meteorSyncService->usuarioExisteEnMeteor($user);
        } catch (\Exception $e) {
            Log::error('SyncWithMeteor: Error verificando existencia en Meteor', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Crea un usuario en Meteor si no existe
     *
     * @param User $user Usuario a crear
     * @param string $password Contraseña del usuario
     * @param array $datosAdicionales Datos adicionales para el perfil
     * @return array Resultado de la creación
     */
    protected function crearUsuarioEnMeteor(User $user, string $password, array $datosAdicionales = []): array
    {
        try {
            Log::info('SyncWithMeteor: Creando usuario en Meteor', [
                'user_id' => $user->id,
                'usuario' => $user->usuario
            ]);

            return $this->meteorSyncService->crearUsuarioEnMeteor($user, $password, $datosAdicionales);

        } catch (\Exception $e) {
            Log::error('SyncWithMeteor: Error creando usuario en Meteor', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Error creando usuario: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verifica si un username está disponible en Meteor
     *
     * @param string $username Username a verificar
     * @return array Resultado de la verificación
     */
    protected function verificarUsernameEnMeteor(string $username): array
    {
        try {
            return $this->meteorSyncService->verificarUsername($username);
        } catch (\Exception $e) {
            Log::error('SyncWithMeteor: Error verificando username', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Error verificando username: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Sincroniza automáticamente el usuario actual después de cambios
     * Esta función se puede llamar desde cualquier método de update
     *
     * @param array $camposActualizados Opcional: especificar qué campos se actualizaron
     * @return bool True si la sincronización fue exitosa
     */
    protected function autoSyncWithMeteor(array $camposActualizados = []): bool
    {
        $user = Auth::user();

        if (!$user) {
            Log::warning('SyncWithMeteor: No hay usuario autenticado para sincronizar');
            return false;
        }

        return $this->sincronizarConMeteor($user, $camposActualizados);
    }

    /**
     * Hook para ser llamado después de actualizar el perfil
     * Se puede sobrescribir en las clases que usen este trait
     *
     * @param User $user Usuario actualizado
     * @param array $camposActualizados Campos actualizados
     */
    protected function afterProfileUpdate(User $user, array $camposActualizados = []): void
    {
        // Sincronizar automáticamente con Meteor
        $this->sincronizarConMeteor($user, $camposActualizados);
    }

    /**
     * Hook para ser llamado después de actualizar el avatar
     * Se puede sobrescribir en las clases que usen este trait
     *
     * @param User $user Usuario con avatar actualizado
     * @param string $avatarUrl URL del nuevo avatar
     */
    protected function afterAvatarUpdate(User $user, string $avatarUrl): void
    {
        // Sincronizar específicamente el avatar con Meteor
        $this->sincronizarAvatarConMeteor($user, $avatarUrl);
    }
}
