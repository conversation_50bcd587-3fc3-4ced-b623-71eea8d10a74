<?php

namespace App\Livewire\Traits;

use App\Models\Cobro;
use App\Models\Contrato;
use App\Models\Servicio;
use App\Models\SIConfig;
use App\Models\User;
use App\Services\CobroService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

trait UpdateContratoYCobroTrait
{
    // Servicio de Cobros
    protected $cobroService;

    /**
     * Constructor del componente
     *
     * @param CobroService $cobroService
     */
    public function boot(CobroService $cobroService)
    {
        $this->cobroService = $cobroService;
    }

    /**
     * Actualiza el contrato existente y crea un cobro prorrateado
     */
    protected function updateContratoYCobro($user, $currentPlanId, $currentBillingPeriod, $subscription): void
    {
        Log::info('ChangePlanForm › updateContratoYCobro: Actualizando contrato y cobro', [
            'user_id' => $user->id,
            'current_plan' => $currentPlanId,
            'new_plan' => $this->selectedPlan,
            'subscription_id' => $subscription->id
        ]);

        // Buscar el contrato existente del usuario relacionado con el plan de Sistema Inmobiliario
        $contrato = $user->contratos()
            ->where('status', 1)
            ->whereHas('servicioRelacion', function ($query) {
                $query->where('tipo', 'PLAN-SI');
            })
            ->first();

        if (!$contrato) {
            Log::error('ChangePlanForm › updateContratoYCobro: No se encontró el contrato para actualizar');
            return;
        }

        // Obtener el servicio correspondiente al nuevo plan seleccionado
        if ($this->selectedPlan === 'SI-FREE' && $this->addMsComplement) {
            // Si es plan FREE con complemento SI-MS, usamos el servicio SI-MS
            $servicioCode = 'SI-MS';
        } else {
            $servicioCode = $this->selectedPlan;
        }
        $servicio = Servicio::where('servicio', $servicioCode)->first();

        if (!$servicio) {
            Log::error('ChangePlanForm › updateContratoYCobro: No se encontró el servicio para el nuevo plan');
            return;
        }

        // Determinar la frecuencia basada en el período de facturación
        $frequency = match ($this->billingPeriod) {
            'quarterly' => 3,
            'biannual' => 6,
            'annual' => 12,
            default => 1, // monthly
        };

        // Calcular el porcentaje de descuento según la frecuencia
        $discount = match ($this->billingPeriod) {
            'quarterly' => $servicio->desc_trimestral,
            'biannual' => $servicio->desc_semestral,
            'annual' => $servicio->desc_anual,
            default => 0, // Sin descuento para mensual
        };

        // Verificar si hay un cobro pendiente que necesitamos cancelar o ajustar
        $cobroPendiente = $this->cobroService->obtenerPorContrato($contrato->numero, true)
            ->where('pagado', 'Si') // Buscamos cobros pagados vigentes
            ->where('hasta', '>', now())
            ->sortByDesc('numero')
            ->first();

        if (!$cobroPendiente) {
            // Si no encontramos cobros pagados, buscamos cobros pendientes
            $cobroPendiente = $this->cobroService->obtenerPorContrato($contrato->numero)
                ->where('hasta', '>', now())
                ->sortByDesc('numero')
                ->first();
        }

        // Determinar si es un upgrade o downgrade
        $isUpgrade = $this->esPlanSuperior($servicioCode, $currentPlanId);

        // Actualizar el contrato con los nuevos valores
        $contrato->servicio = $servicioCode;
        $contrato->forma_pago = $frequency;
        $contrato->en_precio = $discount;
        $contrato->save();

        Log::info('ChangePlanForm › updateContratoYCobro: Contrato actualizado', [
            'contrato_id' => $contrato->numero,
            'servicio' => $servicioCode,
            'forma_pago' => $frequency
        ]);

        // Si es un downgrade y hay un cobro vigente, solo creamos un registro de control
        // y no generamos un nuevo cobro
        if (!$isUpgrade && $cobroPendiente) {
            Log::info(
                'ChangePlanForm › updateContratoYCobro: Downgrade de plan - manteniendo cobro actual hasta su vencimiento',
                [
                    'contrato_id' => $contrato->numero,
                    'cobro_actual_id' => $cobroPendiente->numero,
                    'fecha_vencimiento' => $cobroPendiente->hasta->format('Y-m-d'),
                    'servicio_actual' => $cobroPendiente->servicio,
                    'nuevo_servicio' => $servicioCode,
                    'cobro_pagado' => $cobroPendiente->pagado
                ]
            );

            // Guardamos la información del downgrade en el adicional del cobro existente
            // para futuras referencias sin crear un nuevo registro
            $adicional = json_decode($cobroPendiente->adicional ?? '{}', true) ?: [];
            $adicional['downgrade_info'] = [
                'plan_anterior' => $currentPlanId,
                'plan_nuevo' => $servicioCode,
                'fecha_cambio' => now()->format('Y-m-d H:i:s'),
                'tipo_cambio' => 'downgrade',
                'activo_hasta' => $cobroPendiente->hasta->format('Y-m-d H:i:s'),
                'siguiente_cobro' => $cobroPendiente->hasta->format('Y-m-d H:i:s')
            ];

            // Usar cobroService para actualizar el cobro
            $this->cobroService->actualizar($cobroPendiente->numero, [
                'adicional' => json_encode($adicional)
            ], 'sistema');

            Log::info('ChangePlanForm › updateContratoYCobro: Información de downgrade guardada en cobro existente', [
                'cobro_id' => $cobroPendiente->numero,
                'plan_anterior' => $currentPlanId,
                'plan_nuevo' => $servicioCode,
                'activo_hasta' => $cobroPendiente->hasta->format('Y-m-d')
            ]);

            return;
        }

        // A partir de aquí el código solo se ejecuta para upgrades o cuando no hay cobro pendiente

        // Calcular fechas para el nuevo cobro
        $fechaInicio = now();
        $fechaFin = match ($this->billingPeriod) {
            'quarterly' => $fechaInicio->copy()->addMonths(3),
            'biannual' => $fechaInicio->copy()->addMonths(6),
            'annual' => $fechaInicio->copy()->addYear(),
            default => $fechaInicio->copy()->addMonth(), // monthly
        };

        // Calcular el prorrateo si hay un cobro pendiente (solo para upgrades)
        $montoProrrateado = 0;

        if ($cobroPendiente && $isUpgrade) {
            // Calcular los días restantes del período anterior
            $diasRestantes = now()->diffInDays($cobroPendiente->hasta, false);
            $diasTotales = $cobroPendiente->desde->diffInDays($cobroPendiente->hasta) + 1;

            if ($diasRestantes > 0 && $diasTotales > 0) {
                // Calcular el valor diario del plan anterior
                $valorDiarioAnterior = $cobroPendiente->precio / $diasTotales;
                // Calcular el crédito por los días no utilizados
                $montoProrrateado = round($valorDiarioAnterior * $diasRestantes);

                Log::info('ChangePlanForm › updateContratoYCobro: Prorrateo calculado', [
                    'cobro_id' => $cobroPendiente->numero,
                    'dias_restantes' => $diasRestantes,
                    'dias_totales' => $diasTotales,
                    'valor_diario' => $valorDiarioAnterior,
                    'monto_prorrateado' => $montoProrrateado,
                    'es_upgrade' => $isUpgrade ? 'Sí' : 'No'
                ]);

                // Marcar el cobro anterior como cancelado/pagado con observación usando cobroService
                $this->cobroService->marcarComoPagado(
                    $cobroPendiente->numero,
                    'Tarjeta',
                    'Cancelado automáticamente por upgrade de plan. Prorrateado al nuevo plan.',
                    'sistema'
                );

                // Crear un registro en por_cobrar con el saldo a favor
                $this->crearCobroSaldoAFavor($contrato, $user, $currentPlanId, $montoProrrateado, $cobroPendiente);
            }
        } else {
            // Incluso si no hay cobro pendiente, creamos un registro con monto 0 solo para upgrades
            if ($isUpgrade) {
                $this->crearCobroSaldoAFavor($contrato, $user, $currentPlanId, 0, null);
            }
        }

        // Determinar la descripción del cobro según el plan y período
        $periodoTexto = match ($this->billingPeriod) {
            'quarterly' => 'trimestral',
            'biannual' => 'semestral',
            'annual' => 'anual',
            default => 'mensual',
        };

        if ($this->selectedPlan === 'SI-FREE' && $this->addMsComplement) {
            $planTexto = 'Freemium con ' . $this->msComplementName;
        } else {
            $planTexto = match ($this->selectedPlan) {
                'SI-FREE' => 'Freemium',
                'SI-PRO' => 'PRO',
                'SI-PLUS' => 'PLUS',
                default => ucfirst($this->selectedPlan),
            };
        }

        // Calcular el precio final con el prorrateo (solo para upgrades)
        $precioFinal = $this->totalPrice;

        // Descripción con información de prorrateo si aplica
        $descripcion = "Servicio $planTexto con facturación $periodoTexto";

        // Crear nuevo cobro (solo para upgrades o nuevos planes) usando cobroService
        $datosCobro = [
            'contrato' => $contrato->numero,
            'fecha' => now(),
            'usuario' => $user->usuario,
            'servicio' => $servicioCode,
            'cantidad' => 1,
            'descripcion' => $descripcion,
            'dominio' => $contrato->dominio,
            'adicional' => json_encode([
                'plan' => $this->selectedPlan,
                'periodo' => $this->billingPeriod,
                'subscription_id' => $subscription->conekta_id,
                'frequency' => $frequency,
                'descuento' => $discount,
                'prorrateo' => $isUpgrade && $montoProrrateado > 0 ? $montoProrrateado : null,
                'addMsComplement' => $this->addMsComplement,
                'msComplementPrice' => $this->msComplementPrice,
                'msComplementName' => $this->msComplementName,
                'es_upgrade' => $isUpgrade
            ]),
            'precio' => $precioFinal,
            'desde' => $fechaInicio,
            'hasta' => $fechaFin,
            'pagado' => ($precioFinal === 0) ? 'Si' : 'No',
            'factura' => '',
            'forma_pago' => 'Tarjeta',
            'observaciones_pago' => ($precioFinal === 0)
                ? 'Cobro gratuito o compensado por prorrateo. Marcado como pagado automáticamente.'
                : 'Cobro generado automáticamente en el proceso de cambio de plan',
            'vencimiento' => $fechaInicio->copy()->addDays($contrato->dias_tregua),
            'ultima_notificacion' => now(),
            'detener' => 'No',
        ];

        $cobro = $this->cobroService->crear($datosCobro, 'sistema');

        Log::info('ChangePlanForm › updateContratoYCobro: Nuevo cobro creado exitosamente', [
            'cobro_id' => $cobro->numero,
            'contrato_id' => $contrato->numero,
            'precio' => $precioFinal,
            'prorrateo' => $isUpgrade ? $montoProrrateado : 0,
            'subscription_id' => $subscription->conekta_id,
            'pagado' => ($precioFinal === 0) ? 'Si' : 'No',
            'es_upgrade' => $isUpgrade ? 'Sí' : 'No'
        ]);

        // Actualizar SIConfig si existe
        $this->actualizarSIConfig($contrato, $servicioCode, $fechaFin);

        // Actualizar el campo pagado_hasta del contrato
        $contrato->pagado_hasta = $fechaFin;
        $contrato->save();
    }

    /**
     * Crea un registro en por_cobrar con el saldo a favor por cambio de plan
     *
     * @param Contrato $contrato
     * @param User $user
     * @param string $servicioCode
     * @param float $montoProrrateado
     * @param Cobro|null $cobroPendiente
     * @return void
     */
    protected function crearCobroSaldoAFavor(
        Contrato $contrato,
        User $user,
        string $servicioCode,
        float $montoProrrateado,
        ?Cobro $cobroPendiente = null
    ): void {
        // Corregimos las fechas: desde = fecha fin del pago vigente, hasta = fecha del cambio
        $fechaInicio = now();
        $fechaFin = $cobroPendiente ? $cobroPendiente->hasta : $fechaInicio->copy()->addMonth();

        // Crear un nuevo registro de saldo a favor con precio negativo usando cobroService
        $datosSaldoAFavor = [
            'contrato' => $contrato->numero,
            'fecha' => now(),
            'usuario' => $user->usuario,
            'servicio' => $servicioCode,
            'cantidad' => 1,
            'descripcion' => $montoProrrateado > 0
                ? "Saldo a favor por upgrade de plan - {$montoProrrateado} MXN"
                : "Registro de control por upgrade de plan sin saldo a favor",
            'dominio' => $contrato->dominio,
            'adicional' => json_encode([
                'tipo_registro' => 'saldo_a_favor',
                'cobro_origen' => $cobroPendiente ? $cobroPendiente->numero : null,
                'cambio_plan' => [
                    'plan_anterior' => $cobroPendiente ? $cobroPendiente->servicio : null,
                    'plan_nuevo' => $servicioCode,
                    'fecha_cambio' => now()->format('Y-m-d H:i:s'),
                    'tipo_cambio' => 'upgrade'
                ]
            ]),
            'precio' => $montoProrrateado > 0 ? -$montoProrrateado : 0, // Precio negativo para indicar saldo a favor
            'desde' => $fechaFin, // Invertimos las fechas: desde = fecha fin del pago vigente
            'hasta' => $fechaInicio, // Invertimos las fechas: hasta = fecha del cambio (ahora)
            'pagado' => !$montoProrrateado ? 'Si' : 'No',
            'factura' => '',
            'forma_pago' => 'Saldo a favor',
            'observaciones_pago' => $montoProrrateado > 0
                ? "Saldo a favor generado automáticamente por upgrade de plan. El cambio se realizó el {$fechaInicio->format('d-m-Y')} y su plan anterior estaba vigente hasta {$fechaFin->format('d-m-Y')}."
                : "Registro de control por upgrade de plan sin saldo a favor",
            'vencimiento' => $fechaInicio, // El vencimiento es ahora (fecha del cambio)
            'ultima_notificacion' => now(),
            'detener' => 'No',
        ];

        $cobroSaldoAFavor = $this->cobroService->crear($datosSaldoAFavor, 'sistema');

        Log::info('ChangePlanForm › crearCobroSaldoAFavor: Cobro de saldo a favor por upgrade creado exitosamente', [
            'cobro_id' => $cobroSaldoAFavor->numero,
            'contrato_id' => $contrato->numero,
            'precio' => $montoProrrateado > 0 ? -$montoProrrateado : 0,
            'cobro_origen' => $cobroPendiente ? $cobroPendiente->numero : null,
            'desde' => $fechaFin->format('Y-m-d'),
            'hasta' => $fechaInicio->format('Y-m-d')
        ]);
    }

    /**
     * Actualiza el registro de SIConfig
     *
     * @param Contrato $contrato
     * @param string $servicioCode
     * @param Carbon $fechaFin
     * @return void
     */
    protected function actualizarSIConfig(Contrato $contrato, string $servicioCode, Carbon $fechaFin): void
    {
        $siConfig = SIConfig::where('contrato', $contrato->numero)->first();
        if ($siConfig) {
            $siConfig->servicio_contratado = $servicioCode;
            $siConfig->pagado_hasta = $fechaFin->format('Y-m-d');
            $siConfig->vencimiento = $fechaFin->format('Y-m-d');
            $siConfig->custom_data = json_encode([
                'plan' => $this->selectedPlan,
                'billing_period' => $this->billingPeriod,
                'last_plan_change' => now()->format('Y-m-d H:i:s'),
                'addMsComplement' => $this->addMsComplement,
                'msComplementPrice' => $this->msComplementPrice,
                'msComplementName' => $this->msComplementName,
                'msComplementDescription' => $this->msComplementDescription
            ]);
            $siConfig->save();

            Log::info('ChangePlanForm › actualizarSIConfig: SIConfig actualizado', [
                'contrato_id' => $contrato->numero,
                'pagado_hasta' => $fechaFin->format('Y-m-d')
            ]);
        }
    }

}
