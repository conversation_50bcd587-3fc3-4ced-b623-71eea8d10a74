<?php

namespace App\Livewire\Traits;

use App\Models\Contrato;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Log;

trait CreateUserInMeteorTrait
{
    /**
     * Crea el usuario en Meteor a través de la API REST
     *
     * @param Contrato $contrato El contrato creado
     * @param User $user El usuario para crear en Meteor
     * @param array $activationData Datos de activación del plan
     * @return bool True si se creó exitosamente, false en caso contrario
     */
    protected function createUserInMeteor(Contrato $contrato, User $user, array $activationData): bool
    {
        try {
            Log::info('ActivationForm: Creando usuario en Meteor', [
                'user_id' => $user->id,
                'usuario' => $user->usuario,
                'contrato_id' => $contrato->numero
            ]);

            // Configurar la URL base de la API de Meteor
            $meteorApiUrl = config('services.meteor.api_url', 'https://ws-si.mulb.in/api/');
            $apiKey = config('services.meteor.api_key', 'dev-api-key-mulbin-2024');

            // Preparar los datos del usuario para Meteor
            $userData = [
                'email' => $user->email,
                'password' => $contrato->s_password, // Usar la password generada del contrato
                'username' => $user->usuario,
                'profile' => [
                    'firstName' => $user->nombres ?? '',
                    'lastName' => $user->apellidos ?? '',
                    'phone' => $user->telefono ?? '',
                    'company' => $user->empresa ?? '',
                    'location' => $user->estado ?? '',
                    'city' => $user->ciudad ?? '',
                    'contratoId' => $contrato->numero,
                    'servicioType' => $contrato->servicio,
                    'dominio' => $contrato->dominio,
                    'activationData' => [
                        'plan' => $activationData['selectedPlan'],
                        'billingPeriod' => $activationData['billingPeriod'],
                        'activationDate' => now()->format('Y-m-d H:i:s')
                    ]
                ],
                'roles' => ['user']
            ];

            // Realizar la petición HTTP a la API de Meteor
            $response = $this->sendMeteorApiRequest($meteorApiUrl . 'users', 'POST', $userData, $apiKey);

            if ($response['success']) {
                Log::info('ActivationForm: Usuario creado exitosamente en Meteor', [
                    'user_id' => $user->id,
                    'meteor_user_id' => $response['data']['userId'] ?? null,
                    'contrato_id' => $contrato->numero
                ]);

                // Actualizar el contrato con el ID del usuario de Meteor si está disponible
                if (isset($response['data']['userId'])) {
                    $contrato->update([
                        'observaciones' => $contrato->observaciones . ' | Meteor User ID: ' . $response['data']['userId']
                    ]);
                }

                return true;
            } else {
                Log::error('ActivationForm: Error al crear usuario en Meteor', [
                    'user_id' => $user->id,
                    'error' => $response['error'] ?? 'Error desconocido',
                    'http_code' => $response['http_code'] ?? null,
                    'contrato_id' => $contrato->numero
                ]);

                return false;
            }
        } catch (Exception $e) {
            Log::error('ActivationForm: Excepción al crear usuario en Meteor', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'contrato_id' => $contrato->numero
            ]);

            return false;
        }
    }

    /**
     * Envía una petición HTTP a la API de Meteor
     *
     * @param string $url URL completa del endpoint
     * @param string $method Método HTTP (GET, POST, PUT, DELETE)
     * @param array|null $data Datos a enviar en el body
     * @param string $apiKey API Key para autenticación
     * @return array Respuesta con success, data, error y http_code
     */
    protected function sendMeteorApiRequest(
        string $url,
        string $method,
        ?array $data = null,
        string $apiKey = ''
    ): array {
        try {
            $headers = [
                'Authorization: ApiKey ' . $apiKey,
                'Content-Type: application/json',
                'Accept: application/json'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                return [
                    'success' => false,
                    'error' => 'cURL Error: ' . $curlError,
                    'http_code' => 0,
                    'data' => null
                ];
            }

            $decodedResponse = json_decode($response, true);

            if ($httpCode >= 200 && $httpCode < 300) {
                return [
                    'success' => true,
                    'data' => $decodedResponse,
                    'http_code' => $httpCode,
                    'error' => null
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $decodedResponse['error'] ?? 'HTTP Error ' . $httpCode,
                    'http_code' => $httpCode,
                    'data' => $decodedResponse
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage(),
                'http_code' => 0,
                'data' => null
            ];
        }
    }
}