<?php

namespace App\Livewire\Auth;

use App\Models\User;
use Exception;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Validate;
use Livewire\Component;
use RuntimeException;

class Login extends Component
{
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    public ?string $error = null;

    /**
     * Handle an incoming authentication request.
     *
     * @return RedirectResponse|void
     */
    public function login()
    {
        // Validación personalizada para el campo email/usuario
        $rules = [
            'password' => 'required|string',
        ];

        // Si el campo contiene un @, validar como email
        if (str_contains($this->email, '@')) {
            $rules['email'] = 'required|string|email';
        } else {
            // Validar como slug (alfanumérico con guiones y guiones bajos)
            $rules['email'] = 'required|string|regex:/^[a-zA-Z0-9_-]+$/';
        }

        $messages = [];
        if (str_contains($this->email, '@')) {
            $messages['email.email'] = 'El campo de correo electrónico debe ser una dirección de correo válida.';
        } else {
            $messages['email.regex'] = 'El nombre de usuario solo puede contener letras, números, guiones y guiones bajos.';
        }

        $this->validate($rules, $messages);

        $this->ensureIsNotRateLimited();

        try {
            // No existe el email, o la contraseña es incorrecta, pero sí está encriptada correctamente por Laravel.
            if (
                !Auth::attempt(['logmail' => $this->email, 'password' => $this->password], $this->remember) &&
                !Auth::attempt(['usuario' => $this->email, 'password' => $this->password], $this->remember)
            ) {
                // Si la autenticación normal falla, intentamos con el metodo para contraseñas antiguas
                $user = User::where('logmail', $this->email)->first();

                if (!$user) {
                    // Recupero el usuario con las condiciones necesarias para poder actualizar la contraseña
                    $user = User::where('email', $this->email)
                        ->where('activo', 'Si')
                        ->whereHas('contratos', function ($query) {
                            $query->where('status', 1)
                                ->where('servicio', 'LIKE', 'SI-%');
                        })
                        ->first();

                    if ($user && $user->checkLegacyPassword($this->password)) {
                        // Iniciar sesión manualmente
                        Auth::login($user, $this->remember);

                        // Actualizar la contraseña al formato Bcrypt para futuras autenticaciones
                        $user->password = Hash::make($this->password);
                        $user->logmail = $this->email;
                        $user->name = $user->nombre . ' ' . $user->apellidos;
                        $user->save();

                        throw ValidationException::withMessages([
                            // El mensaje debe decir que se ha actualizado la contraseña
                            'email' => 'Tu acceso se ha integrado con éxito a la nueva plataforma. Por favor, inicia sesión con tu correo electrónico y contraseña nuevamente.',
                        ]);
                    }
                }

                RateLimiter::hit($this->throttleKey());

                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
            }

            // Verificar si el usuario autenticado está activo
            $authenticatedUser = Auth::user();
            if ($authenticatedUser && $authenticatedUser->activo == 'No') {
                Auth::logout();
                $this->error = 'Tu cuenta no está activa. Por favor, contacta al administrador para activarla.';
                return;
            }

            RateLimiter::clear($this->throttleKey());
            Session::regenerate();

            // Intentar redirección directa en lugar de usar navigate: true
            return redirect()->intended(route('dashboard'));
        } catch (RuntimeException $e) {
            // La contraseña no es válida para el algoritmo Bcrypt (encriptación antigua)
            // Capturar específicamente errores relacionados con el algoritmo de hash
            if (str_contains($e->getMessage(), 'Bcrypt')) {
                // Intentar autenticar con el método para contraseñas antiguas
                $user = User::where('logmail', $this->email)->first();

                if ($user && $user->checkLegacyPassword($this->password)) {
                    // Iniciar sesión manualmente
                    Auth::login($user, $this->remember);

                    // Actualizar la contraseña al formato Bcrypt para futuras autenticaciones
                    $user->password = Hash::make($this->password);
                    $user->save();

                    RateLimiter::clear($this->throttleKey());
                    Session::regenerate();

                    return redirect()->intended(route('dashboard'));
                }

                // Si no es una contraseña antigua válida, mostrar el mensaje de error
                $this->error = 'El sistema no puede validar tu contraseña. Por favor, usa el enlace "¿Olvidaste tu contraseña?" para crear una nueva.';
                return;
            }

            // Relanzar cualquier otro error de tiempo de ejecución
            throw $e;
        } catch (Exception $e) {
            // Capturar cualquier otra excepción
            $this->error = $e->getMessage();
            return;
        }
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email) . '|' . request()->ip());
    }

    public function render(): View
    {
        return view('livewire.auth.login')
            ->layout('components.layouts.auth-meta');
    }
}