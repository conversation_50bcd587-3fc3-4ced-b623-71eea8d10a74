<?php

namespace App\Http\Controllers;

use App\Traits\ActivateTrait;
use App\Traits\CambiaPlanTrait;
use App\Traits\UpdateOrCreateConektaCustomerTrait;

class SistemaInmobiliarioController extends Controller
{
    use UpdateOrCreateConektaCustomerTrait,
        ActivateTrait,
        CambiaPlanTrait;

    /**
     * Muestra la página principal del Sistema Inmobiliario
     */
    public function index()
    {
        return view('sistema-inmobiliario.index');
    }

    /**
     * Muestra la página de aviso de privacidad (requerida por Facebook)
     */
    public function avisoPrivacidad()
    {
        return view('legal.aviso-privacidad');
    }

    /**
     * Muestra la página de eliminación de datos (requerida por Facebook)
     */
    public function eliminacionDatos()
    {
        return view('legal.eliminacion-datos');
    }

    /**
     * Muestra la página de términos de uso
     */
    public function terminosUso()
    {
        return view('legal.terminos-uso');
    }
}