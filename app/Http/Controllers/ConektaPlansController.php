<?php

namespace App\Http\Controllers;

use Conekta\Conekta;
use Conekta\Plan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConektaPlansController extends Controller
{
    /**
     * Obtiene los planes de Conekta usando curl directamente
     */
    private function getPlansWithCurl()
    {
        try {
            Log::info('Obteniendo planes con curl');

            $apiKey = env('CONEKTA_PRIVATE_KEY');
            $url = 'https://api.conekta.io/plans';
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/vnd.conekta-v2.1.0+json',
                'Content-Type: application/json',
                'Authorization: Basic ' . base64_encode($apiKey . ':')
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            Log::info('Respuesta curl: HTTP ' . $httpCode);

            if ($httpCode >= 200 && $httpCode < 300) {
                $responseData = json_decode($response, true);

                if (isset($responseData['data']) && is_array($responseData['data'])) {
                    Log::info('Planes obtenidos con curl: ' . count($responseData['data']));

                    // Convertir el array asociativo a objetos para mantener consistencia
                    $plans = [];
                    foreach ($responseData['data'] as $planData) {
                        $plans[] = json_decode(json_encode($planData));
                    }

                    return $plans;
                } else {
                    Log::warning('La respuesta de curl no contiene un array de planes');
                    return [];
                }
            } else {
                Log::warning('Error al obtener planes con curl: HTTP ' . $httpCode . ' - ' . $response);
                return [];
            }
        } catch (\Exception $e) {
            Log::error('Error al obtener planes con curl: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtiene los planes desde la base de datos
     */
    private function getPlansFromDatabase()
    {
        try {
            Log::info('Obteniendo planes desde la base de datos');
            $plans = DB::table('plans')
                ->select('id', 'conekta_plan_id', 'name', 'amount', 'currency', 'interval', 'frequency', 'service_id', 'is_active', 'description')
                ->where('is_active', true)
                ->get();

            Log::info('Planes obtenidos desde la base de datos: ' . count($plans));
            
            // Convertimos los resultados a un formato adecuado para la vista
            $formattedPlans = [];
            foreach ($plans as $plan) {
                // Solo incluimos en el array los planes que tienen un ID de Conekta
                // ya que son los que están sincronizados con la plataforma de pago
                // if (!empty($plan->conekta_plan_id)) {
                    $formattedPlans[] = (object)[
                        'id' => $plan->conekta_plan_id ?? 'NULL',
                        'name' => $plan->name,
                        'amount' => $plan->amount,
                        'currency' => $plan->currency,
                        'interval' => $plan->interval,
                        'frequency' => $plan->frequency,
                        'fromDatabase' => true,
                        'description' => $plan->description
                    ];
                // }
            }

            return $formattedPlans;
        } catch (\Exception $e) {
            Log::error('Error al obtener planes desde la base de datos: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Muestra los planes disponibles en Conekta
     */
    public function index()
    {
        try {
            // Configurar Conekta
            Conekta::setApiKey(env('CONEKTA_PRIVATE_KEY'));
            Conekta::setApiVersion('2.1.0');

            // Obtener los planes de la base de datos
            $databasePlans = $this->getPlansFromDatabase();

            // Obtener los planes de Conekta
            try {
                Log::info('Obteniendo planes de Conekta');

                // Intentar obtener los planes usando curl directamente
                $conektaPlans = $this->getPlansWithCurl();

                if (empty($conektaPlans)) {
                    // Si no se obtuvieron planes con curl, intentar con el SDK
                    Log::info('Intentando obtener planes con el SDK de Conekta');
                    $plansResponse = Plan::where(['limit' => 100]);

                    // Depurar la respuesta completa
                    Log::info('Respuesta de Conekta SDK: ' . json_encode($plansResponse));

                    // Verificar si la respuesta es un objeto
                    if (is_object($plansResponse)) {
                        // Obtener los datos como array desde la respuesta
                        $responseArray = json_decode(json_encode($plansResponse), true);
                        
                        // Verificar si hay datos en el array
                        if (isset($responseArray['data']) && is_array($responseArray['data'])) {
                            $conektaPlans = $responseArray['data'];
                            Log::info('Planes obtenidos con SDK: ' . count($conektaPlans));
                        } else {
                            $conektaPlans = $responseArray;
                            Log::info('Planes obtenidos con SDK (array convertido): ' . (is_array($conektaPlans) ? count($conektaPlans) : 'no es un array'));
                        }
                    } else if (is_array($plansResponse)) {
                        $conektaPlans = $plansResponse;
                        Log::info('Planes obtenidos con SDK (array directo): ' . count($conektaPlans));
                    } else {
                        Log::warning('La respuesta de Conekta SDK no contiene un array de planes');
                        $conektaPlans = [];
                    }
                }
            } catch (\Exception $e) {
                // Si hay un error al obtener los planes de Conekta, establecer un array vacío
                Log::warning('No se pudieron obtener planes de Conekta: ' . $e->getMessage());
                $conektaPlans = [];
            }
            
            // Combinar los planes para mostrar tanto los de la base de datos como los de Conekta
            // que podrían no estar sincronizados
            $plansData = array_merge($databasePlans, $conektaPlans);

            return view('conekta-plans', [
                'plans' => $plansData,
                'databasePlans' => $databasePlans,
                'conektaPlans' => $conektaPlans
            ]);
        } catch (\Exception $e) {
            Log::error('Error al obtener planes de Conekta: ' . $e->getMessage());
            return view('conekta-plans', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Crea los planes necesarios en Conekta basados en los planes de la base de datos
     */
    public function createPlans()
    {
        try {
            // Configurar Conekta
            Conekta::setApiKey(env('CONEKTA_PRIVATE_KEY'));
            Conekta::setApiVersion('2.1.0');

            $createdPlans = [];
            $errors = [];

            // Obtener todos los planes activos que no tienen un conekta_plan_id (no están sincronizados)
            // o que tienen un conekta_plan_id pero no existen en Conekta (necesitan resincronizarse)
            $plans = DB::table('plans')
                ->select('id', 'conekta_plan_id', 'name', 'amount', 'currency', 'interval', 'frequency', 'service_id', 'description')
                ->where('is_active', true)
                ->whereNotNull('conekta_plan_id')
                ->get();

            // Obtener planes existentes en Conekta para comprobar duplicados
            $existingConektaPlans = $this->getPlansWithCurl();
            $existingConektaPlanIds = [];
            foreach ($existingConektaPlans as $existingPlan) {
                $existingConektaPlanIds[] = $existingPlan->id;
            }

            foreach ($plans as $plan) {
                try {
                    // Si el plan ya tiene un ID de Conekta y existe en Conekta, omitirlo
                    if (!empty($plan->conekta_plan_id) && in_array($plan->conekta_plan_id, $existingConektaPlanIds)) {
                        $createdPlans[] = "Plan {$plan->conekta_plan_id} ya existe en Conekta";
                        continue;
                    }

                    // Crear el plan en Conekta con el mismo ID que usamos en nuestra BD si está definido
                    $planId = !empty($plan->conekta_plan_id) ? $plan->conekta_plan_id : 'plan-' . $plan->id;
                    
                    $planData = [
                        'id' => $planId,
                        'name' => $plan->name,
                        'amount' => $plan->amount,
                        'currency' => $plan->currency,
                        'interval' => $plan->interval,
                        'frequency' => $plan->frequency,
                    ];

                    Log::info("Creando plan en Conekta: " . json_encode($planData));
                    $conektaPlan = Plan::create($planData);
                    
                    // Si el plan se creó correctamente, actualizar el conekta_plan_id en nuestra base de datos
                    if ($conektaPlan) {
                        DB::table('plans')
                            ->where('id', $plan->id)
                            ->update(['conekta_plan_id' => $planId]);
                            
                        Log::info("Plan creado en Conekta: {$conektaPlan->id} y actualizado en la base de datos");
                        $createdPlans[] = "Plan {$conektaPlan->id} creado exitosamente en Conekta";
                    }
                } catch (\Exception $e) {
                    $errors[] = "Error al crear plan {$plan->name}: " . $e->getMessage();
                    Log::error("Error al crear plan {$plan->name}: " . $e->getMessage());
                }
            }

            return view('conekta-plans', [
                'createdPlans' => $createdPlans,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            Log::error('Error general al crear planes en Conekta: ' . $e->getMessage());
            return view('conekta-plans', ['error' => $e->getMessage()]);
        }
    }
}
