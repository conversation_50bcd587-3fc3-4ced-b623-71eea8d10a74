<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Webhook;
use Illuminate\Http\Request;

class WebhookController extends Controller
{
    /**
     * Muestra una lista de webhooks con filtros opcionales.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Webhook::query();
        
        // Filtrar por tipo
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        // Filtrar por cliente
        if ($request->filled('cliente_ids')) {
            $query->where('cliente_ids', $request->cliente_ids);
        }
        
        // Filtrar por fecha desde
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        // Filtrar por fecha hasta
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        // Ordenar por fecha descendente (más recientes primero)
        $query->orderBy('created_at', 'desc');
        
        // Paginar resultados
        $webhooks = $query->paginate(15);
        
        return view('admin.webhooks', compact('webhooks'));
    }
    
    /**
     * Muestra los detalles de un webhook específico.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $webhook = Webhook::with(['cliente', 'cobro'])->findOrFail($id);
        
        return view('admin.webhooks-show', compact('webhook'));
    }
}
