<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Livewire\Drawer\Utils;

class CustomFilePreviewController implements HasMiddleware
{
    public static array $middleware = ['web'];

    public static function middleware()
    {
        return array_map(fn ($middleware) => new Middleware($middleware), static::$middleware);
    }

    public function handle($filename)
    {
        // En desarrollo, saltamos la validación de firma para proxies HTTPS
        if (config('app.env') === 'development') {
            // Solo validamos en development que la request tenga los parámetros necesarios
            abort_unless(request()->has(['expires', 'signature']), 401);
        } else {
            // En producción, usar la validación normal de firma
            abort_unless(request()->hasValidSignature(), 401);
        }

        return Utils::pretendPreviewResponseIsPreviewFile($filename);
    }
}
