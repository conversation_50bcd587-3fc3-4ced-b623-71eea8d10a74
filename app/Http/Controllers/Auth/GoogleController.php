<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AvatarService;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    protected $avatarService;

    public function __construct(AvatarService $avatarService)
    {
        $this->avatarService = $avatarService;
    }

    /**
     * Redirecciona al usuario a la página de autenticación de Google.
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtiene la información del usuario de Google y lo autentica.
     *
     * @return RedirectResponse
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // 1. Buscar usuario por google_id (usuarios que ya se han autenticado con Google antes)
            $user = User::where('google_id', $googleUser->id)->first();

            // Si el usuario existe y no está activo, no se puede autenticar
            if ($user && $user->activo == 'No') {
                return redirect()->route('login')->with([
                    'error' => 'Tu cuenta no está activa. Por favor, contacta al administrador para activarla.'
                ]);
            }

            // Descargar y guardar avatar solo si existe y el usuario no tiene avatar o es nuevo
            $avatarUrl = null;
            if ($googleUser->avatar && (!$user || ($user && $user->avatar === null))) {
                $avatarUrl = $this->avatarService->downloadAndSaveAvatar($googleUser->avatar);
            }

            // 2. Si no existe por google_id, buscar por logmail
            if (!$user) {
                $user = User::where('logmail', $googleUser->email)->first();

                // Si existe el usuario con ese logmail pero no está activo, no se puede autenticar
                if ($user && $user->activo == 'No') {
                    return redirect()->route('login')->with([
                        'error' => 'Tu cuenta no está activa. Por favor, contacta al administrador para activarla.'
                    ]);
                }

                // 3. Si existe el usuario con ese logmail, actualizar sus datos de Google
                if ($user) {
                    $user->update([
                        'google_id' => $googleUser->id,
                        'google_token' => $googleUser->token,
                        'google_refresh_token' => $googleUser->refreshToken,
                        'avatar' => $user->avatar ?? $avatarUrl,
                        'email_verified_at' => now(),
                    ]);
                } else {
                    // 4. Si no existe por logmail, buscar por email con las mismas condiciones que en login tradicional
                    $user = User::where('email', $googleUser->email)
                        ->where('activo', 'Si')
                        ->whereHas('contratos', function ($query) {
                            $query->where('status', 1)
                                ->where('servicio', 'LIKE', 'SI-%');
                        })
                        ->first();

                    // 5. Si existe un usuario que cumple con las condiciones, actualizar sus datos para migración
                    if ($user) {
                        // Componer el nombre desde nombre y apellidos (igual que en login.blade.php)
                        $fullName = trim($user->nombre . ' ' . $user->apellidos);

                        // Actualizar datos críticos para la migración
                        $user->update([
                            'google_id' => $googleUser->id,
                            'google_token' => $googleUser->token,
                            'google_refresh_token' => $googleUser->refreshToken,
                            'avatar' => $user->avatar ?? $avatarUrl,
                            'logmail' => $googleUser->email,
                            // Actualizar logmail con el email de Google
                            'name' => $fullName ?: $googleUser->name,
                            // Usar nombre completo o el de Google si está vacío
                            'email_verified_at' => now(),
                        ]);

                        // Redireccionar con mensaje de migración exitosa
                        Auth::login($user);
                        return redirect()->intended(route('dashboard'))
                            ->with('status', 'Tu cuenta ha sido actualizada y vinculada con Google correctamente.');
                    } else {
                        // 6. Si no existe ningún usuario, crear uno nuevo
                        $names = explode(' ', $googleUser->name);
                        $firstname = $names[0] ?? '';
                        $lastname = count($names) > 1 ? implode(' ', array_slice($names, 1)) : '';

                        // Generar un nombre de usuario único basado en el nombre
                        $baseUsername = Str::slug($googleUser->name);
                        $username = $baseUsername;
                        $counter = 1;

                        // Verificar si el username ya existe y generar uno único
                        while (User::where('usuario', $username)->exists()) {
                            $username = $baseUsername . $counter;
                            $counter++;
                        }

                        // Preparar teléfono (valores predeterminados ya que Google no proporciona número de teléfono)
                        $phone_country_code = '+52'; // Valor predeterminado
                        $phone_number = ''; // Vacío por defecto
                        $telefono = $phone_country_code . ' ' . $phone_number;

                        $user = User::create([
                            'name' => $googleUser->name,
                            'nombre' => $firstname,
                            'apellidos' => $lastname,
                            'usuario' => $username,
                            'email' => $googleUser->email,
                            'logmail' => $googleUser->email,
                            'telefono' => $telefono,
                            'phone_country_code' => $phone_country_code,
                            'phone_number' => $phone_number,
                            'google_id' => $googleUser->id,
                            'google_token' => $googleUser->token,
                            'google_refresh_token' => $googleUser->refreshToken,
                            'avatar' => $avatarUrl,
                            'password' => Hash::make(Str::random(16)), // Contraseña aleatoria
                            'email_verified_at' => now(), // El email ya está verificado por Google
                            'activo' => 'Si',
                            'quien_registro' => 'SistemaInmobiliario',
                        ]);

                        event(new Registered($user));
                    }
                }
            } else {
                // 7. Actualizar tokens y avatar si el usuario ya existe por google_id
                $user->update([
                    'google_token' => $googleUser->token,
                    'google_refresh_token' => $googleUser->refreshToken,
                    'avatar' => $user->avatar ?? $avatarUrl,
                ]);
            }

            // Iniciar sesión
            Auth::login($user);

            return redirect()->intended(route('dashboard'));
        } catch (Exception $e) {
            Log::error('Error al autenticar con Google: ' . $e->getMessage());
            return redirect()->route('login')->with(
                'error',
                'Ocurrió un error al autenticar con Google, intenta nuevamente.'
            );
        }
    }
}
