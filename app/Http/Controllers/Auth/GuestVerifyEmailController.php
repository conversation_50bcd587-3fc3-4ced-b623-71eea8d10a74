<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class GuestVerifyEmailController extends Controller
{
    /**
     * Mark the user's email address as verified.
     * This controller handles verification without requiring authentication.
     */
    public function __invoke(Request $request)
    {
        // Validar que los parámetros requeridos estén presentes
        $userId = $request->route('id');
        $hash = $request->route('hash');

        if (!$userId || !$hash) {
            return redirect()->route('login')
                ->with('error', 'El enlace de verificación no es válido.');
        }

        // Buscar al usuario por ID
        try {
            $user = User::findOrFail($userId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return redirect()->route('login')
                ->with('error', 'El usuario no existe.');
        }

        // Verificar que el hash sea válido
        if (!hash_equals(sha1($user->getEmailForVerification()), $hash)) {
            return redirect()->route('login')
                ->with('error', 'El enlace de verificación no es válido.');
        }

        // Verificar si el email ya estaba verificado antes de este proceso
        $emailAlreadyVerified = $user->hasVerifiedEmail();

        // Si el email ya está verificado, redirigir al login sin hacer login automático
        if ($emailAlreadyVerified) {
            // Si el usuario ya está autenticado, ir al dashboard
            if (Auth::check()) {
                return redirect()->intended(route('dashboard', absolute: false) . '?verified=1');
            }

            // Si no está autenticado, redirigir al login
            return redirect()->route('login')
                ->with('status', 'Tu correo electrónico ya ha sido verificado. Puedes iniciar sesión.');
        }

        // Marcar el email como verificado (primera vez)
        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        // Verificar si el usuario está autenticado
        if (Auth::check()) {
            // Si está autenticado, redirigir al dashboard
            return redirect()->intended(route('dashboard', absolute: false) . '?verified=1');
        }

        // Si no está autenticado y es la primera verificación, hacer login automático
        Auth::login($user);

        // Redirigir al dashboard con mensaje de éxito
        return redirect()->intended(route('dashboard', absolute: false) . '?verified=1')
            ->with('status', 'Tu correo electrónico ha sido verificado correctamente y has sido autenticado automáticamente.');
    }
}
