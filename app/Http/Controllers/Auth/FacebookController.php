<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AvatarService;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class FacebookController extends Controller
{
    protected $avatarService;

    public function __construct(AvatarService $avatarService)
    {
        $this->avatarService = $avatarService;
    }

    /**
     * Redirecciona al usuario a la página de autenticación de Facebook.
     *
     * @return RedirectResponse
     */
    public function redirectToFacebook(): RedirectResponse
    {
        return Socialite::driver('facebook')->redirect();
    }

    /**
     * Obtiene la información del usuario de Facebook y lo autentica.
     *
     * @return RedirectResponse
     */
    public function handleFacebookCallback()
    {
        try {
            $facebookUser = Socialite::driver('facebook')->user();

            // 1. Buscar usuario por facebook_id (usuarios que ya se han autenticado con Facebook antes)
            $user = User::where('facebook_id', $facebookUser->id)->first();

            // Si el usuario existe y no está activo, no se puede autenticar
            if ($user && $user->activo == 'No') {
                return redirect()->route('login')->with([
                    'error' => 'Tu cuenta no está activa. Por favor, contacta al administrador para activarla.'
                ]);
            }

            // Descargar y guardar avatar solo si existe y el usuario no tiene avatar o es nuevo
            $avatarUrl = null;
            if ($facebookUser->avatar && (!$user || ($user && $user->avatar === null))) {
                $avatarUrl = $this->avatarService->downloadAndSaveAvatar($facebookUser->avatar);
            }

            // 2. Si no existe por facebook_id, buscar por logmail
            if (!$user) {
                $user = User::where('logmail', $facebookUser->email)->first();

                // Si existe el usuario con ese logmail pero no está activo, no se puede autenticar
                if ($user && $user->activo == 'No') {
                    return redirect()->route('login')->with([
                        'error' => 'Tu cuenta no está activa. Por favor, contacta al administrador para activarla.'
                    ]);
                }

                // 3. Si existe el usuario con ese logmail, actualizar sus datos de Facebook
                if ($user) {
                    $user->update([
                        'facebook_id' => $facebookUser->id,
                        'facebook_token' => $facebookUser->token,
                        'avatar' => $user->avatar ?? $avatarUrl,
                    ]);
                } else {
                    // 4. Si no existe por logmail, buscar por email con las mismas condiciones que en login tradicional
                    $user = User::where('email', $facebookUser->email)
                        ->where('activo', 'Si')
                        ->whereHas('contratos', function ($query) {
                            $query->where('status', 1)
                                ->where('servicio', 'LIKE', 'SI-%');
                        })
                        ->first();

                    // 5. Si existe un usuario que cumple con las condiciones, actualizar sus datos para migración
                    if ($user) {
                        // Componer el nombre desde nombre y apellidos (igual que en login.blade.php)
                        $fullName = trim($user->nombre . ' ' . $user->apellidos);

                        // Actualizar datos críticos para la migración
                        $user->update([
                            'facebook_id' => $facebookUser->id,
                            'facebook_token' => $facebookUser->token,
                            'avatar' => $user->avatar ?? $avatarUrl,
                            'logmail' => $facebookUser->email,
                            // Actualizar logmail con el email de Facebook
                            'name' => $fullName ?: $facebookUser->name,
                            // Usar nombre completo o el de Facebook si está vacío
                        ]);

                        // Redireccionar con mensaje de migración exitosa
                        Auth::login($user);
                        return redirect()->intended(route('dashboard'))
                            ->with('status', 'Tu cuenta ha sido actualizada y vinculada con Facebook correctamente.');
                    } else {
                        // 6. Si no existe ningún usuario, crear uno nuevo
                        $names = explode(' ', $facebookUser->name);
                        $firstname = $names[0] ?? '';
                        $lastname = count($names) > 1 ? implode(' ', array_slice($names, 1)) : '';

                        // Generar un nombre de usuario único basado en el nombre
                        $baseUsername = Str::slug($facebookUser->name);
                        $username = $baseUsername;
                        $counter = 1;

                        // Verificar si el username ya existe y generar uno único
                        while (User::where('usuario', $username)->exists()) {
                            $username = $baseUsername . $counter;
                            $counter++;
                        }

                        // Preparar teléfono (valores predeterminados ya que Facebook no proporciona número de teléfono)
                        $phone_country_code = '+52'; // Valor predeterminado
                        $phone_number = ''; // Vacío por defecto
                        $telefono = $phone_country_code . ' ' . $phone_number;

                        $user = User::create([
                            'name' => $facebookUser->name,
                            'nombre' => $firstname,
                            'apellidos' => $lastname,
                            'usuario' => $username,
                            'email' => $facebookUser->email,
                            'logmail' => $facebookUser->email,
                            'telefono' => $telefono,
                            'phone_country_code' => $phone_country_code,
                            'phone_number' => $phone_number,
                            'facebook_id' => $facebookUser->id,
                            'facebook_token' => $facebookUser->token,
                            'avatar' => $avatarUrl,
                            'password' => Hash::make(Str::random(16)), // Contraseña aleatoria
                            'email_verified_at' => now(), // El email ya está verificado por Facebook
                            'activo' => 'Si',
                            'quien_registro' => 'SistemaInmobiliario',
                        ]);

                        event(new Registered($user));
                    }
                }
            } else {
                // 7. Actualizar tokens y avatar si el usuario ya existe por facebook_id
                $user->update([
                    'facebook_token' => $facebookUser->token,
                    'avatar' => $user->avatar ?? $avatarUrl,
                ]);
            }

            // Iniciar sesión
            Auth::login($user);

            return redirect()->intended(route('dashboard'));
        } catch (Exception $e) {
            return redirect()->route('login')->with(
                'error',
                'Ocurrió un error al autenticar con Facebook: ' . $e->getMessage()
            );
        }
    }
}
