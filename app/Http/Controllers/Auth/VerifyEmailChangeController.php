<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class VerifyEmailChangeController extends Controller
{
    /**
     * Verify the new email address.
     */
    public function __invoke(Request $request): RedirectResponse
    {
        $user = User::findOrFail($request->route('id'));

        if (!hash_equals(sha1($request->route('email')), $request->route('hash'))) {
            return redirect()->route('settings.profile')->with('error', 'El enlace de verificación no es válido.');
        }

        // Verificar que el correo en la URL coincida con el correo pendiente
        if ($user->pending_email !== $request->route('email')) {
            return redirect()->route('settings.profile')->with('error', 'El correo electrónico no coincide con la solicitud pendiente.');
        }

        // Actualizar el correo electrónico del usuario
        $newEmail = $request->route('email');

        // Verificar si el nuevo correo ya existe como logmail de otro usuario
        $existingUser = User::where('logmail', $newEmail)->where('id', '!=', $user->id)->first();

        if ($existingUser) {
            return redirect()->route('settings.profile')->with('error', 'El correo electrónico ya está siendo utilizado por otra cuenta. Por favor, intente con un correo diferente.');
        }

        // Actualizar tanto el email como el logmail
        $user->email = $newEmail;
        $user->logmail = $newEmail; // Actualizar el campo de inicio de sesión
        $user->email_verified_at = now();
        $user->pending_email = null;
        $user->pending_email_at = null;
        $user->save();

        // Si el usuario está autenticado, actualizar la sesión
        if (Auth::check() && Auth::id() === $user->id) {
            event(new Verified($user));
            Session::forget('pending_email_change');
        }

        return redirect()->route('settings.profile')->with('status', 'email-changed');
    }
}
