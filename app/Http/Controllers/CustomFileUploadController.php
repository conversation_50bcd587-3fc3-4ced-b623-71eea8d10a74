<?php

namespace App\Http\Controllers;

use Livewire\Features\SupportFileUploads\FileUploadController;
use Livewire\Features\SupportFileUploads\FileUploadConfiguration;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class CustomFileUploadController extends FileUploadController implements HasMiddleware
{
    public static function middleware()
    {
        $middleware = (array) FileUploadConfiguration::middleware();

        if (! in_array('web', $middleware)) {
            $middleware = array_merge(['web'], $middleware);
        }

        return array_map(fn ($middleware) => new Middleware($middleware), $middleware);
    }

    public function handle()
    {
        // En desarrollo, saltamos la validación de firma para proxies HTTPS
        if (config('app.env') === 'development') {
            // Solo validamos en development que la request tenga los parámetros necesarios
            abort_unless(request()->has(['expires', 'signature']), 401);
        } else {
            // En producción, usar la validación normal de firma
            abort_unless(request()->hasValidSignature(), 401);
        }

        $disk = FileUploadConfiguration::disk();

        $filePaths = $this->validateAndStore(request('files'), $disk);

        return ['paths' => $filePaths];
    }
}
