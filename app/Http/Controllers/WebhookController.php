<?php

namespace App\Http\Controllers;

use App\Models\Cobro;
use App\Models\Contrato;
use App\Models\DepRel;
use App\Models\Deposito;
use App\Models\SIConfig;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Webhook;
use App\Livewire\Traits\WebhookGestionTrait;
use App\Services\CobroService;
use App\Services\DepositoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    use WebhookGestionTrait;

    /**
     * El servicio de cobros
     *
     * @var CobroService
     */
    protected $cobroService;

    /**
     * El servicio de depósitos
     *
     * @var DepositoService
     */
    protected $depositoService;

    /**
     * Constructor del controlador
     *
     * @param CobroService $cobroService
     * @param DepositoService $depositoService
     */
    public function __construct(CobroService $cobroService, DepositoService $depositoService)
    {
        $this->cobroService = $cobroService;
        $this->depositoService = $depositoService;
    }

    /**
     * Maneja los Webhooks entrantes de Conekta.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function handleConektaWebhook(Request $request)
    {
        $payload = $request->all();
        $event = $request->input('type');

        Log::info('Webhook de Conekta recibido: ' . $event);

        // Guardar el webhook en la base de datos
        $this->guardarWebhook($payload, $event, 'conekta');

        // Verificar la firma del webhook (recomendado para producción)
        // $this->verifyWebhookSignature($request);

        try {
            switch ($event) {
                case 'subscription.created':
                    $this->handleSubscriptionCreated($payload);
                    break;
                case 'subscription.paid':
                    $this->handleSubscriptionPaid($payload);
                    break;
                case 'subscription.payment_failed':
                    $this->handleSubscriptionPaymentFailed($payload);
                    break;
                case 'subscription.canceled':
                    $this->handleSubscriptionCanceled($payload);
                    break;
                case 'subscription.paused':
                    $this->handleSubscriptionPaused($payload);
                    break;
                case 'subscription.resumed':
                    $this->handleSubscriptionResumed($payload);
                    break;
                case 'order.refunded':
                    $this->handleOrderRefunded($payload);
                    break;
                case 'order.partially_refunded':
                    $this->handleOrderPartiallyRefunded($payload);
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Error al procesar webhook de Conekta: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Maneja el evento de creación de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionCreated($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.created');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        // Buscar la suscripción existente o crear una nueva
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if (!$subscription) {
            Log::info('Nueva suscripción creada vía webhook: ' . $subscriptionId);
        } else {
            $subscription->status = $object['status'] ?? 'active';
            $subscription->save();

            Log::info('Suscripción actualizada vía webhook: ' . $subscriptionId);
        }
    }

    /**
     * Maneja el evento de pago exitoso de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionPaid($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.paid');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if ($subscription) {
            // Actualizar el estado de la suscripción
            $subscription->status = 'active';
            $subscription->save();

            Log::info('Suscripción pagada vía webhook: ' . $subscriptionId);

            // Buscar el usuario asociado a esta suscripción
            $user = $subscription->user;
            if (!$user) {
                Log::warning('Usuario no encontrado para la suscripción: ' . $subscriptionId);
                return;
            }

            // Procesar el pago exitoso
            $this->procesarPagoExitoso($user, $subscriptionId, $object, 'Conekta');
        } else {
            Log::warning('Suscripción no encontrada para el ID: ' . $subscriptionId);
        }
    }

    /**
     * Maneja el evento de fallo de pago de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionPaymentFailed($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.payment_failed');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if ($subscription) {
            $subscription->status = 'past_due';
            $subscription->save();

            Log::info('Fallo de pago en suscripción vía webhook: ' . $subscriptionId);

            // Enviar notificación al usuario
            // $subscription->user->notify(new SubscriptionPaymentFailed($subscription));
        } else {
            Log::warning('Suscripción no encontrada para el ID: ' . $subscriptionId);
        }
    }

    /**
     * Maneja el evento de cancelación de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionCanceled($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.canceled');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if ($subscription) {
            $subscription->status = 'canceled';
            $subscription->ends_at = now();
            $subscription->save();

            Log::info('Suscripción cancelada vía webhook: ' . $subscriptionId);
        } else {
            Log::warning('Suscripción no encontrada para el ID: ' . $subscriptionId);
        }
    }

    /**
     * Maneja el evento de pausa de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionPaused($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.paused');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if ($subscription) {
            $subscription->status = 'paused';
            $subscription->save();

            Log::info('Suscripción pausada vía webhook: ' . $subscriptionId);
        } else {
            Log::warning('Suscripción no encontrada para el ID: ' . $subscriptionId);
        }
    }

    /**
     * Maneja el evento de reanudación de suscripción.
     *
     * @param array $payload
     * @return void
     */
    private function handleSubscriptionResumed($payload)
    {
        // Extraer el objeto de suscripción del payload
        $object = $this->extraerObjetoSuscripcion($payload, 'subscription.resumed');
        if (!$object) {
            return;
        }

        $subscriptionId = $object['id'];
        $subscription = Subscription::where('conekta_id', $subscriptionId)->first();

        if ($subscription) {
            $subscription->status = 'active';
            $subscription->save();

            Log::info('Suscripción reanudada vía webhook: ' . $subscriptionId);
        } else {
            Log::warning('Suscripción no encontrada para el ID: ' . $subscriptionId);
        }
    }

    /**
     * Verifica la firma del webhook para confirmar que viene de Conekta.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    private function verifyWebhookSignature(Request $request)
    {
        // Este método debe implementarse para entornos de producción
        // Documentación: https://developers.conekta.com/docs/verificar-firmas
        return true;
    }

    /**
     * Crea un depósito para respaldar el pago de un cobro
     *
     * @param User $user El usuario que realiza el pago
     * @param Cobro $cobro El cobro que se está pagando
     * @param array $object El objeto de suscripción de Conekta
     * @param string $subscriptionId El ID de la suscripción
     * @return Deposito El depósito creado
     */
    private function crearDeposito(User $user, Cobro $cobro, array $object, string $subscriptionId): Deposito
    {
        // Extraer información relevante del objeto de suscripción
        $monto = $cobro->precio;

        // Usar DepositoService para crear el depósito
        return $this->depositoService->crearPagoAutomatico(
            $user,
            $monto,
            'Conekta',
            $subscriptionId,
            'Pago de suscripción',
            $object
        );
    }

    /**
     * Extrae el objeto de suscripción del payload del webhook.
     * Maneja diferentes estructuras de payload que puede enviar Conekta.
     *
     * @param array $payload
     * @param string $eventType
     * @return array|null
     */
    private function extraerObjetoSuscripcion($payload, $eventType)
    {
        // Caso 1: El objeto está directamente en la raíz del payload
        if (isset($payload['object']) && is_array($payload['object']) && isset($payload['object']['id'])) {
            return $payload['object'];
        }

        // Caso 2: El objeto está dentro de data.object (formato de webhook más reciente)
        if (isset($payload['data']) && is_array($payload['data']) &&
            isset($payload['data']['object']) && is_array($payload['data']['object']) &&
            isset($payload['data']['object']['id'])) {
            return $payload['data']['object'];
        }

        // Si llegamos aquí, no pudimos encontrar un objeto de suscripción válido
        Log::error('Estructura de payload inválida en ' . $eventType . ' webhook', ['payload' => $payload]);
        return null;
    }

    /**
     * Maneja el evento de reembolso total de una orden.
     *
     * @param array $payload
     * @return void
     */
    private function handleOrderRefunded($payload)
    {
        // Extraer el objeto de orden del payload
        $object = $this->extraerObjetoOrden($payload, 'order.refunded');
        if (!$object) {
            return;
        }

        $orderId = $object['id'];
        Log::info('Reembolso total de orden vía webhook: ' . $orderId);

        // Utilizar el método del trait para procesar los reembolsos
        $this->procesarReembolsosDeOrden($object, true, 'Conekta');
    }

    /**
     * Maneja el evento de reembolso parcial de una orden.
     *
     * @param array $payload
     * @return void
     */
    private function handleOrderPartiallyRefunded($payload)
    {
        // Extraer el objeto de orden del payload
        $object = $this->extraerObjetoOrden($payload, 'order.partially_refunded');
        if (!$object) {
            return;
        }

        $orderId = $object['id'];
        Log::info('Reembolso parcial de orden vía webhook: ' . $orderId);

        // Utilizar el método del trait para procesar los reembolsos
        $this->procesarReembolsosDeOrden($object, false, 'Conekta');
    }

    /**
     * Extrae el objeto de orden del payload del webhook.
     * Similar a extraerObjetoSuscripcion pero para órdenes.
     *
     * @param array $payload
     * @param string $eventType
     * @return array|null
     */
    private function extraerObjetoOrden($payload, $eventType)
    {
        $orden = null;
        $refundInfo = [];
        
        // Caso 1: El objeto está directamente en la raíz del payload
        if (isset($payload['object']) && is_array($payload['object']) && isset($payload['object']['id'])) {
            $orden = $payload['object'];
        }
        // Caso 2: El objeto está dentro de data.object (formato de webhook más reciente)
        elseif (isset($payload['data']) && is_array($payload['data']) &&
            isset($payload['data']['object']) && is_array($payload['data']['object']) &&
            isset($payload['data']['object']['id'])) {
            $orden = $payload['data']['object'];
        }
        
        // Si no encontramos la orden, no podemos continuar
        if (!$orden) {
            Log::error('Estructura de payload inválida en ' . $eventType . ' webhook - No se encontró orden', ['payload' => $payload]);
            return null;
        }
        
        // Para eventos de reembolso, necesitamos extraer la información del reembolso
        if (in_array($eventType, ['order.refunded', 'order.partially_refunded'])) {
            // Buscar información de reembolso en la estructura de cargos
            if (isset($orden['charges']) && isset($orden['charges']['data']) && is_array($orden['charges']['data'])) {
                foreach ($orden['charges']['data'] as $charge) {
                    // Si hay datos de reembolso pero están truncados
                    if (isset($charge['refunds']) && isset($charge['refunds']['data'])) {
                        // Si los datos están truncados, crear una estructura mínima
                        if (is_array($charge['refunds']['data']) && count($charge['refunds']['data']) > 0) {
                            if (is_string($charge['refunds']['data'][0]) && 
                                strpos($charge['refunds']['data'][0], 'Over') !== false) {
                                // Datos truncados por anidamiento profundo
                                $refundInfo = [
                                    'id' => $orden['id'] . '_refund', // Crear un ID único basado en la orden
                                    'amount' => $orden['amount_refunded'] ?? 0,
                                    'order_id' => $orden['id'],
                                    'timestamp' => $orden['updated_at'] ?? time()
                                ];
                            } else {
                                // Datos de reembolso disponibles normalmente
                                $refundInfo = $charge['refunds']['data'][0];
                            }
                        }
                    }
                }
            }
            
            // Si no encontramos información de reembolso, pero sabemos que hay un reembolso
            if (empty($refundInfo) && isset($orden['amount_refunded']) && $orden['amount_refunded'] > 0) {
                $refundInfo = [
                    'id' => $orden['id'] . '_refund', // Crear un ID único basado en la orden
                    'amount' => $orden['amount_refunded'],
                    'order_id' => $orden['id'],
                    'timestamp' => $orden['updated_at'] ?? time()
                ];
            }
            
            // Si aún no tenemos información de reembolso, no podemos proceder
            if (empty($refundInfo)) {
                Log::error('No se pudo extraer información de reembolso en ' . $eventType . ' webhook', ['orden' => $orden]);
                return null;
            }
            
            // Añadir la información de reembolso a la estructura principal
            $orden['_refund_info'] = $refundInfo;
            $orden['refunds'] = ['data' => [$refundInfo]];
            
            return $orden;
        }
        
        // Para otros tipos de eventos, simplemente devolver la orden
        return $orden;
    }
}
