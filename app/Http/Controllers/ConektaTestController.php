<?php

namespace App\Http\Controllers;

use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ConektaTestController extends Controller
{
    /**
     * Muestra una página para probar la integración con Conekta
     */
    public function index()
    {
        return view('conekta-test');
    }

    /**
     * Procesa una prueba de creación de cliente en Conekta
     */
    public function testCreateCustomer(Request $request)
    {
        $request->validate([
            'token_id' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string',
        ]);

        $paymentService = app(PaymentService::class);
        
        try {
            $result = $paymentService->createCustomer(
                $request->token_id,
                $request->email,
                $request->name
            );
            
            if (isset($result['error'])) {
                Log::error('Error al crear cliente en Conekta: ' . $result['error']);
                return back()->withErrors(['error' => $result['error']]);
            }
            
            return back()->with('success', 'Cliente creado exitosamente en Conekta: ' . $result['id']);
        } catch (\Exception $e) {
            Log::error('Excepción al crear cliente en Conekta: ' . $e->getMessage());
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
}
