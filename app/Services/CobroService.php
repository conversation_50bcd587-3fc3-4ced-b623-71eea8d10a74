<?php

namespace App\Services;

use App\Models\Cobro;
use App\Models\User;
use App\Models\Deposito;
use App\Models\Contrato;
use App\Models\Plan;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CobroService
{
    /**
     * Obtiene un cobro por su número
     *
     * @param int $numero
     * @return Cobro|null
     */
    public function obtenerPorNumero(int $numero): ?Cobro
    {
        return Cobro::find($numero);
    }

    /**
     * Busca cobros por usuario
     *
     * @param string $usuario
     * @param bool $incluirPagados
     * @return Collection
     */
    public function obtenerPorUsuario(string $usuario, bool $incluirPagados = false): Collection
    {
        $query = Cobro::where('usuario', $usuario);
        
        if (!$incluirPagados) {
            $query->pendientes();
        }
        
        return $query->orderBy('vencimiento')->get();
    }

    /**
     * Obtiene los cobros asociados a un contrato
     *
     * @param int $numeroContrato
     * @param bool $incluirPagados
     * @return Collection
     */
    public function obtenerPorContrato(int $numeroContrato, bool $incluirPagados = false): Collection
    {
        $query = Cobro::where('contrato', $numeroContrato);
        
        if (!$incluirPagados) {
            $query->pendientes();
        }
        
        return $query->orderBy('desde')->get();
    }

    /**
     * Obtiene cobros independientes (sin contrato asociado)
     *
     * @param bool $incluirPagados
     * @return Collection
     */
    public function obtenerIndependientes(bool $incluirPagados = false): Collection
    {
        $query = Cobro::independientes();
        
        if (!$incluirPagados) {
            $query->pendientes();
        }
        
        return $query->orderBy('vencimiento')->get();
    }

    /**
     * Obtiene los cobros vencidos
     *
     * @param int $dias Días de vencimiento
     * @return Collection
     */
    public function obtenerVencidos(int $dias = 0): Collection
    {
        $query = Cobro::vencidos();
        
        if ($dias > 0) {
            $query->where('vencimiento', '>=', now()->subDays($dias)->startOfDay());
        }
        
        return $query->orderBy('vencimiento')->get();
    }

    /**
     * Obtiene los cobros por vencer en los próximos días
     *
     * @param int $dias
     * @return Collection
     */
    public function obtenerPorVencer(int $dias = 7): Collection
    {
        return Cobro::porVencer($dias)
            ->orderBy('vencimiento')
            ->get();
    }

    /**
     * Crea un nuevo cobro
     *
     * @param array $datos
     * @param string $usuarioCreador
     * @return Cobro
     */
    public function crear(array $datos, string $usuarioCreador): Cobro
    {
        $datos['creado_por'] = $usuarioCreador;
        $datos['modificado_por'] = $usuarioCreador;
        $datos['real_creado'] = now();
        $datos['real_modificado'] = now();
        
        // Por defecto, los cobros no están pagados
        if (!isset($datos['pagado'])) {
            $datos['pagado'] = 'No';
        }
        
        // Por defecto, los cobros no están detenidos
        if (!isset($datos['detener'])) {
            $datos['detener'] = 'No';
        }
        
        // Inicializar cantidad_pagada en 0 si no se proporciona
        if (!isset($datos['cantidad_pagada'])) {
            $datos['cantidad_pagada'] = 0;
        }
        
        return Cobro::create($datos);
    }

    /**
     * Actualiza un cobro existente
     *
     * @param int $numero
     * @param array $datos
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function actualizar(int $numero, array $datos, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $datos['modificado_por'] = $usuarioModificador;
        $datos['real_modificado'] = now();
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Marca un cobro como pagado
     *
     * @param int $numero
     * @param string $formaPago
     * @param string|null $observaciones
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function marcarComoPagado(int $numero, string $formaPago, ?string $observaciones, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $datos = [
            'pagado' => 'Si',
            'forma_pago' => $formaPago,
            'observaciones_pago' => $observaciones,
            'notificacion_pago' => now(),
            'autorizacion_de_pago' => now(),
            'cantidad_pagada' => $cobro->cantidad,
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Registra un pago parcial para un cobro
     *
     * @param int $numero
     * @param int $cantidadPagada
     * @param string $formaPago
     * @param string|null $observaciones
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function registrarPagoParcial(int $numero, int $cantidadPagada, string $formaPago, ?string $observaciones, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $nuevaCantidadPagada = $cobro->cantidad_pagada + $cantidadPagada;
        
        // Si la nueva cantidad pagada es mayor o igual a la cantidad total, marcar como pagado completamente
        if ($nuevaCantidadPagada >= $cobro->cantidad) {
            return $this->marcarComoPagado($numero, $formaPago, $observaciones, $usuarioModificador);
        }
        
        $datos = [
            'cantidad_pagada' => $nuevaCantidadPagada,
            'forma_pago' => $formaPago,
            'observaciones_pago' => $observaciones,
            'notificacion_pago' => now(),
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Marca un cobro como detenido o lo reactiva
     *
     * @param int $numero
     * @param bool $detener
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function cambiarEstadoDetenido(int $numero, bool $detener, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $datos = [
            'detener' => $detener ? 'Si' : 'No',
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Asocia un depósito a un cobro
     *
     * @param int $numeroCobro
     * @param int $idDeposito
     * @return bool
     */
    public function asociarDeposito(int $numeroCobro, int $idDeposito): bool
    {
        $cobro = Cobro::find($numeroCobro);
        $deposito = Deposito::find($idDeposito);
        
        if (!$cobro || !$deposito) {
            return false;
        }
        
        // Verificar si ya existe la relación
        if ($cobro->depositos()->where('id_deposito', $idDeposito)->exists()) {
            return true;
        }
        
        $cobro->depositos()->attach($idDeposito);
        return true;
    }

    /**
     * Desasocia un depósito de un cobro
     *
     * @param int $numeroCobro
     * @param int $idDeposito
     * @return bool
     */
    public function desasociarDeposito(int $numeroCobro, int $idDeposito): bool
    {
        $cobro = Cobro::find($numeroCobro);
        
        if (!$cobro) {
            return false;
        }
        
        $cobro->depositos()->detach($idDeposito);
        return true;
    }

    /**
     * Registra una notificación enviada para un cobro
     *
     * @param int $numero
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function registrarNotificacion(int $numero, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $datos = [
            'ultima_notificacion' => now(),
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Autoriza el pago de un cobro
     *
     * @param int $numero
     * @param string $usuarioModificador
     * @return Cobro|null
     */
    public function autorizarPago(int $numero, string $usuarioModificador): ?Cobro
    {
        $cobro = Cobro::find($numero);
        
        if (!$cobro) {
            return null;
        }
        
        $datos = [
            'autorizacion_de_pago' => now(),
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }

    /**
     * Busca cobros por texto en el campo pabusqueda
     *
     * @param string $texto
     * @param bool $incluirPagados
     * @return Collection
     */
    public function buscar(string $texto, bool $incluirPagados = false): Collection
    {
        $query = Cobro::where('pabusqueda', 'LIKE', "%{$texto}%");
        
        if (!$incluirPagados) {
            $query->pendientes();
        }
        
        return $query->orderBy('vencimiento')->get();
    }

    /**
     * Genera cobros para un período específico basado en un contrato
     *
     * @param int $numeroContrato
     * @param Carbon $desde
     * @param Carbon $hasta
     * @param string $usuarioCreador
     * @return Collection
     */
    public function generarCobrosPeriodo(int $numeroContrato, Carbon $desde, Carbon $hasta, string $usuarioCreador): Collection
    {
        // Aquí se implementaría la lógica para generar cobros periódicos
        // basados en un contrato. Esta lógica puede variar según los requisitos específicos.
        
        // Por ahora, retornamos una colección vacía como placeholder
        return collect([]);
    }

    /**
     * Obtiene estadísticas de cobros
     *
     * @param Carbon|null $desde
     * @param Carbon|null $hasta
     * @return array
     */
    public function obtenerEstadisticas(?Carbon $desde = null, ?Carbon $hasta = null): array
    {
        $query = Cobro::query();
        
        if ($desde) {
            $query->where('fecha', '>=', $desde);
        }
        
        if ($hasta) {
            $query->where('fecha', '<=', $hasta);
        }
        
        $pendientes = $query->clone()->pendientes()->count();
        $pagados = $query->clone()->pagados()->count();
        $vencidos = $query->clone()->vencidos()->count();
        $porVencer = $query->clone()->porVencer(7)->count();
        
        $totalPendiente = $query->clone()->pendientes()->sum(DB::raw('precio * (cantidad - cantidad_pagada)'));
        $totalCobrado = $query->clone()->pagados()->sum(DB::raw('precio * cantidad'));
        
        return [
            'pendientes' => $pendientes,
            'pagados' => $pagados,
            'vencidos' => $vencidos,
            'por_vencer' => $porVencer,
            'total_pendiente' => $totalPendiente,
            'total_cobrado' => $totalCobrado
        ];
    }

    /**
     * Crea un cobro inicial asociado a un contrato recién creado
     *
     * @param Contrato $contrato El contrato para el que se crea el cobro
     * @param User $user El usuario asociado al contrato
     * @param array $activationData Datos de activación del plan
     * @param Carbon $pagadoHasta Fecha hasta la que se paga el servicio
     * @param Plan|null $plan El plan seleccionado (opcional)
     * @return Cobro|null El cobro creado o null si hubo un error
     */
    public function crearCobroInicial(
        Contrato $contrato,
        User $user,
        array $activationData,
        Carbon $pagadoHasta,
        ?Plan $plan = null
    ): ?Cobro {
        try {
            // Determinar la descripción del cobro según el plan y período
            $periodoTexto = match ($activationData['billingPeriod']) {
                'quarterly' => 'trimestral',
                'biannual' => 'semestral',
                'annual' => 'anual',
                default => 'mensual',
            };

            $planTexto = ucfirst($activationData['selectedPlan']);
            $descripcion = "Servicio $planTexto con facturación $periodoTexto";

            // Si es plan freemium sin complemento, marcar como pagado
            // Para planes de pago o plan freemium con complemento SI-MS, se marca como no pagado inicialmente
            $pagado = ($activationData['selectedPlan'] === 'SI-FREE' && !($activationData['addMsComplement'] ?? false)) ? 'Si' : 'No';

            // Fecha de inicio del servicio (ahora)
            $fechaInicio = now();

            // Fecha de fin del servicio basada en el período de facturación
            $fechaFin = $pagadoHasta;

            // Calcular la fecha de vencimiento (fecha de inicio + días de gracia)
            $fechaVencimiento = $fechaInicio->copy()->addDays($contrato->dias_tregua);

            Log::info('CobroService: Calculando fechas del cobro', [
                'fechaInicio' => $fechaInicio->format('Y-m-d'),
                'fechaFin' => $fechaFin->format('Y-m-d'),
                'diasTregua' => $contrato->dias_tregua,
                'fechaVencimiento' => $fechaVencimiento->format('Y-m-d')
            ]);

            // Datos para el cobro
            $datosCobro = [
                'contrato' => $contrato->numero,
                'fecha' => now(),
                'usuario' => $user->usuario,
                'servicio' => $contrato->servicio,
                'cantidad' => 1,
                'descripcion' => $descripcion,
                'dominio' => $contrato->dominio,
                'adicional' => json_encode([
                    'plan' => $activationData['selectedPlan'],
                    'periodo' => $activationData['billingPeriod'],
                    'subscription_id' => $activationData['subscription_id'] ?? null,
                    'frequency' => $contrato->forma_pago, // Guardamos la frecuencia del plan
                    'plan_id' => $plan?->id, // Guardamos el ID del plan si existe
                    'descuento' => $contrato->en_precio // Guardamos el porcentaje de descuento
                ]),
                'precio' => $activationData['totalPrice'] ?? 0,
                'desde' => $fechaInicio,
                'hasta' => $fechaFin,
                'pagado' => $pagado,
                'factura' => '',
                'forma_pago' => ($activationData['selectedPlan'] === 'SI-FREE' && !($activationData['addMsComplement'] ?? false)) ? 'Gratuito' : 'Tarjeta',
                'observaciones_pago' => 'Cobro generado automáticamente en el proceso de activación',
                'vencimiento' => $fechaVencimiento, // Fecha de inicio + días de gracia
                'ultima_notificacion' => now(),
                'detener' => 'No',
                'cantidad_pagada' => ($pagado === 'Si') ? 1 : 0
            ];

            // Crear el cobro usando el método crear del servicio
            $cobro = $this->crear($datosCobro, 'sistema');

            Log::info('CobroService: Cobro inicial creado exitosamente', [
                'cobro_id' => $cobro->numero,
                'contrato_id' => $contrato->numero,
                'usuario' => $user->usuario,
                'monto' => $activationData['totalPrice'] ?? 0,
                'pagado' => $pagado,
                'desde' => $fechaInicio->format('Y-m-d'),
                'hasta' => $fechaFin->format('Y-m-d'),
                'vencimiento' => $fechaVencimiento->format('Y-m-d')
            ]);

            return $cobro;
        } catch (\Exception $e) {
            Log::error('CobroService: Error al crear cobro inicial', [
                'error' => $e->getMessage(),
                'contrato_id' => $contrato->numero
            ]);

            return null;
        }
    }

    /**
     * Crea un cobro para registrar una devolución (cantidad negativa)
     *
     * @param array $datos Datos del cobro
     * @param string $referenciaExterna ID de la orden o transacción externa
     * @param string $usuarioCreador Usuario que crea el registro
     * @return Cobro
     */
    public function crearDevolucion(array $datos, string $referenciaExterna, string $usuarioCreador): Cobro
    {
        // Aseguramos que la cantidad sea negativa
        if (isset($datos['cantidad']) && $datos['cantidad'] > 0) {
            $datos['cantidad'] = -$datos['cantidad'];
        }
        
        // Por defecto, las devoluciones NO están pagadas hasta que se confirme el reembolso
        $datos['pagado'] = 'No';
        
        // Almacenar la referencia externa para poder identificarla luego cuando llegue el webhook
        if (!isset($datos['adicional'])) {
            $datos['adicional'] = json_encode([
                'referencia_reembolso' => $referenciaExterna,
                'tipo' => 'devolucion'
            ]);
        } else {
            $adicional = is_string($datos['adicional']) ? json_decode($datos['adicional'], true) : $datos['adicional'];
            $adicional['referencia_reembolso'] = $referenciaExterna;
            $adicional['tipo'] = 'devolucion';
            $datos['adicional'] = json_encode($adicional);
        }
        
        // Usar el método crear general con los datos procesados
        return $this->crear($datos, $usuarioCreador);
    }

    /**
     * Marca una devolución como reembolsada (pagada)
     *
     * @param string $referenciaExterna ID de referencia del reembolso
     * @param string|null $observaciones Observaciones adicionales
     * @param string $usuarioModificador Usuario que modifica el registro
     * @return Cobro|null Cobro actualizado o null si no se encuentra
     */
    public function marcarDevolucionReembolsada(string $referenciaExterna, ?string $observaciones, string $usuarioModificador): ?Cobro
    {
        // Buscar el cobro de devolución por la referencia externa exacta
        $cobro = Cobro::whereRaw("JSON_EXTRACT(adicional, '$.referencia_reembolso') = ?", [$referenciaExterna])
            ->whereRaw("JSON_EXTRACT(adicional, '$.tipo') = 'devolucion'")
            ->where('pagado', 'No')
            ->first();
        
        // Si no encontramos por referencia exacta y parece ser una referencia generada (contiene '_refund')
        if (!$cobro && strpos($referenciaExterna, '_refund') !== false) {
            // Extraer la orden_id de la referencia generada
            $ordenId = str_replace('_refund', '', $referenciaExterna);
            
            Log::info('CobroService: Buscando devolución por orden_id', [
                'orden_id' => $ordenId,
                'referencia_generada' => $referenciaExterna
            ]);
            
            // Buscar por devoluciones pendientes y comparar si alguna podría corresponder a esta orden
            $cobro = Cobro::whereRaw("JSON_EXTRACT(adicional, '$.tipo') = 'devolucion'")
                ->where('pagado', 'No')
                ->first();
        }
        
        if (!$cobro) {
            Log::warning('CobroService: No se encontró cobro de devolución para referencia', [
                'referencia_externa' => $referenciaExterna
            ]);
            return null;
        }
        
        $datos = [
            'pagado' => 'Si',
            'forma_pago' => 'Tarjeta',
            'observaciones_pago' => $observaciones ?? 'Reembolso confirmado por la plataforma de pago',
            'notificacion_pago' => now(),
            'autorizacion_de_pago' => now(),
            'cantidad_pagada' => $cobro->cantidad, // La cantidad ya es negativa
            'modificado_por' => $usuarioModificador,
            'real_modificado' => now()
        ];
        
        $cobro->fill($datos);
        $cobro->save();
        
        return $cobro;
    }
}