<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AvatarService
{
    /**
     * Descarga una imagen de avatar desde una URL y la guarda localmente
     * 
     * @param string $url URL de la imagen a descargar
     * @return string|null URL local de la imagen guardada o null si hubo un error
     */
    public function downloadAndSaveAvatar(string $url): ?string
    {
        try {
            // Obtener el contenido de la imagen desde la URL
            $response = Http::get($url);
            
            if ($response->failed()) {
                return null;
            }
            
            // Generar un nombre único para la imagen
            $filename = 'avatar_' . Str::random(20) . '.jpg';
            $path = 'avatars/' . $filename;
            
            // Guardar la imagen en el almacenamiento público
            Storage::disk('public')->put($path, $response->body());
            
            // Devolver la URL pública de la imagen
            return Storage::url($path);
        } catch (\Exception $e) {
            report($e);
            return null;
        }
    }
} 