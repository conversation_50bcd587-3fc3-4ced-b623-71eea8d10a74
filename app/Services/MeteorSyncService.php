<?php

namespace App\Services;

use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Log;

class MeteorSyncService
{
    private string $meteorApiUrl;
    private string $apiKey;

    public function __construct()
    {
        $this->meteorApiUrl = config('services.meteor.api_url', 'https://ws-si.mulb.in/api/');
        $this->apiKey = config('services.meteor.api_key', 'dev-api-key-mulbin-2024');
    }

    /**
     * Verifica si un usuario existe en Meteor basándose en su configuración SI
     *
     * @param User $user Usuario de <PERSON>vel
     * @return bool True si el usuario existe en Meteor
     */
    public function usuarioExisteEnMeteor(User $user): bool
    {
        $siConfig = $user->siConfig;

        if (!$siConfig || empty($siConfig->meteor_id)) {
            Log::info('MeteorSync: Usuario no tiene meteor_id configurado', [
                'user_id' => $user->id,
                'usuario' => $user->usuario
            ]);
            return false;
        }

        try {
            $response = $this->sendMeteorApiRequest("users/{$siConfig->meteor_id}", 'GET');

            if ($response['success']) {
                Log::info('MeteorSync: Usuario encontrado en Meteor', [
                    'user_id' => $user->id,
                    'meteor_id' => $siConfig->meteor_id
                ]);
                return true;
            }

            Log::warning('MeteorSync: Usuario no encontrado en Meteor', [
                'user_id' => $user->id,
                'meteor_id' => $siConfig->meteor_id,
                'http_code' => $response['http_code']
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('MeteorSync: Error verificando usuario en Meteor', [
                'user_id' => $user->id,
                'meteor_id' => $siConfig->meteor_id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Sincroniza los datos del usuario con Meteor cuando se actualiza su perfil
     *
     * @param User $user Usuario actualizado
     * @param array $camposActualizados Campos específicos que se actualizaron
     * @return bool True si la sincronización fue exitosa
     */
    public function sincronizarUsuario(User $user, array $camposActualizados = []): bool
    {
        // Verificar si el usuario existe en Meteor
        if (!$this->usuarioExisteEnMeteor($user)) {
            Log::info('MeteorSync: Usuario no existe en Meteor, omitiendo sincronización', [
                'user_id' => $user->id,
                'usuario' => $user->usuario
            ]);
            return false;
        }

        $siConfig = $user->siConfig;

        try {
            // Preparar datos para actualización en Meteor
            Log::info('MeteorSync: Preparando datos para actualización', [
                'user_id' => $user->id,
                'campos_actualizados' => $camposActualizados
            ]);
            $updateData = $this->prepararDatosParaActualizacion($user, $camposActualizados);

            Log::info('MeteorSync: Datos preparados para envío', [
                'user_id' => $user->id,
                'update_data' => $updateData
            ]);

            if (empty($updateData)) {
                Log::info('MeteorSync: No hay datos para sincronizar', [
                    'user_id' => $user->id,
                    'campos_actualizados' => $camposActualizados
                ]);
                return true;
            }

            Log::info('MeteorSync: Sincronizando usuario con Meteor', [
                'user_id' => $user->id,
                'meteor_id' => $siConfig->meteor_id,
                'datos_actualizados' => array_keys($updateData)
            ]);

            $response = $this->sendMeteorApiRequest(
                "update-user/{$siConfig->meteor_id}",
                'PUT',
                $updateData
            );

            if ($response['success']) {
                Log::info('MeteorSync: Usuario sincronizado exitosamente', [
                    'user_id' => $user->id,
                    'meteor_id' => $siConfig->meteor_id
                ]);
                return true;
            }

            Log::error('MeteorSync: Error sincronizando usuario', [
                'user_id' => $user->id,
                'meteor_id' => $siConfig->meteor_id,
                'error' => $response['error'],
                'http_code' => $response['http_code']
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('MeteorSync: Excepción sincronizando usuario', [
                'user_id' => $user->id,
                'meteor_id' => $siConfig->meteor_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Sincroniza específicamente el avatar del usuario con Meteor
     *
     * @param User $user Usuario con avatar actualizado
     * @param string $avatarUrl URL del nuevo avatar
     * @return bool True si la sincronización fue exitosa
     */
    public function sincronizarAvatar(User $user, string $avatarUrl): bool
    {
        Log::info('MeteorSync: Sincronizando avatar específicamente', [
            'user_id' => $user->id,
            'usuario' => $user->usuario,
            'avatar_url' => $avatarUrl
        ]);

        return $this->sincronizarUsuario($user, ['avatar' => $avatarUrl]);
    }

    /**
     * Crea un usuario en Meteor si no existe
     *
     * @param User $user Usuario de Laravel
     * @param string $password Contraseña del usuario
     * @param array $datosAdicionales Datos adicionales para el perfil
     * @return array Resultado con success, meteor_id y data
     */
    public function crearUsuarioEnMeteor(User $user, string $password, array $datosAdicionales = []): array
    {
        try {
            // Verificar si ya existe un meteor_id
            $siConfig = $user->siConfig;
            if ($siConfig && !empty($siConfig->meteor_id)) {
                Log::warning('MeteorSync: Usuario ya tiene meteor_id, verificando existencia', [
                    'user_id' => $user->id,
                    'meteor_id' => $siConfig->meteor_id
                ]);

                if ($this->usuarioExisteEnMeteor($user)) {
                    return [
                        'success' => true,
                        'meteor_id' => $siConfig->meteor_id,
                        'message' => 'Usuario ya existe en Meteor'
                    ];
                }
            }

            // Preparar datos del usuario para Meteor
            $userData = [
                'email' => $user->email,
                'password' => $password,
                'username' => $user->usuario,
                'profile' => [
                    'firstName' => $user->nombre ?? '',
                    'lastName' => $user->apellidos ?? '',
                    'phone' => $user->telefono ?? '',
                    'company' => $user->empresa ?? '',
                    'location' => $user->estado ?? '',
                    'city' => $user->ciudad ?? '',
                    'bio' => 'Agente inmobiliario profesional',
                    'avatar' => $user->getAvatarUrl(),
                ],
                'roles' => ['user']
            ];

            // Agregar datos adicionales al perfil
            if (!empty($datosAdicionales)) {
                $userData['profile'] = array_merge($userData['profile'], $datosAdicionales);
            }

            Log::info('MeteorSync: Creando usuario en Meteor', [
                'user_id' => $user->id,
                'usuario' => $user->usuario,
                'email' => $user->email
            ]);

            $response = $this->sendMeteorApiRequest('users', 'POST', $userData);

            if ($response['success']) {
                $meteorUserId = $response['data']['userId'] ?? null;

                if ($meteorUserId) {
                    // Guardar el meteor_id en la configuración SI
                    $this->guardarMeteorId($user, $meteorUserId);

                    Log::info('MeteorSync: Usuario creado exitosamente en Meteor', [
                        'user_id' => $user->id,
                        'meteor_id' => $meteorUserId
                    ]);

                    return [
                        'success' => true,
                        'meteor_id' => $meteorUserId,
                        'data' => $response['data']
                    ];
                }
            }

            Log::error('MeteorSync: Error creando usuario en Meteor', [
                'user_id' => $user->id,
                'error' => $response['error'],
                'http_code' => $response['http_code']
            ]);

            return [
                'success' => false,
                'error' => $response['error'] ?? 'Error desconocido'
            ];
        } catch (Exception $e) {
            Log::error('MeteorSync: Excepción creando usuario en Meteor', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Excepción: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verifica si un username está disponible en Meteor
     *
     * @param string $username Username a verificar
     * @return array Resultado con exists, message y data
     */
    public function verificarUsername(string $username): array
    {
        try {
            $response = $this->sendMeteorApiRequest("users/check-username/{$username}", 'GET');

            if ($response['success']) {
                return [
                    'success' => true,
                    'data' => $response['data']
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];
        } catch (Exception $e) {
            Log::error('MeteorSync: Error verificando username', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Error verificando username: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepara los datos del usuario para actualización en Meteor
     *
     * @param User $user Usuario de Laravel
     * @param array $camposActualizados Campos específicos actualizados
     * @return array Datos preparados para la API de Meteor
     */
    private function prepararDatosParaActualizacion(User $user, array $camposActualizados): array
    {
        $updateData = [];

        // Mapeo de campos de Laravel a estructura de Meteor
        $mapeosCampos = [
            'name' => 'profile.fullName',
            'nombre' => 'profile.firstName',
            'apellidos' => 'profile.lastName',
            'telefono' => 'profile.phone',
            'empresa' => 'profile.company',
            'estado' => 'profile.location',
            'ciudad' => 'profile.city',
            'avatar' => 'profile.avatar',
            'email' => 'email'
        ];

        // Si no se especifican campos, sincronizar todos los principales
        if (empty($camposActualizados)) {
            $camposActualizados = array_keys($mapeosCampos);
        }

        foreach ($camposActualizados as $campo => $valor) {
            // Si el campo viene como key => value
            if (is_string($campo)) {
                $nombreCampo = $campo;
                $valorCampo = $valor;
            } else {
                // Si viene como array simple de nombres de campos
                $nombreCampo = $valor;
                $valorCampo = $this->obtenerValorCampo($user, $nombreCampo);
            }

            if (isset($mapeosCampos[$nombreCampo])) {
                $rutaMeteor = $mapeosCampos[$nombreCampo];

                // Crear la estructura anidada si es necesario
                if (strpos($rutaMeteor, '.') !== false) {
                    [$seccion, $subcampo] = explode('.', $rutaMeteor);

                    if (!isset($updateData[$seccion])) {
                        $updateData[$seccion] = [];
                    }

                    $updateData[$seccion][$subcampo] = $valorCampo;
                } else {
                    $updateData[$rutaMeteor] = $valorCampo;
                }
            }
        }

        return $updateData;
    }

    /**
     * Obtiene el valor de un campo específico del usuario
     *
     * @param User $user Usuario
     * @param string $nombreCampo Nombre del campo
     * @return mixed Valor del campo
     */
    private function obtenerValorCampo(User $user, string $nombreCampo)
    {
        switch ($nombreCampo) {
            case 'avatar':
                $avatarUrl = $user->getAvatarUrl();
                // Si la URL es relativa, convertirla a absoluta
                if ($avatarUrl && !filter_var($avatarUrl, FILTER_VALIDATE_URL)) {
                    return getenv('APP_URL') . $avatarUrl;
                }
                return $avatarUrl;
            case 'name':
                return $user->name ?? '';
            case 'nombre':
                return $user->nombre ?? '';
            case 'apellidos':
                return $user->apellidos ?? '';
            case 'telefono':
                return $user->telefono ?? '';
            case 'empresa':
                return $user->empresa ?? '';
            case 'estado':
                return $user->estado ?? '';
            case 'ciudad':
                return $user->ciudad ?? '';
            case 'email':
                return $user->email;
            default:
                return $user->getAttribute($nombreCampo) ?? '';
        }
    }

    /**
     * Guarda el meteor_id en la configuración SI del usuario
     *
     * @param User $user Usuario de Laravel
     * @param string $meteorId ID del usuario en Meteor
     * @return bool True si se guardó exitosamente
     */
    private function guardarMeteorId(User $user, string $meteorId): bool
    {
        try {
            $siConfig = $user->siConfig;

            if (!$siConfig) {
                Log::warning('MeteorSync: Usuario no tiene configuración SI', [
                    'user_id' => $user->id
                ]);
                return false;
            }

            $siConfig->update(['meteor_id' => $meteorId]);

            Log::info('MeteorSync: meteor_id guardado en configuración SI', [
                'user_id' => $user->id,
                'meteor_id' => $meteorId
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('MeteorSync: Error guardando meteor_id', [
                'user_id' => $user->id,
                'meteor_id' => $meteorId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Envía una petición HTTP a la API de Meteor
     *
     * @param string $endpoint Endpoint de la API (relativo a la base URL)
     * @param string $method Método HTTP
     * @param array|null $data Datos a enviar
     * @return array Respuesta con success, data, error y http_code
     */
    private function sendMeteorApiRequest(string $endpoint, string $method, ?array $data = null): array
    {
        try {
            $url = rtrim($this->meteorApiUrl, '/') . '/' . ltrim($endpoint, '/');

            Log::info('MeteorSync: Enviando petición a Meteor', [
                'url' => $url,
                'method' => $method,
                'data' => $data
            ]);

            $headers = [
                'Authorization: ApiKey ' . $this->apiKey,
                'Content-Type: application/json',
                'Accept: application/json'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Para desarrollo

            if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                return [
                    'success' => false,
                    'error' => 'cURL Error: ' . $curlError,
                    'http_code' => 0,
                    'data' => null
                ];
            }

            $decodedResponse = json_decode($response, true);

            if ($httpCode >= 200 && $httpCode < 300) {
                return [
                    'success' => true,
                    'data' => $decodedResponse,
                    'http_code' => $httpCode,
                    'error' => null
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $decodedResponse['error'] ?? 'HTTP Error ' . $httpCode,
                    'http_code' => $httpCode,
                    'data' => $decodedResponse
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage(),
                'http_code' => 0,
                'data' => null
            ];
        }
    }
}
