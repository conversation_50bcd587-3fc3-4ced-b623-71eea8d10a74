<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Contracts\User as SocialiteUser;
use Exception;

class OAuthLoggingService
{
    /**
     * Log información detallada de un intento de autenticación OAuth
     */
    public function logAuthAttempt(string $provider, ?SocialiteUser $socialiteUser = null, ?Exception $exception = null): void
    {
        $context = [
            'provider' => $provider,
            'timestamp' => now()->toISOString(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ];

        if ($socialiteUser) {
            $context['socialite_user'] = [
                'id' => $socialiteUser->getId(),
                'email' => $socialiteUser->getEmail(),
                'name' => $socialiteUser->getName(),
                'has_avatar' => !empty($socialiteUser->getAvatar()),
                'has_token' => !empty($socialiteUser->token),
                'has_refresh_token' => !empty($socialiteUser->refreshToken),
            ];
        }

        if ($exception) {
            $context['error'] = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];

            Log::error("OAuth {$provider} authentication failed", $context);
        } else {
            Log::info("OAuth {$provider} authentication attempt", $context);
        }
    }

    /**
     * Log información específica de errores de Socialite
     */
    public function logSocialiteError(string $provider, Exception $exception): void
    {
        $errorType = $this->categorizeError($exception);
        
        $context = [
            'provider' => $provider,
            'error_type' => $errorType,
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'request_url' => request()->fullUrl(),
            'request_params' => request()->all(),
            'timestamp' => now()->toISOString(),
        ];

        Log::error("Socialite {$provider} error: {$errorType}", $context);
    }

    /**
     * Log éxito en autenticación OAuth
     */
    public function logAuthSuccess(string $provider, $user, bool $isNewUser = false): void
    {
        $context = [
            'provider' => $provider,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'is_new_user' => $isNewUser,
            'timestamp' => now()->toISOString(),
        ];

        Log::info("OAuth {$provider} authentication successful", $context);
    }

    /**
     * Log problemas con el servicio de avatares
     */
    public function logAvatarError(string $provider, string $avatarUrl, Exception $exception): void
    {
        $context = [
            'provider' => $provider,
            'avatar_url' => $avatarUrl,
            'error_message' => $exception->getMessage(),
            'timestamp' => now()->toISOString(),
        ];

        Log::warning("OAuth {$provider} avatar download failed", $context);
    }

    /**
     * Categorizar el tipo de error para mejor debugging
     */
    private function categorizeError(Exception $exception): string
    {
        $message = strtolower($exception->getMessage());
        
        if (str_contains($message, 'invalid_request')) {
            return 'INVALID_REQUEST';
        }
        
        if (str_contains($message, 'access_denied')) {
            return 'ACCESS_DENIED';
        }
        
        if (str_contains($message, 'invalid_client')) {
            return 'INVALID_CLIENT_CREDENTIALS';
        }
        
        if (str_contains($message, 'invalid_grant')) {
            return 'INVALID_GRANT';
        }
        
        if (str_contains($message, 'network') || str_contains($message, 'timeout')) {
            return 'NETWORK_ERROR';
        }
        
        if (str_contains($message, 'curl')) {
            return 'CURL_ERROR';
        }
        
        return 'UNKNOWN_ERROR';
    }
}
