<?php

namespace App\Services;

use App\Models\ConektaCustomer;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConektaService
{
    /**
     * API Key de Conekta
     *
     * @var string
     */
    protected $apiKey;

    /**
     * API Secret de Conekta
     *
     * @var string
     */
    protected $apiSecret;

    /**
     * URL base de la API de Conekta
     *
     * @var string
     */
    protected $apiUrl = 'https://api.conekta.io';

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = config('services.conekta.key');
        $this->apiSecret = config('services.conekta.secret');
    }

    /**
     * Crea un token para el checkout de Conekta
     *
     * @return array|null
     */
    public function createToken()
    {
        try {
            $response = Http::withBasicAuth($this->apiSecret, '')
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ])
                ->post($this->apiUrl . '/tokens', []);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'checkout_id' => $data['id']
                ];
            }

            Log::error('Error al crear token de Conekta: ' . $response->body());
            return null;
        } catch (\Exception $e) {
            Log::error('Excepción al crear token de Conekta: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Crea o actualiza un cliente en Conekta
     *
     * @param User $user
     * @param string $tokenId
     * @return ConektaCustomer|null
     */
    public function createOrUpdateCustomer(User $user, string $tokenId)
    {
        try {
            // Verificar si el usuario ya tiene un cliente en Conekta
            $conektaCustomer = $user->conektaCustomer;

            if ($conektaCustomer) {
                // Actualizar cliente existente
                $response = Http::withBasicAuth($this->apiSecret, '')
                    ->withHeaders([
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ])
                    ->put($this->apiUrl . '/customers/' . $conektaCustomer->conekta_id, [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->telefono ?? '',
                        'payment_sources' => [
                            [
                                'type' => 'card',
                                'token_id' => $tokenId
                            ]
                        ]
                    ]);
            } else {
                // Crear nuevo cliente
                $response = Http::withBasicAuth($this->apiSecret, '')
                    ->withHeaders([
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ])
                    ->post($this->apiUrl . '/customers', [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->telefono ?? '',
                        'payment_sources' => [
                            [
                                'type' => 'card',
                                'token_id' => $tokenId
                            ]
                        ]
                    ]);
            }

            if ($response->successful()) {
                $data = $response->json();

                // Extraer información de las fuentes de pago
                $paymentSources = [];
                $defaultSourceId = null;

                if (!empty($data['payment_sources']['data'])) {
                    foreach ($data['payment_sources']['data'] as $source) {
                        $paymentSources[] = [
                            'id' => $source['id'],
                            'brand' => $source['brand'] ?? '',
                            'last4' => $source['last4'] ?? '',
                            'exp_month' => $source['exp_month'] ?? '',
                            'exp_year' => $source['exp_year'] ?? '',
                            'name' => $source['name'] ?? '',
                        ];

                        // Usar la primera fuente como predeterminada si no hay una
                        if (!$defaultSourceId) {
                            $defaultSourceId = $source['id'];
                        }
                    }
                }

                // Crear o actualizar el registro del cliente
                if (!$conektaCustomer) {
                    $conektaCustomer = new ConektaCustomer([
                        'user_id' => $user->id,
                        'conekta_id' => $data['id'],
                        'payment_sources' => $paymentSources,
                        'default_payment_source_id' => $defaultSourceId,
                    ]);
                    $conektaCustomer->save();
                } else {
                    $conektaCustomer->payment_sources = $paymentSources;
                    $conektaCustomer->default_payment_source_id = $defaultSourceId;
                    $conektaCustomer->save();
                }

                return $conektaCustomer;
            }

            Log::error('Error al crear/actualizar cliente en Conekta: ' . $response->body());
            return null;
        } catch (\Exception $e) {
            Log::error('Excepción al crear/actualizar cliente en Conekta: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Crea una suscripción en Conekta
     *
     * @param User $user
     * @param string $planId
     * @param string $billingPeriod
     * @param bool $personalization
     * @param float $planPrice
     * @param float $totalPrice
     * @return Subscription|null
     */
    public function createSubscription(
        User $user,
        string $planId,
        string $billingPeriod,
        bool $personalization,
        float $planPrice,
        float $totalPrice
    ) {
        try {
            // Verificar que el usuario tenga un cliente en Conekta
            $conektaCustomer = $user->conektaCustomer;

            if (!$conektaCustomer) {
                Log::error('No se puede crear suscripción: el usuario no tiene un cliente en Conekta');
                return null;
            }

            // Crear la suscripción en Conekta
            $response = Http::withBasicAuth($this->apiSecret, '')
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ])
                ->post($this->apiUrl . '/customers/' . $conektaCustomer->conekta_id . '/subscriptions', [
                    'plan' => $planId,
                ]);

            if ($response->successful()) {
                $data = $response->json();

                // Crear el registro de suscripción en la base de datos
                $subscription = new Subscription([
                    'user_id' => $user->id,
                    'conekta_id' => $data['id'],
                    'conekta_plan' => $planId,
                    'conekta_customer' => $conektaCustomer->conekta_id,
                    'status' => $data['status'],
                    'billing_period' => $billingPeriod,
                    'plan_price' => $planPrice,
                    'total_price' => $totalPrice,
                    'personalization' => $personalization,
                ]);

                // Si hay un período de prueba
                if (!empty($data['trial_end'])) {
                    $subscription->trial_ends_at = date('Y-m-d H:i:s', $data['trial_end']);
                }

                $subscription->save();

                return $subscription;
            }

            Log::error('Error al crear suscripción en Conekta: ' . $response->body());
            return null;
        } catch (\Exception $e) {
            Log::error('Excepción al crear suscripción en Conekta: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Cancela una suscripción en Conekta
     *
     * @param Subscription $subscription
     * @return bool
     */
    public function cancelSubscription(Subscription $subscription)
    {
        try {
            $response = Http::withBasicAuth($this->apiSecret, '')
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ])
                ->delete(
                    $this->apiUrl . '/customers/' . $subscription->conekta_customer . '/subscriptions/' . $subscription->conekta_id
                );

            if ($response->successful()) {
                $subscription->status = 'canceled';
                $subscription->ends_at = now();
                $subscription->save();

                return true;
            }

            Log::error('Error al cancelar suscripción en Conekta: ' . $response->body());
            return false;
        } catch (\Exception $e) {
            Log::error('Excepción al cancelar suscripción en Conekta: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Pausa una suscripción en Conekta
     *
     * @param Subscription $subscription
     * @return bool
     */
    public function pauseSubscription(Subscription $subscription)
    {
        try {
            $response = Http::withBasicAuth($this->apiSecret, '')
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ])
                ->post(
                    $this->apiUrl . '/customers/' . $subscription->conekta_customer . '/subscriptions/' . $subscription->conekta_id . '/pause'
                );

            if ($response->successful()) {
                $subscription->status = 'paused';
                $subscription->save();

                return true;
            }

            Log::error('Error al pausar suscripción en Conekta: ' . $response->body());
            return false;
        } catch (\Exception $e) {
            Log::error('Excepción al pausar suscripción en Conekta: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Reanuda una suscripción en Conekta
     *
     * @param Subscription $subscription
     * @return bool
     */
    public function resumeSubscription(Subscription $subscription)
    {
        try {
            $response = Http::withBasicAuth($this->apiSecret, '')
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ])
                ->post(
                    $this->apiUrl . '/customers/' . $subscription->conekta_customer . '/subscriptions/' . $subscription->conekta_id . '/resume'
                );

            if ($response->successful()) {
                $subscription->status = 'active';
                $subscription->save();

                return true;
            }

            Log::error('Error al reanudar suscripción en Conekta: ' . $response->body());
            return false;
        } catch (\Exception $e) {
            Log::error('Excepción al reanudar suscripción en Conekta: ' . $e->getMessage());
            return false;
        }
    }
}
