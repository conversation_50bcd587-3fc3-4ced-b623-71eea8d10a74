<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class RecaptchaService
{
    /**
     * Validate a reCAPTCHA response.
     *
     * @param string $recaptchaResponse
     * @return bool
     */
    public function validate(string $recaptchaResponse): bool
    {
        // Si estamos en entorno local o de pruebas, podemos omitir la validación
        if (app()->environment('local', 'testing') && empty(config('recaptcha.secret_key'))) {
            return true;
        }

        $response = Http::asForm()->post(config('recaptcha.validation_url'), [
            'secret' => config('recaptcha.secret_key'),
            'response' => $recaptchaResponse,
            'remoteip' => request()->ip(),
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['success'] ?? false;
        }

        return false;
    }
}
