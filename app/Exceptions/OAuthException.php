<?php

namespace App\Exceptions;

use Exception;

class OAuthException extends Exception
{
    protected $provider;
    protected $errorType;
    protected $userFriendlyMessage;

    public function __construct(
        string $provider,
        string $errorType,
        string $message,
        string $userFriendlyMessage = null,
        int $code = 0,
        Exception $previous = null
    ) {
        $this->provider = $provider;
        $this->errorType = $errorType;
        $this->userFriendlyMessage = $userFriendlyMessage ?? $this->getDefaultUserMessage($errorType);
        
        parent::__construct($message, $code, $previous);
    }

    public function getProvider(): string
    {
        return $this->provider;
    }

    public function getErrorType(): string
    {
        return $this->errorType;
    }

    public function getUserFriendlyMessage(): string
    {
        return $this->userFriendlyMessage;
    }

    private function getDefaultUserMessage(string $errorType): string
    {
        return match ($errorType) {
            'ACCESS_DENIED' => 'Has cancelado la autorización. Para continuar, necesitas autorizar el acceso a tu cuenta.',
            'INVALID_CLIENT_CREDENTIALS' => 'Error de configuración del servicio. Por favor, contacta al soporte técnico.',
            'NETWORK_ERROR' => 'Error de conexión. Por favor, verifica tu conexión a internet e intenta nuevamente.',
            'INVALID_REQUEST' => 'Error en la solicitud de autenticación. Por favor, intenta nuevamente.',
            'INVALID_GRANT' => 'La autorización ha expirado. Por favor, intenta autenticarte nuevamente.',
            'AVATAR_DOWNLOAD_FAILED' => 'No se pudo descargar tu foto de perfil, pero tu cuenta se ha creado correctamente.',
            'USER_CREATION_FAILED' => 'Error al crear tu cuenta. Por favor, intenta nuevamente o contacta al soporte.',
            'DATABASE_ERROR' => 'Error interno del sistema. Por favor, intenta nuevamente en unos momentos.',
            default => 'Ha ocurrido un error inesperado durante la autenticación. Por favor, intenta nuevamente.',
        };
    }

    /**
     * Crear una excepción específica para errores de Socialite
     */
    public static function socialiteError(string $provider, Exception $previous): self
    {
        $message = $previous->getMessage();
        $errorType = 'SOCIALITE_ERROR';
        
        // Detectar tipos específicos de error
        if (str_contains(strtolower($message), 'access_denied')) {
            $errorType = 'ACCESS_DENIED';
        } elseif (str_contains(strtolower($message), 'invalid_client')) {
            $errorType = 'INVALID_CLIENT_CREDENTIALS';
        } elseif (str_contains(strtolower($message), 'network') || str_contains(strtolower($message), 'curl')) {
            $errorType = 'NETWORK_ERROR';
        }

        return new self(
            $provider,
            $errorType,
            "Socialite {$provider} error: {$message}",
            null,
            $previous->getCode(),
            $previous
        );
    }

    /**
     * Crear una excepción para errores de descarga de avatar
     */
    public static function avatarDownloadFailed(string $provider, Exception $previous): self
    {
        return new self(
            $provider,
            'AVATAR_DOWNLOAD_FAILED',
            "Avatar download failed for {$provider}: {$previous->getMessage()}",
            null,
            0,
            $previous
        );
    }

    /**
     * Crear una excepción para errores de base de datos
     */
    public static function databaseError(string $provider, Exception $previous): self
    {
        return new self(
            $provider,
            'DATABASE_ERROR',
            "Database error during {$provider} authentication: {$previous->getMessage()}",
            null,
            0,
            $previous
        );
    }
}
