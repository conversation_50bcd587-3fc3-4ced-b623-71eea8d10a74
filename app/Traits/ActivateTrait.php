<?php

namespace App\Traits;

use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

trait ActivateTrait
{
    /**
     * Muestra el formulario de activación
     */
    public function activar()
    {
        $user = Auth::user();

        // Verificar si el usuario ya tiene su sitio web activado
        if ($user->isWebsiteActivated()) {
            // Si ya está activado, redirigir a la página de activación exitosa
            return redirect()->route('activacion.exitosa');
        }

        // Pasamos los datos del usuario al formulario para prellenarlo
        return view('sistema-inmobiliario.activar', [
            'user' => $user
        ]);
    }

    /**
     * Procesa el formulario de activación
     */
    public function procesarActivacion(Request $request)
    {
        Log::info('SistemaInmobiliarioController: Iniciando procesarActivacion');
        Log::info('SistemaInmobiliarioController: Request method: ' . $request->method());
        Log::info('SistemaInmobiliarioController: Request path: ' . $request->path());
        Log::info('SistemaInmobiliarioController: Request all: ', $request->all());

        $user = Auth::user();

        // Verificar si el usuario ya tiene su sitio web activado
        if ($user->isWebsiteActivated()) {
            // Si ya está activado, redirigir a la página de activación exitosa
            return redirect()->route('activacion.exitosa');
        }

        // Obtener datos de la sesión
        $activationData = session('activation_data');
        Log::info('SistemaInmobiliarioController: Datos de activación obtenidos de la sesión', [
            'activationData' => $activationData ?? 'null'
        ]);

        if (!$activationData) {
            Log::warning('SistemaInmobiliarioController: No se encontraron datos de activación en la sesión');
            return redirect()->route('activar')->withErrors(
                ['general' => 'No se encontraron datos de activación. Por favor intente nuevamente.']
            );
        }

        // Validar los datos
        $validator = validator($activationData, [
            'selectedPlan' => 'required|string',
            'billingPeriod' => 'required|string',
            'planPrice' => 'required|numeric',
            'totalPrice' => 'required|numeric',
            'token_id' => 'required_if:selectedPlan,web,pro',
        ]);

        if ($validator->fails()) {
            return redirect()->route('activar')->withErrors($validator);
        }

        // Guardar la información del plan seleccionado en la sesión para mostrarla en la página de éxito
        session([
            'plan' => $activationData['selectedPlan'],
            'billing_period' => $activationData['billingPeriod'],
            'plan_price' => $activationData['planPrice'],
            'total_price' => $activationData['totalPrice'],
        ]);

        // Si es plan freemium, actualizamos los datos del usuario y activamos el portal
        if ($activationData['selectedPlan'] === 'freemium') {
            Log::info('SistemaInmobiliarioController: Procesando plan freemium');

            // Obtenemos los datos del usuario desde la sesión o request
            $userData = session('user_data', []);
            Log::info('SistemaInmobiliarioController: Datos de usuario obtenidos', ['userData' => $userData]);

            // Actualizamos los datos del usuario si están disponibles
            if (!empty($userData)) {
                $user->usuario = $userData['username'] ?? $user->usuario;
                $user->empresa = $userData['empresa'] ?? $user->empresa;
                $user->telefono = isset($userData['phone_country_code'], $userData['phone_number']) ?
                    $userData['phone_country_code'] . ' ' . $userData['phone_number'] : $user->telefono;
                $user->calle_numero = $userData['calle_numero'] ?? $user->calle_numero;
                $user->colonia = $userData['colonia'] ?? $user->colonia;
                $user->codigo_postal = $userData['codigo_postal'] ?? $user->codigo_postal;
                $user->ciudad = $userData['ciudad'] ?? $user->ciudad;
                $user->estado = $userData['estado'] ?? $user->estado;
                $user->pais = $userData['pais'] ?? $user->pais;
            }

            // Activamos el portal
            $user->website_activated_at = now()->addHours(24);
            $user->save();

            Log::info('SistemaInmobiliarioController: Portal activado para usuario', [
                'user_id' => $user->id,
                'datos_guardados' => !empty($userData)
            ]);

            // Limpiar los datos de activación y usuario de la sesión
            session()->forget(['activation_data', 'user_data']);
            Log::info('SistemaInmobiliarioController: Datos eliminados de la sesión');

            return redirect()->route('activacion.exitosa');
        }

        // Si es un plan de pago, procesamos con Conekta
        $paymentService = app(PaymentService::class);

        try {
            Log::info('SistemaInmobiliarioController: Procesando plan pagado', [
                'plan' => $activationData['selectedPlan'],
                'periodo' => $activationData['billingPeriod']
            ]);

            // Validar que el token_id esté presente
            if (empty($activationData['token_id'])) {
                Log::warning('SistemaInmobiliarioController: Token de tarjeta no proporcionado');
                return back()->withErrors(
                    ['token_id' => 'No se ha proporcionado un token válido para la tarjeta. Por favor valide su tarjeta primero.']
                );
            }

            Log::info('SistemaInmobiliarioController: Token de tarjeta presente', [
                'token' => substr($activationData['token_id'], 0, 5) . '...'
            ]);

            // Mapear los ID's de planes según corresponda en Conekta
            $planConektaId = 'plan-' . $activationData['selectedPlan'] . '-' . $activationData['billingPeriod'];
            Log::info('SistemaInmobiliarioController: Plan Conekta ID generado', ['planConektaId' => $planConektaId]);

            // Crear cliente y suscripción en Conekta
            Log::info('SistemaInmobiliarioController: Llamando a createAndSubscribeCustomer', [
                'email' => $user->email,
                'name' => $user->name
            ]);

            $result = $paymentService->createAndSubscribeCustomer(
                $activationData['token_id'],
                $user->email,
                $user->name,
                $planConektaId
            );

            Log::info('SistemaInmobiliarioController: Resultado de createAndSubscribeCustomer', [
                'success' => !isset($result['error']),
                'error' => $result['error'] ?? null,
                'hasCustomer' => isset($result['Customer']),
                'hasSubscription' => isset($result['Subscription'])
            ]);

            // Verificar si hubo error
            if (isset($result['error'])) {
                Log::error('Error al crear cliente/suscripción en Conekta: ' . $result['error']);
                return back()->withErrors(['general' => 'Error al procesar el pago: ' . $result['error']]);
            }

            // Verificar que los datos necesarios estén presentes
            if (!isset($result['Customer']) || !isset($result['Customer']['id']) || !isset($result['Subscription']) || !isset($result['Subscription']['id'])) {
                Log::error('Respuesta incompleta de Conekta: ' . json_encode($result));
                return back()->withErrors(
                    ['general' => 'Error al procesar el pago: respuesta incompleta del servidor de pagos.']
                );
            }

            // Guardar la información del cliente en nuestra base de datos
            $this->updateOrCreateConektaCustomer($user, $result, $activationData, $planConektaId);
        } catch (Exception $e) {
            Log::error('Excepción al procesar pago con Conekta: ' . $e->getMessage());
            return back()->withErrors(['general' => 'Error inesperado al procesar el pago. Por favor intente de nuevo.']
            );
        }

        // Activamos el portal para dentro de 24 horas
        $user->website_activated_at = now()->addHours(24);
        $user->save();

        // Limpiar los datos de activación de la sesión
        session()->forget('activation_data');

        // Redirigir a la página de activación exitosa
        return redirect()->route('activacion.exitosa');
    }

    /**
     * Muestra la página de activación exitosa
     */
    public function activacionExitosa()
    {
        $user = Auth::user();

        // Verificar si el usuario NO tiene su sitio web activado
        if (!$user->isWebsiteActivated()) {
            // Si no está activado, redirigir a la página de activación
            return redirect()->route('activar');
        }

        return view('sistema-inmobiliario.activacion-exitosa');
    }
}