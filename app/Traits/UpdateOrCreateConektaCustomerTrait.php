<?php

namespace App\Traits;

use App\Models\ConektaCustomer;
use App\Models\Subscription;
use App\Models\User;

trait UpdateOrCreateConektaCustomerTrait
{
    protected function updateOrCreateConektaCustomer(
        User $user,
        array $result,
        array $activationData,
        string $planConektaId
    ): void {
        ConektaCustomer::updateOrCreate(
            ['user_id' => $user->id],
            [
                'conekta_id' => $result['Customer']['id'],
                'payment_sources' => [
                    [
                        'id' => $result['Customer']['payment_sources']['data'][0]['id'],
                        'brand' => $result['Customer']['payment_sources']['data'][0]['brand'] ?? '',
                        'last4' => $result['Customer']['payment_sources']['data'][0]['last4'] ?? '',
                        'exp_month' => $result['Customer']['payment_sources']['data'][0]['exp_month'] ?? '',
                        'exp_year' => $result['Customer']['payment_sources']['data'][0]['exp_year'] ?? '',
                        'name' => $result['Customer']['payment_sources']['data'][0]['name'] ?? '',
                    ]
                ],
                'default_payment_source_id' => $result['Customer']['payment_sources']['data'][0]['id'] ?? null,
            ]
        );

        // Guardar la información de la suscripción
        $subscription = new Subscription([
            'user_id' => $user->id,
            'conekta_id' => $result['Subscription']['id'],
            'conekta_plan' => $planConektaId,
            'conekta_customer' => $result['Customer']['id'],
            'status' => $result['Subscription']['status'],
            'billing_period' => $activationData['billingPeriod'],
            'plan_price' => (float)$activationData['planPrice'],
            'total_price' => (float)$activationData['totalPrice'],
            'personalization' => false,
        ]);

        // Si hay un período de prueba
        if (!empty($result['Subscription']['trial_end'])) {
            $subscription->trial_ends_at = date('Y-m-d H:i:s', $result['Subscription']['trial_end']);
        }

        $subscription->save();
    }

}