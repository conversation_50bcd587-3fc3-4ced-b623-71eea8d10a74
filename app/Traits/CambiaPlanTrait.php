<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

trait CambiaPlanTrait
{
    public function cambiarPlan()
    {
        $user = Auth::user();

        // Verificar si el usuario ya tiene su sitio web activado
        if (!$user->isWebsiteActivated()) {
            return redirect()->route('activar');
        }

        return view('sistema-inmobiliario.cambiar-plan');
    }

    public function procesarCambioPlan(Request $request)
    {
        // Validar el formulario
        $validatedData = $request->validate([
            'plan' => 'required|in:SI-FREE,SI-PRO,SI-PLUS',
            'billing_period' => 'required|in:monthly,quarterly,biannual,annual',
        ]);

        // Lógica para procesar el cambio de plan
        // ...

        return redirect()->route('dashboard')->with('success', 'Plan cambiado exitosamente');
    }
}
