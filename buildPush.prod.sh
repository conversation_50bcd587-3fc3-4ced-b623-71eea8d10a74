#!/bin/sh

# Definir variables
DOCKERFILE="Dockerfile.production"
IMAGE_NAME="landing-mulbin"
REPOSITORY="publiweb"
VERSION="$(date +%Y.%m.%d.%H%M)"
TAG="production"
LATEST="latest"
NAMESPACE="mulbin-landing"
PLATFORM="linux/amd64,linux/arm64/v8"

# Función para manejar errores y limpieza
handle_error() {
    echo "Error: $1"
    exit 1
}

echo "=== Iniciando proceso de construcción de imagen Docker ==="

# Construir la imagen Docker
echo "Construyendo la imagen multiplataforma con la configuración actualizada..."
docker buildx build \
    --file $DOCKERFILE \
    --platform $PLATFORM \
    -t $REPOSITORY/$IMAGE_NAME:$VERSION \
    -t $REPOSITORY/$IMAGE_NAME:$TAG \
    -t $REPOSITORY/$IMAGE_NAME:$LATEST \
    --push . || handle_error "Falló la construcción de la imagen Docker"

echo "=== Proceso completado exitosamente ==="
echo "La imagen ha sido actualizada en:"
echo "- $REPOSITORY/$IMAGE_NAME:$TAG"
echo "- $REPOSITORY/$IMAGE_NAME:$VERSION"
