# ------------------------------------------------------------------------------------------------
# Etapa de compilación de PHP
FROM composer:2 AS php-build
WORKDIR /app
COPY . .
RUN composer install --no-dev --optimize-autoloader --ignore-platform-reqs

# ------------------------------------------------------------------------------------------------
# Etapa de compilación del frontend
FROM node:20-alpine AS frontend-build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY vite.config.js ./
COPY resources/ ./resources/
COPY public/ ./public/
# Copiamos los archivos de vendor desde la etapa de PHP para que estén disponibles durante la compilación
COPY --from=php-build /app/vendor ./vendor
RUN npm run build

# ------------------------------------------------------------------------------------------------
# Imagen final
FROM publiweb/landing-mulbin:base-production

# Configurar Nginx y PHP
COPY docker/nginx/default.conf /etc/nginx/http.d/default.conf
COPY docker/php/php-prod.ini /usr/local/etc/php/php.ini
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

WORKDIR /var/www/html

# Copiar el código optimizado
COPY --chown=www-data:www-data --from=php-build /app /var/www/html
COPY --from=frontend-build /app/public/build /var/www/html/public/build

# Crear directorios necesarios y establecer permisos correctos
RUN mkdir -p storage/app/public \
    && mkdir -p storage/framework/cache \
    && mkdir -p storage/framework/sessions \
    && mkdir -p storage/framework/views \
    && mkdir -p storage/logs \
    && mkdir -p bootstrap/cache \
    && chmod -R 775 storage bootstrap/cache \
    && chown -R www-data:www-data storage bootstrap/cache

# Scripts de inicio
COPY docker/prod-start.sh /usr/local/bin/prod-start.sh
RUN chmod +x /usr/local/bin/prod-start.sh

EXPOSE 80

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"] 