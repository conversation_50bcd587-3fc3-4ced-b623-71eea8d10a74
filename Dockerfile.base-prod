FROM php:8.2-fpm-alpine

# Instalar dependencias permanentes del sistema
RUN apk add --no-cache \
    nginx \
    supervisor \
    libpng \
    libjpeg \
    libzip \
    curl \
    libcurl \
    oniguruma \
    icu \
    && mkdir -p /run/nginx \
    && mkdir -p /var/log/supervisor

# Instalar extensiones PHP con dependencias de compilación
RUN apk add --no-cache --virtual .build-deps \
    $PHPIZE_DEPS \
    libpng-dev \
    jpeg-dev \
    libzip-dev \
    oniguruma-dev \
    curl-dev \
    libcurl \
    icu-dev \
    && docker-php-ext-configure gd --with-jpeg \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        zip \
        gd \
        mbstring \
        opcache \
        curl \
        intl \
        exif \
    && apk del .build-deps
