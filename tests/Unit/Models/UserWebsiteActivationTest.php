<?php

namespace Tests\Unit\Models;

use PHPUnit\Framework\TestCase;

class UserWebsiteActivationTest extends TestCase
{
    /**
     * Test that the isWebsiteActivated method works correctly
     */
    public function test_website_activation_logic()
    {
        // Create a test class that implements the isWebsiteActivated method
        $testClass = new class {
            public $website_activated_at = null;
            public $has_plan_si_contract = false;

            public function isWebsiteActivated(): bool
            {
                // Check if website_activated_at is set
                if (!is_null($this->website_activated_at)) {
                    return true;
                }

                // Check if the user has any contract with a service of type 'PLAN-SI'
                return $this->has_plan_si_contract;
            }
        };

        // Test case 1: website_activated_at is set
        $testClass->website_activated_at = now();
        $testClass->has_plan_si_contract = false;
        $this->assertTrue($testClass->isWebsiteActivated(), 'Should be activated when website_activated_at is set');

        // Test case 2: website_activated_at is null, but has PLAN-SI contract
        $testClass->website_activated_at = null;
        $testClass->has_plan_si_contract = true;
        $this->assertTrue($testClass->isWebsiteActivated(), 'Should be activated when has PLAN-SI contract');

        // Test case 3: website_activated_at is null and no PLAN-SI contract
        $testClass->website_activated_at = null;
        $testClass->has_plan_si_contract = false;
        $this->assertFalse($testClass->isWebsiteActivated(), 'Should not be activated when no conditions are met');
    }
}
