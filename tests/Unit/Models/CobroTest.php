<?php

namespace Tests\Unit\Models;

use App\Models\Cobro;
use Carbon\Carbon;
use PHPUnit\Framework\TestCase;
use ReflectionMethod;

class CobroTest extends TestCase
{
    /**
     * Prueba la función formatearFecha para asegurar que formatea correctamente diferentes fechas
     */
    public function test_formatearFecha_formats_dates_correctly()
    {
        // Crear una clase anónima que extienda la clase Cobro para acceder al método formatearFecha
        $mockCobro = new class extends Cobro {
            public function formatearFechaPublic($fecha)
            {
                return $this->formatearFecha($fecha);
            }
        };
        
        // Probar diferentes fechas para verificar el formato
        $testDates = [
            ['date' => Carbon::create(2025, 1, 15), 'expected' => '15-ene-2025'],
            ['date' => Carbon::create(2025, 4, 7), 'expected' => '07-abr-2025'],
            ['date' => Carbon::create(2025, 12, 31), 'expected' => '31-dic-2025'],
        ];
        
        foreach ($testDates as $test) {
            $formatted = $mockCobro->formatearFechaPublic($test['date']);
            $this->assertEquals($test['expected'], $formatted);
        }
    }
    
    /**
     * Prueba que la estructura del campo pabusqueda se construye correctamente
     */
    public function test_pabusqueda_format_is_correct()
    {
        // Crear un mock para la clase CobroForTest con valores fijos
        $mockCobro = $this->getMockBuilder(CobroForTest::class)
            ->onlyMethods([])
            ->getMock();
        
        // Establecer propiedades
        $mockCobro->numero = 54321;
        $mockCobro->contrato = 12345;
        $mockCobro->usuario = 'usuario_test';
        $mockCobro->descripcion = 'Servicio mensual';
        $mockCobro->dominio = 'dominio_test.com';
        $mockCobro->precio = 100.50;
        $mockCobro->cantidad_pagada = 0;
        $mockCobro->desde = Carbon::create(2025, 4, 7);
        $mockCobro->hasta = Carbon::create(2025, 5, 7);
        
        // Llamar al método updatePaBusqueda
        $mockCobro->updatePaBusquedaPublic();
        
        // Verificar que el campo pabusqueda contiene la cadena esperada
        $expected = "cobro 54321 12345 usuario_test 07-abr-2025 07-may-2025 Servicio mensual dominio_test.com 100.5 0";
        $this->assertEquals($expected, $mockCobro->pabusqueda);
    }
    
    /**
     * Prueba que el campo pabusqueda se actualiza al cambiar los valores del cobro
     */
    public function test_pabusqueda_updates_when_values_change()
    {
        // Crear un mock para la clase CobroForTest
        $mockCobro = $this->getMockBuilder(CobroForTest::class)
            ->onlyMethods([])
            ->getMock();
        
        // Configuración inicial
        $mockCobro->numero = 54321;
        $mockCobro->contrato = 12345;
        $mockCobro->usuario = 'usuario_inicial';
        $mockCobro->descripcion = 'Servicio inicial';
        $mockCobro->dominio = 'dominio_inicial.com';
        $mockCobro->precio = 100.00;
        $mockCobro->cantidad_pagada = 0;
        $mockCobro->desde = Carbon::create(2025, 4, 7);
        $mockCobro->hasta = Carbon::create(2025, 5, 7);
        
        // Actualizar pabusqueda con valores iniciales
        $mockCobro->updatePaBusquedaPublic();
        
        // Verificar el valor inicial
        $expected = "cobro 54321 12345 usuario_inicial 07-abr-2025 07-may-2025 Servicio inicial dominio_inicial.com 100 0";
        $this->assertEquals($expected, $mockCobro->pabusqueda);
        
        // Cambiar algunos valores
        $mockCobro->usuario = 'usuario_nuevo';
        $mockCobro->dominio = 'dominio_nuevo.com';
        $mockCobro->precio = 150.75;
        $mockCobro->cantidad_pagada = 1;
        $mockCobro->desde = Carbon::create(2025, 6, 10);
        $mockCobro->hasta = Carbon::create(2025, 7, 10);
        
        // Actualizar pabusqueda con los nuevos valores
        $mockCobro->updatePaBusquedaPublic();
        
        // Verificar que pabusqueda refleja los cambios
        $newExpected = "cobro 54321 12345 usuario_nuevo 10-jun-2025 10-jul-2025 Servicio inicial dominio_nuevo.com 150.75 1";
        $this->assertEquals($newExpected, $mockCobro->pabusqueda);
    }
}

/**
 * Clase auxiliar para pruebas que extiende de Cobro y expone métodos protegidos
 */
class CobroForTest extends Cobro
{
    public $numero;
    public $contrato;
    public $usuario;
    public $descripcion;
    public $dominio;
    public $precio;
    public $cantidad_pagada;
    public $desde;
    public $hasta;
    public $pabusqueda;
    
    public function __construct()
    {
        // Constructor vacío para evitar problemas de conexión
    }
    
    public function updatePaBusquedaPublic()
    {
        // Formatear fechas al estilo "07-abr-2025"
        $desdeFormateado = $this->desde ? $this->formatearFecha($this->desde) : '';
        $hastaFormateado = $this->hasta ? $this->formatearFecha($this->hasta) : '';
        
        $this->pabusqueda = "cobro {$this->numero} {$this->contrato} {$this->usuario} {$desdeFormateado} {$hastaFormateado} {$this->descripcion} {$this->dominio} {$this->precio} {$this->cantidad_pagada}";
    }
    
    public function formatearFechaPublic($fecha)
    {
        return $this->formatearFecha($fecha);
    }
} 