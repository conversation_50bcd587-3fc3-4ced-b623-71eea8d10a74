<?php

namespace Tests\Unit\Models;

use App\Models\Contrato;
use PHPUnit\Framework\TestCase;

class ContratoTest extends TestCase
{
    public function test_pabusqueda_is_updated_when_creating_contrato_object()
    {
        // Crear un objeto Contrato sin guardarlo en la base de datos
        $contrato = new Contrato([
            'numero' => 12345,
            'usuario' => 'usuario_test',
            'servicio' => 'servicio_test',
            'dominio' => 'dominio_test.com',
        ]);

        // Asignar valores directamente para la prueba
        $contrato->numero = 12345;

        // Llamar manualmente al método updatePaBusqueda
        $reflectionClass = new \ReflectionClass(Contrato::class);
        $method = $reflectionClass->getMethod('updatePaBusqueda');
        $method->setAccessible(true);
        $method->invoke($contrato);

        // Verificar que el campo pabusqueda contiene la cadena esperada
        $expected = "contrato 12345 usuario_test servicio_test dominio_test.com";
        $this->assertEquals($expected, $contrato->pabusqueda);
    }

    public function test_pabusqueda_is_updated_when_updating_contrato_object()
    {
        // Crear un objeto Contrato sin guardarlo en la base de datos
        $contrato = new Contrato([
            'numero' => 12345,
            'usuario' => 'usuario_inicial',
            'servicio' => 'servicio_inicial',
            'dominio' => 'dominio_inicial.com',
        ]);
        
        // Asignar valores directamente para la prueba
        $contrato->numero = 12345;

        // Llamar manualmente al método updatePaBusqueda
        $reflectionClass = new \ReflectionClass(Contrato::class);
        $method = $reflectionClass->getMethod('updatePaBusqueda');
        $method->setAccessible(true);
        $method->invoke($contrato);

        // Verificar que el campo pabusqueda contiene la cadena esperada
        $expected = "contrato 12345 usuario_inicial servicio_inicial dominio_inicial.com";
        $this->assertEquals($expected, $contrato->pabusqueda);

        // Actualizar el contrato con nuevos datos
        $contrato->usuario = 'usuario_nuevo';
        $contrato->dominio = 'dominio_nuevo.com';

        // Llamar manualmente al método updatePaBusqueda de nuevo
        $method->invoke($contrato);

        // Verificar que el campo pabusqueda se actualizó con los nuevos datos
        $newExpected = "contrato 12345 usuario_nuevo servicio_inicial dominio_nuevo.com";
        $this->assertEquals($newExpected, $contrato->pabusqueda);
    }
} 