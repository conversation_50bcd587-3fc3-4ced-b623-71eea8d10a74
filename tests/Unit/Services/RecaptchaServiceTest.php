<?php

namespace Tests\Unit\Services;

use App\Services\RecaptchaService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class RecaptchaServiceTest extends TestCase
{
    #[Test]
    public function it_returns_true_in_local_environment_with_empty_secret_key()
    {
        // Configurar el entorno como local
        app()->detectEnvironment(function() {
            return 'local';
        });

        // Configurar una clave secreta vacía
        Config::set('recaptcha.secret_key', '');

        $service = new RecaptchaService();
        $result = $service->validate('test-response');

        $this->assertTrue($result);
    }

    #[Test]
    public function it_validates_recaptcha_response_successfully()
    {
        // Configurar el entorno como producción
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Configurar una clave secreta
        Config::set('recaptcha.secret_key', 'test-secret-key');
        Config::set('recaptcha.validation_url', 'https://www.google.com/recaptcha/api/siteverify');

        // Simular la respuesta HTTP
        Http::fake([
            'www.google.com/recaptcha/api/siteverify' => Http::response([
                'success' => true,
                'score' => 0.9,
            ], 200)
        ]);

        $service = new RecaptchaService();
        $result = $service->validate('test-response');

        $this->assertTrue($result);
    }

    #[Test]
    public function it_returns_false_when_recaptcha_validation_fails()
    {
        // Configurar el entorno como producción
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Configurar una clave secreta
        Config::set('recaptcha.secret_key', 'test-secret-key');
        Config::set('recaptcha.validation_url', 'https://www.google.com/recaptcha/api/siteverify');

        // Simular la respuesta HTTP
        Http::fake([
            'www.google.com/recaptcha/api/siteverify' => Http::response([
                'success' => false,
                'error-codes' => ['invalid-input-response']
            ], 200)
        ]);

        $service = new RecaptchaService();
        $result = $service->validate('invalid-response');

        $this->assertFalse($result);
    }
}
