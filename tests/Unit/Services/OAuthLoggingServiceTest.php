<?php

namespace Tests\Unit\Services;

use App\Services\OAuthLoggingService;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Contracts\User as SocialiteUser;
use Mockery;
use Tests\TestCase;

class OAuthLoggingServiceTest extends TestCase
{
    protected $oauthLogger;
    protected $socialiteUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oauthLogger = new OAuthLoggingService();

        // Mock Socialite user
        $this->socialiteUser = Mockery::mock(SocialiteUser::class);
        $this->socialiteUser->shouldReceive('getId')->andReturn('google_123');
        $this->socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $this->socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $this->socialiteUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');
        $this->socialiteUser->token = 'access_token';
        $this->socialiteUser->refreshToken = 'refresh_token';
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_logs_successful_auth_attempt()
    {
        // Arrange
        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', \Mockery::on(function ($context) {
                return $context['provider'] === 'google' &&
                       isset($context['socialite_user']) &&
                       $context['socialite_user']['id'] === 'google_123' &&
                       $context['socialite_user']['email'] === '<EMAIL>' &&
                       isset($context['timestamp']) &&
                       isset($context['ip_address']);
            }));

        // Act
        $this->oauthLogger->logAuthAttempt('google', $this->socialiteUser);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_failed_auth_attempt_with_exception()
    {
        // Arrange
        $exception = new Exception('Authentication failed', 500);

        Log::shouldReceive('error')
            ->once()
            ->with('OAuth google authentication failed', \Mockery::on(function ($context) {
                return $context['provider'] === 'google' &&
                       isset($context['error']) &&
                       $context['error']['message'] === 'Authentication failed' &&
                       $context['error']['code'] === 500 &&
                       isset($context['error']['trace']);
            }));

        // Act
        $this->oauthLogger->logAuthAttempt('google', $this->socialiteUser, $exception);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_auth_attempt_without_socialite_user()
    {
        // Arrange
        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', \Mockery::on(function ($context) {
                return $context['provider'] === 'google' &&
                       !isset($context['socialite_user']) &&
                       isset($context['timestamp']);
            }));

        // Act
        $this->oauthLogger->logAuthAttempt('google');

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_categorizes_socialite_errors_correctly()
    {
        // Test ACCESS_DENIED
        $accessDeniedError = new Exception('access_denied: User denied access');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: ACCESS_DENIED', \Mockery::on(function ($context) {
                return $context['error_type'] === 'ACCESS_DENIED';
            }));

        $this->oauthLogger->logSocialiteError('google', $accessDeniedError);

        // Test INVALID_CLIENT_CREDENTIALS
        $invalidClientError = new Exception('invalid_client: Client authentication failed');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: INVALID_CLIENT_CREDENTIALS', \Mockery::on(function ($context) {
                return $context['error_type'] === 'INVALID_CLIENT_CREDENTIALS';
            }));

        $this->oauthLogger->logSocialiteError('google', $invalidClientError);

        // Test NETWORK_ERROR
        $networkError = new Exception('Network timeout occurred');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: NETWORK_ERROR', \Mockery::on(function ($context) {
                return $context['error_type'] === 'NETWORK_ERROR';
            }));

        $this->oauthLogger->logSocialiteError('google', $networkError);

        // Test CURL_ERROR
        $curlError = new Exception('cURL error 28: Operation timed out');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: CURL_ERROR', \Mockery::on(function ($context) {
                return $context['error_type'] === 'CURL_ERROR';
            }));

        $this->oauthLogger->logSocialiteError('google', $curlError);

        // Test UNKNOWN_ERROR
        $unknownError = new Exception('Some random error');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: UNKNOWN_ERROR', \Mockery::on(function ($context) {
                return $context['error_type'] === 'UNKNOWN_ERROR';
            }));

        $this->oauthLogger->logSocialiteError('google', $unknownError);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_successful_authentication()
    {
        // Arrange
        $user = (object) [
            'id' => 1,
            'email' => '<EMAIL>'
        ];

        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication successful', \Mockery::on(function ($context) {
                return $context['provider'] === 'google' &&
                       $context['user_id'] === 1 &&
                       $context['user_email'] === '<EMAIL>' &&
                       $context['is_new_user'] === true &&
                       isset($context['timestamp']);
            }));

        // Act
        $this->oauthLogger->logAuthSuccess('google', $user, true);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_avatar_download_errors()
    {
        // Arrange
        $avatarException = new Exception('Failed to download image');

        Log::shouldReceive('warning')
            ->once()
            ->with('OAuth google avatar download failed', \Mockery::on(function ($context) {
                return $context['provider'] === 'google' &&
                       $context['avatar_url'] === 'https://example.com/avatar.jpg' &&
                       $context['error_message'] === 'Failed to download image' &&
                       isset($context['timestamp']);
            }));

        // Act
        $this->oauthLogger->logAvatarError('google', 'https://example.com/avatar.jpg', $avatarException);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_includes_request_context_in_logs()
    {
        // Arrange
        $request = Request::create('/auth/google/callback', 'GET', ['code' => 'test_code']);
        $request->headers->set('User-Agent', 'Test Browser');
        $request->server->set('REMOTE_ADDR', '***********');

        $this->app->instance('request', $request);

        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', \Mockery::on(function ($context) {
                return isset($context['ip_address']) &&
                       isset($context['user_agent']) &&
                       $context['user_agent'] === 'Test Browser';
            }));

        // Act
        $this->oauthLogger->logAuthAttempt('google', $this->socialiteUser);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_socialite_error_with_request_params()
    {
        // Arrange
        $request = Request::create('/auth/google/callback', 'GET', [
            'error' => 'access_denied',
            'error_description' => 'User denied access'
        ]);

        $this->app->instance('request', $request);

        $exception = new Exception('OAuth error');

        Log::shouldReceive('error')
            ->once()
            ->with('Socialite google error: UNKNOWN_ERROR', \Mockery::on(function ($context) {
                return isset($context['request_params']) &&
                       $context['request_params']['error'] === 'access_denied' &&
                       $context['request_params']['error_description'] === 'User denied access' &&
                       isset($context['request_url']);
            }));

        // Act
        $this->oauthLogger->logSocialiteError('google', $exception);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }

    /** @test */
    public function it_handles_socialite_user_with_missing_properties()
    {
        // Arrange
        $incompleteSocialiteUser = Mockery::mock(SocialiteUser::class);
        $incompleteSocialiteUser->shouldReceive('getId')->andReturn(null);
        $incompleteSocialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $incompleteSocialiteUser->shouldReceive('getName')->andReturn(null);
        $incompleteSocialiteUser->shouldReceive('getAvatar')->andReturn(null);
        $incompleteSocialiteUser->token = null;
        $incompleteSocialiteUser->refreshToken = null;

        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', \Mockery::on(function ($context) {
                return $context['socialite_user']['id'] === null &&
                       $context['socialite_user']['email'] === '<EMAIL>' &&
                       $context['socialite_user']['name'] === null &&
                       $context['socialite_user']['has_avatar'] === false &&
                       $context['socialite_user']['has_token'] === false &&
                       $context['socialite_user']['has_refresh_token'] === false;
            }));

        // Act
        $this->oauthLogger->logAuthAttempt('google', $incompleteSocialiteUser);

        // Assert - El test pasa si no hay excepciones
        $this->assertTrue(true);
    }
}
