<?php

namespace Tests\Unit\Exceptions;

use App\Exceptions\OAuthException;
use Exception;
use Tests\TestCase;

class OAuthExceptionTest extends TestCase
{
    /** @test */
    public function it_creates_oauth_exception_with_all_parameters()
    {
        // Arrange & Act
        $exception = new OAuthException(
            'google',
            'ACCESS_DENIED',
            'Technical error message',
            'User friendly message',
            500
        );

        // Assert
        $this->assertEquals('google', $exception->getProvider());
        $this->assertEquals('ACCESS_DENIED', $exception->getErrorType());
        $this->assertEquals('Technical error message', $exception->getMessage());
        $this->assertEquals('User friendly message', $exception->getUserFriendlyMessage());
        $this->assertEquals(500, $exception->getCode());
    }

    /** @test */
    public function it_generates_default_user_messages_for_error_types()
    {
        $testCases = [
            'ACCESS_DENIED' => 'Has cancelado la autorización',
            'INVALID_CLIENT_CREDENTIALS' => 'Error de configuración del servicio',
            'NETWORK_ERROR' => 'Error de conexión',
            'INVALID_REQUEST' => 'Error en la solicitud de autenticación',
            'INVALID_GRANT' => 'La autorización ha expirado',
            'AVATAR_DOWNLOAD_FAILED' => 'No se pudo descargar tu foto de perfil',
            'USER_CREATION_FAILED' => 'Error al crear tu cuenta',
            'DATABASE_ERROR' => 'Error interno del sistema',
            'UNKNOWN_ERROR' => 'Ha ocurrido un error inesperado'
        ];

        foreach ($testCases as $errorType => $expectedMessage) {
            $exception = new OAuthException(
                'google',
                $errorType,
                'Technical message'
            );

            $this->assertStringContainsString($expectedMessage, $exception->getUserFriendlyMessage());
        }
    }

    /** @test */
    public function it_creates_socialite_error_exception()
    {
        // Arrange
        $originalException = new Exception('access_denied: User cancelled authorization');

        // Act
        $oauthException = OAuthException::socialiteError('google', $originalException);

        // Assert
        $this->assertEquals('google', $oauthException->getProvider());
        $this->assertEquals('ACCESS_DENIED', $oauthException->getErrorType());
        $this->assertStringContainsString('Socialite google error', $oauthException->getMessage());
        $this->assertStringContainsString('access_denied', $oauthException->getMessage());
        $this->assertEquals($originalException, $oauthException->getPrevious());
    }

    /** @test */
    public function it_detects_invalid_client_error_in_socialite_error()
    {
        // Arrange
        $originalException = new Exception('invalid_client: Client authentication failed');

        // Act
        $oauthException = OAuthException::socialiteError('google', $originalException);

        // Assert
        $this->assertEquals('INVALID_CLIENT_CREDENTIALS', $oauthException->getErrorType());
        $this->assertStringContainsString('Error de configuración del servicio', $oauthException->getUserFriendlyMessage());
    }

    /** @test */
    public function it_detects_network_error_in_socialite_error()
    {
        // Arrange
        $originalException = new Exception('cURL error 28: Operation timed out after 30000 milliseconds');

        // Act
        $oauthException = OAuthException::socialiteError('google', $originalException);

        // Assert
        $this->assertEquals('NETWORK_ERROR', $oauthException->getErrorType());
        $this->assertStringContainsString('Error de conexión', $oauthException->getUserFriendlyMessage());
    }

    /** @test */
    public function it_creates_avatar_download_failed_exception()
    {
        // Arrange
        $originalException = new Exception('HTTP 404: Image not found');

        // Act
        $oauthException = OAuthException::avatarDownloadFailed('google', $originalException);

        // Assert
        $this->assertEquals('google', $oauthException->getProvider());
        $this->assertEquals('AVATAR_DOWNLOAD_FAILED', $oauthException->getErrorType());
        $this->assertStringContainsString('Avatar download failed for google', $oauthException->getMessage());
        $this->assertStringContainsString('No se pudo descargar tu foto de perfil', $oauthException->getUserFriendlyMessage());
        $this->assertEquals($originalException, $oauthException->getPrevious());
    }

    /** @test */
    public function it_creates_database_error_exception()
    {
        // Arrange
        $originalException = new Exception('Connection to database lost');

        // Act
        $oauthException = OAuthException::databaseError('google', $originalException);

        // Assert
        $this->assertEquals('google', $oauthException->getProvider());
        $this->assertEquals('DATABASE_ERROR', $oauthException->getErrorType());
        $this->assertStringContainsString('Database error during google authentication', $oauthException->getMessage());
        $this->assertStringContainsString('Error interno del sistema', $oauthException->getUserFriendlyMessage());
        $this->assertEquals($originalException, $oauthException->getPrevious());
    }

    /** @test */
    public function it_preserves_original_exception_code()
    {
        // Arrange
        $originalException = new Exception('Original error', 404);

        // Act
        $oauthException = OAuthException::socialiteError('google', $originalException);

        // Assert
        $this->assertEquals(404, $oauthException->getCode());
    }

    /** @test */
    public function it_handles_unknown_error_types_in_socialite_error()
    {
        // Arrange
        $originalException = new Exception('Some random unexpected error');

        // Act
        $oauthException = OAuthException::socialiteError('google', $originalException);

        // Assert
        $this->assertEquals('SOCIALITE_ERROR', $oauthException->getErrorType());
        $this->assertStringContainsString('Ha ocurrido un error inesperado', $oauthException->getUserFriendlyMessage());
    }

    /** @test */
    public function it_works_with_different_providers()
    {
        $providers = ['google', 'facebook', 'github', 'twitter'];

        foreach ($providers as $provider) {
            $exception = new OAuthException(
                $provider,
                'ACCESS_DENIED',
                'Test message'
            );

            $this->assertEquals($provider, $exception->getProvider());
        }
    }

    /** @test */
    public function it_handles_case_insensitive_error_detection()
    {
        // Test uppercase
        $upperException = new Exception('ACCESS_DENIED: User cancelled');
        $oauthException1 = OAuthException::socialiteError('google', $upperException);
        $this->assertEquals('ACCESS_DENIED', $oauthException1->getErrorType());

        // Test mixed case
        $mixedException = new Exception('Invalid_Client: Authentication failed');
        $oauthException2 = OAuthException::socialiteError('google', $mixedException);
        $this->assertEquals('INVALID_CLIENT_CREDENTIALS', $oauthException2->getErrorType());

        // Test with network keyword
        $networkException = new Exception('Network connection failed');
        $oauthException3 = OAuthException::socialiteError('google', $networkException);
        $this->assertEquals('NETWORK_ERROR', $oauthException3->getErrorType());
    }
}
