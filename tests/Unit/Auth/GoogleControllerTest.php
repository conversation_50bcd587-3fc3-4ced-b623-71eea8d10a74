<?php

namespace Tests\Unit\Auth;

use App\Http\Controllers\Auth\GoogleController;
use App\Models\User;
use App\Services\AvatarService;
use App\Services\OAuthLoggingService;
use App\Exceptions\OAuthException;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Laravel\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\InvalidStateException;
use Mockery;
use Tests\TestCase;

class GoogleControllerTest extends TestCase
{
    use WithoutMiddleware;

    protected $avatarService;
    protected $oauthLogger;
    protected $controller;
    protected $socialiteUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock services
        $this->avatarService = Mockery::mock(AvatarService::class);
        $this->oauthLogger = Mockery::mock(OAuthLoggingService::class);

        // Create controller instance
        $this->controller = new GoogleController($this->avatarService, $this->oauthLogger);

        // Mock Socialite user
        $this->socialiteUser = Mockery::mock();
        $this->socialiteUser->shouldReceive('getId')->andReturn('google_user_123');
        $this->socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $this->socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $this->socialiteUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');
        $this->socialiteUser->shouldReceive('token')->andReturn('access_token_123');
        $this->socialiteUser->shouldReceive('refreshToken')->andReturn('refresh_token_123');

        // Mock properties for dynamic access
        $this->socialiteUser->id = 'google_user_123';
        $this->socialiteUser->email = '<EMAIL>';
        $this->socialiteUser->name = 'Test User';
        $this->socialiteUser->avatar = 'https://example.com/avatar.jpg';
        $this->socialiteUser->token = 'access_token_123';
        $this->socialiteUser->refreshToken = 'refresh_token_123';

        Event::fake();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_redirects_to_google_successfully()
    {
        // Arrange
        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('redirect')
            ->andReturn(redirect('https://accounts.google.com/oauth/authorize'));

        // Act
        $response = $this->controller->redirectToGoogle();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_google_redirect_error()
    {
        // Arrange
        $exception = new Exception('Connection failed');

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google');

        $this->oauthLogger->shouldReceive('logSocialiteError')
            ->once()
            ->with('google', $exception);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andThrow($exception);

        // Act
        $response = $this->controller->redirectToGoogle();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
    }

    /** @test */
    public function it_handles_access_denied_error()
    {
        // Arrange
        $request = Request::create('/auth/google/callback', 'GET', [
            'error' => 'access_denied',
            'error_description' => 'User denied access'
        ]);
        $this->app->instance('request', $request);

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', null, Mockery::type(OAuthException::class));

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
        $this->assertStringContainsString('cancelado la autorización', session('error'));
    }

    /** @test */
    public function it_handles_incomplete_google_user_data()
    {
        // Arrange
        $incompleteSocialiteUser = Mockery::mock();
        $incompleteSocialiteUser->shouldReceive('getId')->andReturn(null);
        $incompleteSocialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', $incompleteSocialiteUser);

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', null, Mockery::type(OAuthException::class));

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($incompleteSocialiteUser);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
    }

    /** @test */
    public function it_authenticates_existing_user_with_google_id()
    {
        // Arrange
        $user = new User([
            'id' => 1,
            'google_id' => 'google_user_123',
            'email' => '<EMAIL>',
            'activo' => 'Si',
            'avatar' => null
        ]);

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', $this->socialiteUser);

        $this->oauthLogger->shouldReceive('logAuthSuccess')
            ->once()
            ->with('google', $user, false);

        $this->avatarService->shouldReceive('downloadAndSaveAvatar')
            ->once()
            ->with('https://example.com/avatar.jpg')
            ->andReturn('/storage/avatars/avatar_123.jpg');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($this->socialiteUser);

        // Mock User model static methods
        User::shouldReceive('where')
            ->with('google_id', 'google_user_123')
            ->andReturnSelf();
        User::shouldReceive('first')
            ->andReturn($user);

        // Mock user update
        $user->shouldReceive('update')
            ->once()
            ->with(Mockery::subset([
                'google_token' => 'access_token_123',
                'google_refresh_token' => 'refresh_token_123'
            ]));

        Auth::shouldReceive('login')
            ->once()
            ->with($user);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('dashboard'), $response->getTargetUrl());
    }

    /** @test */
    public function it_handles_inactive_user()
    {
        // Arrange
        $user = new User([
            'id' => 1,
            'google_id' => 'google_user_123',
            'email' => '<EMAIL>',
            'activo' => 'No'
        ]);

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', $this->socialiteUser);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($this->socialiteUser);

        User::shouldReceive('where')
            ->with('google_id', 'google_user_123')
            ->andReturnSelf();
        User::shouldReceive('first')
            ->andReturn($user);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
        $this->assertStringContains('no está activa', session('error'));
    }

    /** @test */
    public function it_creates_new_user_when_none_exists()
    {
        // Arrange
        $newUser = new User([
            'id' => 1,
            'email' => '<EMAIL>',
            'google_id' => 'google_user_123'
        ]);

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', $this->socialiteUser);

        $this->oauthLogger->shouldReceive('logAuthSuccess')
            ->once()
            ->with('google', $newUser, true);

        $this->oauthLogger->shouldReceive('logAuthSuccess')
            ->once()
            ->with('google', $newUser, false);

        $this->avatarService->shouldReceive('downloadAndSaveAvatar')
            ->once()
            ->with('https://example.com/avatar.jpg')
            ->andReturn('/storage/avatars/avatar_123.jpg');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($this->socialiteUser);

        // Mock User queries returning null (no existing user)
        User::shouldReceive('where')->andReturnSelf();
        User::shouldReceive('first')->andReturn(null);
        User::shouldReceive('whereHas')->andReturnSelf();
        User::shouldReceive('exists')->andReturn(false);

        // Mock user creation
        User::shouldReceive('create')
            ->once()
            ->andReturn($newUser);

        Auth::shouldReceive('login')
            ->once()
            ->with($newUser);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('dashboard'), $response->getTargetUrl());
        Event::assertDispatched(Registered::class);
    }

    /** @test */
    public function it_handles_avatar_download_failure_gracefully()
    {
        // Arrange
        $user = new User([
            'id' => 1,
            'google_id' => 'google_user_123',
            'email' => '<EMAIL>',
            'activo' => 'Si',
            'avatar' => null
        ]);

        $avatarException = new Exception('Failed to download avatar');

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', $this->socialiteUser);

        $this->oauthLogger->shouldReceive('logAvatarError')
            ->once()
            ->with('google', 'https://example.com/avatar.jpg', $avatarException);

        $this->oauthLogger->shouldReceive('logAuthSuccess')
            ->once()
            ->with('google', $user, false);

        $this->avatarService->shouldReceive('downloadAndSaveAvatar')
            ->once()
            ->with('https://example.com/avatar.jpg')
            ->andThrow($avatarException);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($this->socialiteUser);

        User::shouldReceive('where')
            ->with('google_id', 'google_user_123')
            ->andReturnSelf();
        User::shouldReceive('first')
            ->andReturn($user);

        $user->shouldReceive('update')->once();

        Auth::shouldReceive('login')
            ->once()
            ->with($user);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('dashboard'), $response->getTargetUrl());
    }

    /** @test */
    public function it_handles_invalid_state_exception()
    {
        // Arrange
        $exception = new InvalidStateException('Invalid state parameter');

        $this->oauthLogger->shouldReceive('logSocialiteError')
            ->once()
            ->with('google', $exception);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow($exception);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
        $this->assertStringContainsString('Error de seguridad', session('error'));
    }

    /** @test */
    public function it_shows_debug_details_when_app_debug_is_true()
    {
        // Arrange
        config(['app.debug' => true]);

        $exception = new Exception('Detailed error message');

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', null, Mockery::type(OAuthException::class));

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow($exception);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
        $this->assertNotNull(session('error_details'));
        $this->assertStringContainsString('Detailed error message', session('error_details'));
    }

    /** @test */
    public function it_hides_debug_details_when_app_debug_is_false()
    {
        // Arrange
        config(['app.debug' => false]);

        $exception = new Exception('Detailed error message');

        $this->oauthLogger->shouldReceive('logAuthAttempt')
            ->once()
            ->with('google', null, Mockery::type(OAuthException::class));

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow($exception);

        // Act
        $response = $this->controller->handleGoogleCallback();

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('login'), $response->getTargetUrl());
        $this->assertNull(session('error_details'));
    }
}
