<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\AvatarService;
use App\Services\OAuthLoggingService;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class GoogleOAuthTest extends TestCase
{
    use WithoutMiddleware;

    protected function setUp(): void
    {
        parent::setUp();

        // No usar RefreshDatabase para evitar borrar datos de clientes
        // En su lugar, usaremos mocks y datos de prueba específicos
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function google_redirect_route_is_accessible()
    {
        // Arrange
        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('redirect')
            ->andReturn(redirect('https://accounts.google.com/oauth/authorize'));

        // Act
        $response = $this->get(route('auth.google'));

        // Assert
        $response->assertStatus(302);
        $this->assertStringContains('google', $response->getTargetUrl());
    }

    /** @test */
    public function google_callback_handles_user_cancellation()
    {
        // Act
        $response = $this->get('/auth/google/callback?error=access_denied&error_description=User+denied+access');

        // Assert
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error');
        $this->assertStringContainsString('cancelado', session('error'));
    }

    /** @test */
    public function google_callback_handles_network_errors()
    {
        // Arrange
        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow(new \Exception('Network timeout'));

        // Act
        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error');
    }

    /** @test */
    public function google_callback_logs_authentication_attempts()
    {
        // Arrange
        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', Mockery::type('array'));

        Log::shouldReceive('error')
            ->once()
            ->with('OAuth google authentication failed', Mockery::type('array'));

        $socialiteUser = Mockery::mock();
        $socialiteUser->shouldReceive('getId')->andReturn('google_123');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getAvatar')->andReturn('https://example.com/avatar.jpg');
        $socialiteUser->id = 'google_123';
        $socialiteUser->email = '<EMAIL>';
        $socialiteUser->name = 'Test User';
        $socialiteUser->avatar = 'https://example.com/avatar.jpg';
        $socialiteUser->token = 'access_token';
        $socialiteUser->refreshToken = 'refresh_token';

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($socialiteUser);

        // Mock que no existe usuario para forzar un error controlado
        $this->mock(User::class, function ($mock) {
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
            $mock->shouldReceive('whereHas')->andReturnSelf();
            $mock->shouldReceive('exists')->andReturn(false);
            $mock->shouldReceive('create')->andThrow(new \Exception('Database error'));
        });

        // Act
        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function google_callback_handles_avatar_service_errors()
    {
        // Arrange
        $socialiteUser = Mockery::mock();
        $socialiteUser->shouldReceive('getId')->andReturn('google_123');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getAvatar')->andReturn('https://invalid-url.com/avatar.jpg');
        $socialiteUser->id = 'google_123';
        $socialiteUser->email = '<EMAIL>';
        $socialiteUser->name = 'Test User';
        $socialiteUser->avatar = 'https://invalid-url.com/avatar.jpg';
        $socialiteUser->token = 'access_token';
        $socialiteUser->refreshToken = 'refresh_token';

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($socialiteUser);

        // Mock AvatarService para que falle
        $this->mock(AvatarService::class, function ($mock) {
            $mock->shouldReceive('downloadAndSaveAvatar')
                ->andThrow(new \Exception('Failed to download avatar'));
        });

        // Mock logging service
        $this->mock(OAuthLoggingService::class, function ($mock) {
            $mock->shouldReceive('logAuthAttempt')->twice();
            $mock->shouldReceive('logAvatarError')->once();
        });

        // Mock User model para simular usuario existente
        $existingUser = Mockery::mock(User::class);
        $existingUser->shouldReceive('update')->once();
        $existingUser->id = 1;
        $existingUser->email = '<EMAIL>';
        $existingUser->avatar = null;

        $this->mock(User::class, function ($mock) use ($existingUser) {
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn($existingUser);
        });

        Auth::shouldReceive('login')->once();

        // Act
        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('dashboard'));
    }

    /** @test */
    public function google_callback_validates_required_user_data()
    {
        // Arrange - Socialite user sin email
        $incompleteSocialiteUser = Mockery::mock();
        $incompleteSocialiteUser->shouldReceive('getId')->andReturn('google_123');
        $incompleteSocialiteUser->shouldReceive('getEmail')->andReturn(null); // Email faltante
        $incompleteSocialiteUser->shouldReceive('getName')->andReturn('Test User');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($incompleteSocialiteUser);

        // Act
        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error');
        $this->assertStringContainsString('Error al obtener información', session('error'));
    }

    /** @test */
    public function google_callback_handles_database_connection_errors()
    {
        // Arrange
        $socialiteUser = Mockery::mock();
        $socialiteUser->shouldReceive('getId')->andReturn('google_123');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getAvatar')->andReturn(null);
        $socialiteUser->id = 'google_123';
        $socialiteUser->email = '<EMAIL>';
        $socialiteUser->name = 'Test User';
        $socialiteUser->avatar = null;
        $socialiteUser->token = 'access_token';
        $socialiteUser->refreshToken = 'refresh_token';

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andReturn($socialiteUser);

        // Mock User model para simular error de base de datos
        $this->mock(User::class, function ($mock) {
            $mock->shouldReceive('where')->andReturnSelf();
            $mock->shouldReceive('first')->andReturn(null);
            $mock->shouldReceive('whereHas')->andReturnSelf();
            $mock->shouldReceive('exists')->andReturn(false);
            $mock->shouldReceive('create')->andThrow(new \Illuminate\Database\QueryException(
                'mysql',
                'SELECT * FROM users',
                [],
                new \Exception('Connection lost')
            ));
        });

        // Act
        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error');
        $this->assertStringContainsString('Error al crear tu cuenta', session('error'));
    }

    /** @test */
    public function google_callback_shows_appropriate_error_messages_based_on_debug_mode()
    {
        // Test con debug activado
        config(['app.debug' => true]);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow(new \Exception('Detailed technical error'));

        $response = $this->get('/auth/google/callback?code=test_code&state=test_state');

        $response->assertRedirect(route('login'));
        $response->assertSessionHas('error_details');

        // Test con debug desactivado
        config(['app.debug' => false]);

        $response2 = $this->get('/auth/google/callback?code=test_code&state=test_state');

        $response2->assertRedirect(route('login'));
        $response2->assertSessionMissing('error_details');
    }

    /** @test */
    public function google_oauth_respects_rate_limiting()
    {
        // Arrange - Simular múltiples intentos rápidos
        $responses = [];

        for ($i = 0; $i < 5; $i++) {
            Socialite::shouldReceive('driver')
                ->with('google')
                ->andReturnSelf();

            Socialite::shouldReceive('user')
                ->andThrow(new \Exception('Rate limited'));

            $responses[] = $this->get('/auth/google/callback?code=test_code&state=test_state');
        }

        // Assert - Todos deberían redirigir a login con error
        foreach ($responses as $response) {
            $response->assertRedirect(route('login'));
            $response->assertSessionHas('error');
        }
    }

    /** @test */
    public function google_oauth_logs_security_relevant_information()
    {
        // Arrange
        Log::shouldReceive('info')
            ->once()
            ->with('OAuth google authentication attempt', Mockery::that(function ($context) {
                return isset($context['ip_address']) &&
                       isset($context['user_agent']) &&
                       isset($context['timestamp']);
            }));

        Log::shouldReceive('error')
            ->once()
            ->with('OAuth google authentication failed', Mockery::type('array'));

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();

        Socialite::shouldReceive('user')
            ->andThrow(new \Exception('Test error'));

        // Act
        $response = $this->withHeaders([
            'User-Agent' => 'Test Browser',
            'X-Forwarded-For' => '***********'
        ])->get('/auth/google/callback?code=test_code&state=test_state');

        // Assert
        $response->assertRedirect(route('login'));
    }
}
