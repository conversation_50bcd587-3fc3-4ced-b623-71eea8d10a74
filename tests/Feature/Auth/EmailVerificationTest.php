<?php

use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('email verification screen can be rendered', function () {
    $user = User::factory()->unverified()->create();

    $response = $this->actingAs($user)->get('/verify-email');

    $response->assertStatus(200);
});

test('email can be verified', function () {
    $user = User::factory()->unverified()->create();

    Event::fake();

    $verificationUrl = URL::temporarySignedRoute(
        'verification.verify',
        now()->addMinutes(60),
        ['id' => $user->id, 'hash' => sha1($user->logmail)]
    );

    $response = $this->actingAs($user)->get($verificationUrl);

    Event::assertDispatched(Verified::class);

    expect($user->fresh()->hasVerifiedEmail())->toBeTrue();
    $response->assertRedirect(route('dashboard', absolute: false).'?verified=1');
});

test('email is not verified with invalid hash', function () {
    $user = User::factory()->unverified()->create();

    $verificationUrl = URL::temporarySignedRoute(
        'verification.verify',
        now()->addMinutes(60),
        ['id' => $user->id, 'hash' => sha1('wrong-email')]
    );

    $this->actingAs($user)->get($verificationUrl);

    expect($user->fresh()->hasVerifiedEmail())->toBeFalse();
});

test('guest can verify email and gets automatic login', function () {
    $user = User::factory()->unverified()->create();

    Event::fake();

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
    );

    $response = $this->get($verificationUrl);

    Event::assertDispatched(Verified::class);

    expect($user->fresh()->hasVerifiedEmail())->toBeTrue();
    $response->assertRedirect(route('dashboard', absolute: false).'?verified=1');
    $response->assertSessionHas('status');

    // Verificar que el usuario está autenticado
    $this->assertAuthenticated();
    expect($this->app['auth']->user()->id)->toBe($user->id);
});

test('guest verification fails with invalid hash', function () {
    $user = User::factory()->unverified()->create();

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1('wrong-email')]
    );

    $response = $this->get($verificationUrl);

    expect($user->fresh()->hasVerifiedEmail())->toBeFalse();
    $response->assertRedirect(route('login'));
    $response->assertSessionHas('error');
});

test('guest verification fails for non-existent user', function () {
    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => 99999, 'hash' => sha1('<EMAIL>')]
    );

    $response = $this->get($verificationUrl);

    $response->assertRedirect(route('login'));
    $response->assertSessionHas('error', 'El usuario no existe.');
});

test('guest verification fails for old users', function () {
    // Crear un usuario con fecha antigua
    $user = User::factory()->unverified()->create([
        'created_at' => now()->subHours(25), // Más de 24 horas
    ]);

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
    );

    $response = $this->get($verificationUrl);

    expect($user->fresh()->hasVerifiedEmail())->toBeFalse();
    $response->assertRedirect(route('login'));
    $response->assertSessionHas('error');
});

test('guest verification does not auto-login when email already verified', function () {
    // Crear un usuario con email ya verificado
    $user = User::factory()->create([
        'email_verified_at' => now(),
    ]);

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
    );

    $response = $this->get($verificationUrl);

    // No debería disparar el evento Verified ya que ya estaba verificado
    Event::fake();
    $this->get($verificationUrl);
    Event::assertNotDispatched(Verified::class);

    expect($user->fresh()->hasVerifiedEmail())->toBeTrue();
    $response->assertRedirect(route('login'));
    $response->assertSessionHas('status', 'Tu correo electrónico ya ha sido verificado. Puedes iniciar sesión.');

    // Verificar que NO se hizo login automático
    $this->assertGuest();
});

test('authenticated user verification redirects to dashboard when email already verified', function () {
    $user = User::factory()->create([
        'email_verified_at' => now(),
    ]);

    // Autenticar al usuario
    $this->actingAs($user);

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
    );

    $response = $this->get($verificationUrl);

    expect($user->fresh()->hasVerifiedEmail())->toBeTrue();
    $response->assertRedirect(route('dashboard', absolute: false).'?verified=1');

    // Verificar que sigue autenticado como el mismo usuario
    $this->assertAuthenticated();
    expect($this->app['auth']->user()->id)->toBe($user->id);
});

test('authenticated user verification redirects to dashboard after first-time verification', function () {
    $user = User::factory()->unverified()->create();

    // Autenticar al usuario
    $this->actingAs($user);

    Event::fake();

    $verificationUrl = route(
        'verification.verify.guest',
        ['id' => $user->id, 'hash' => sha1($user->getEmailForVerification())]
    );

    $response = $this->get($verificationUrl);

    Event::assertDispatched(Verified::class);

    expect($user->fresh()->hasVerifiedEmail())->toBeTrue();
    $response->assertRedirect(route('dashboard', absolute: false).'?verified=1');

    // Verificar que sigue autenticado como el mismo usuario
    $this->assertAuthenticated();
    expect($this->app['auth']->user()->id)->toBe($user->id);
});
