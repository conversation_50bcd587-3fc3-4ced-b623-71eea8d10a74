SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";

START TRANSACTION;

SET time_zone = "+00:00";

CREATE TABLE `ampi_articulos_noticias` (
    `id` int(11) NOT NULL,
    `titulo` varchar(50) NOT NULL DEFAULT '',
    `primer_parrafo` text NOT NULL,
    `contenido` text NOT NULL,
    `tipo` enum(
        'noticia',
        'articulo',
        'pagina'
    ) NOT NULL DEFAULT 'noticia',
    `tema` varchar(30) NOT NULL DEFAULT '',
    `visible_para` enum('socios', 'general') NOT NULL DEFAULT 'general',
    `activada` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `en_principal` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `entidad` varchar(30) NOT NULL DEFAULT '',
    `seccion` int(5) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_comercializacion` (
    `id` int(7) NOT NULL,
    `seccion` int(5) NOT NULL,
    `clave_sistema` int(7) NOT NULL,
    `propuesto` datetime NOT NULL,
    `autorizado` datetime NOT NULL,
    `status` enum('en espera', 'autorizada') NOT NULL DEFAULT 'en espera'
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_encuestas` (
    `id` int(11) NOT NULL,
    `titulo` varchar(250) NOT NULL DEFAULT '',
    `fecha` int(10) NOT NULL DEFAULT 0,
    `principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `articulo` int(11) NOT NULL DEFAULT 0,
    `visible_para` enum('socios', 'general') NOT NULL DEFAULT 'general',
    `activada` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `entidad` varchar(30) NOT NULL DEFAULT '',
    `seccion` int(5) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_encuestas_respuestas` (
    `id` int(11) NOT NULL,
    `texto` varchar(50) NOT NULL DEFAULT '',
    `votos` int(5) NOT NULL DEFAULT 0,
    `idenc` int(11) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_funciones` (
    `id` int(2) NOT NULL,
    `jerarquia` char(1) NOT NULL DEFAULT '',
    `descripcion` varchar(30) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_nucal_categories` (
    `id` int(11) NOT NULL,
    `title` varchar(128) NOT NULL DEFAULT '',
    `description` text NOT NULL,
    `showinblock` tinyint(1) NOT NULL DEFAULT 1,
    `estado` varchar(30) NOT NULL DEFAULT '',
    `seccion` int(5) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_nucal_events` (
    `id` int(11) NOT NULL,
    `title` varchar(50) NOT NULL DEFAULT '',
    `location` varchar(64) NOT NULL DEFAULT '',
    `starttime` time NOT NULL DEFAULT '00:00:00',
    `duration` time NOT NULL DEFAULT '00:00:00',
    `fulldesc` text NOT NULL,
    `isactive` tinyint(1) NOT NULL DEFAULT 1,
    `isrecurring` tinyint(1) NOT NULL DEFAULT 0,
    `categoryid` int(11) NOT NULL DEFAULT 1,
    `isapproved` tinyint(1) NOT NULL DEFAULT 0,
    `onetime_date` date NOT NULL DEFAULT '0000-00-00',
    `recur_weekday` tinyint(4) NOT NULL DEFAULT 0,
    `recur_schedule` enum('weekly', 'monthly', 'yearly') NOT NULL DEFAULT 'weekly',
    `recur_period` tinyint(4) DEFAULT NULL,
    `recur_month` tinyint(4) DEFAULT NULL,
    `solo_socios` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `estado` varchar(30) NOT NULL DEFAULT '',
    `seccion` int(5) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_nucal_options` (
    `allow_user_submitted_events` tinyint(1) NOT NULL DEFAULT 0,
    `user_submitted_events_need_admin_aproval` tinyint(1) NOT NULL DEFAULT 1,
    `calendar_title` varchar(128) NOT NULL DEFAULT 'Calendar of Events',
    `calendar_title_image` varchar(255) NOT NULL DEFAULT '',
    `show_n_events` tinyint(6) UNSIGNED NOT NULL DEFAULT 5,
    `in_n_days` int(11) UNSIGNED NOT NULL DEFAULT 90,
    `show_bydate_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `show_yearly_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `show_yearly_recurring_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `show_monthly_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `show_monthly_recurring_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `show_weekly_in_block` tinyint(1) NOT NULL DEFAULT 1,
    `month_day_color` varchar(6) NOT NULL DEFAULT 'ECECEC',
    `month_today_color` varchar(6) NOT NULL DEFAULT 'FFFFFF',
    `month_hover_color` varchar(6) NOT NULL DEFAULT 'C0C0C0',
    `show_mdy` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_secciones` (
    `id` int(5) NOT NULL,
    `Nombre` varchar(30) NOT NULL DEFAULT '',
    `RutaServer` varchar(50) NOT NULL DEFAULT '',
    `Dominio1` varchar(50) NOT NULL DEFAULT '',
    `Dominio2` varchar(50) NOT NULL DEFAULT '',
    `PrincipalSocios` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `caduca_inmuebles` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `dias_caduca_inm` int(3) NOT NULL DEFAULT 30,
    `envio_msg_caducados` date NOT NULL DEFAULT '0000-00-00'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_socios_externos` (
    `id` int(4) NOT NULL,
    `nombre` varchar(65) NOT NULL,
    `apellidos` varchar(65) NOT NULL,
    `empresa` varchar(65) NOT NULL,
    `telefono` varchar(30) NOT NULL,
    `pagina_web` varchar(65) NOT NULL,
    `email` varchar(65) NOT NULL,
    `funcion_ampi` int(2) NOT NULL DEFAULT 0,
    `status` enum('activo', 'inactivo') NOT NULL DEFAULT 'activo',
    `seccion` int(4) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_tmp_errores_calendario` (
    `id` int(11) NOT NULL,
    `fecha_hora` datetime NOT NULL,
    `variables` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ampi_usuarios` (
    `id` int(7) NOT NULL,
    `seccion` int(5) NOT NULL DEFAULT 0,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `tipo` enum('Usuario', 'Administrador') NOT NULL DEFAULT 'Usuario',
    `funcion` int(2) NOT NULL DEFAULT 4,
    `vigencia` date NOT NULL DEFAULT '0000-00-00',
    `contrato_adhesion` enum(
        'pendiente',
        'aceptado',
        'rechazado'
    ) NOT NULL DEFAULT 'pendiente',
    `fecha_contrato` datetime NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `ampi_usuarios_20240316` (
    `id` int(7) NOT NULL,
    `seccion` int(5) NOT NULL DEFAULT 0,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `tipo` enum('Usuario', 'Administrador') NOT NULL DEFAULT 'Usuario',
    `funcion` int(2) NOT NULL DEFAULT 4,
    `vigencia` date NOT NULL DEFAULT '0000-00-00',
    `contrato_adhesion` enum(
        'pendiente',
        'aceptado',
        'rechazado'
    ) NOT NULL DEFAULT 'pendiente',
    `fecha_contrato` datetime NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `antispam` (
    `ip` varchar(50) NOT NULL,
    `cadena` varchar(10) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `api_google` (
    `dominio` varchar(50) NOT NULL,
    `api` varchar(250) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `api_tokens` (
    `id` bigint(20) NOT NULL,
    `token` varchar(150) NOT NULL,
    `contrato_id` int(9) NOT NULL,
    `name` varchar(35) NOT NULL,
    `description` text DEFAULT NULL,
    `permissions` text DEFAULT NULL,
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `asesores` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `usuario` varchar(15) DEFAULT NULL,
    `password` varchar(100) NOT NULL DEFAULT '',
    `tratamiento` varchar(10) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `automovil` varchar(70) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `radio_tel` varchar(30) NOT NULL DEFAULT '',
    `radio_id` varchar(15) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `domicilio` varchar(150) NOT NULL DEFAULT '',
    `p_contesta_preguntas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_hace_citas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_registra_inmuebles` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_edita_todos_los_inmuebles` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_datos_internos_inmuebles` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_bolsa` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_reportes_visitas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_contratos_renta` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_asesor_en_rol` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_publicar_en_web` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `recibio_pregunta_cita` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `status` enum('activo', 'inactivo') NOT NULL DEFAULT 'activo',
    `sucursal_id` int(7) DEFAULT NULL,
    `updated` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
    `created` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `asesor_cliente` (
    `id` bigint(20) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `asesor_id` int(7) NOT NULL DEFAULT 0,
    `usuario_cliente` varchar(25) NOT NULL DEFAULT '',
    `email_cliente` varchar(60) NOT NULL DEFAULT '',
    `telefono_cliente` varchar(10) NOT NULL DEFAULT ''
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `asesor_cliente_20240316` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `asesor_id` int(7) NOT NULL DEFAULT 0,
    `usuario_cliente` varchar(25) NOT NULL DEFAULT '',
    `email_cliente` varchar(60) NOT NULL DEFAULT '',
    `telefono_cliente` varchar(10) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `avisos_bolsa` (
    `id` int(5) NOT NULL,
    `contrato` int(7) NOT NULL,
    `aviso` text NOT NULL,
    `respuestas` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_general_ci;

CREATE TABLE `avisos_respuestas` (
    `id` int(7) NOT NULL,
    `contrato` int(7) NOT NULL,
    `responde_a` int(7) NOT NULL,
    `respuesta` varchar(200) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `bolsa_compradores` (
    `id` int(15) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cliente` varchar(150) NOT NULL DEFAULT '',
    `tipo_de_inmueble` varchar(50) NOT NULL DEFAULT '',
    `promocion` varchar(15) NOT NULL DEFAULT '',
    `zona_o_colonia` varchar(50) NOT NULL DEFAULT '',
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(30) NOT NULL DEFAULT '',
    `uso_de_suelo` varchar(50) NOT NULL DEFAULT '',
    `precio1` int(9) NOT NULL DEFAULT 0,
    `precio2` int(9) NOT NULL DEFAULT 0,
    `moneda` varchar(15) NOT NULL DEFAULT '',
    `forma_de_pago` varchar(50) NOT NULL DEFAULT '',
    `otra_forma_de_pago` varchar(100) NOT NULL DEFAULT '',
    `observaciones` mediumtext NOT NULL,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `visitas` int(4) NOT NULL DEFAULT 0,
    `cerrada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `bolsa_compradores_r` (
    `id` int(15) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `rid` int(15) NOT NULL DEFAULT 0,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `respuesta` mediumtext NOT NULL,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `bolsa_grupos` (
    `id` int(5) NOT NULL,
    `nombre` varchar(100) NOT NULL,
    `descripcion` text NOT NULL,
    `solo_exclusivas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `verificar_publicacion` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `automatico_en_web` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_creacion` datetime NOT NULL,
    `creado_por` int(9) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `bolsa_grupos_rel` (
    `id` int(7) NOT NULL,
    `id_grupo` int(5) NOT NULL,
    `contrato` int(9) NOT NULL,
    `quien_solicita` enum('grupo', 'usuario') NOT NULL,
    `fecha_solicitud` datetime NOT NULL,
    `fecha_aceptacion` datetime NOT NULL,
    `tipo` enum('socio', 'admin') NOT NULL DEFAULT 'socio',
    `nombramiento` varchar(35) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `bolsa_inmobiliaria` (
    `id` int(15) NOT NULL,
    `contrato_solicitante` int(9) DEFAULT NULL,
    `contrato_solicitado` int(9) DEFAULT NULL,
    `conexion_aceptada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `bak_solicitante` int(9) NOT NULL,
    `bak_solicitado` int(9) NOT NULL,
    `fecha_solicitud` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_aceptacion` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `busca_historial` (
    `tiempo` double(15, 3) NOT NULL,
    `pais` varchar(35) NOT NULL,
    `provincia` varchar(35) NOT NULL,
    `ciudad` text NOT NULL,
    `tipo` text NOT NULL,
    `operacion` varchar(150) NOT NULL,
    `resultado` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `busca_propiedades` (
    `clave_sistema` bigint(20) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `datos` mediumtext NOT NULL,
    `publico` mediumtext NOT NULL,
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `campos_bloqueados` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `variable` varchar(35) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `campos_inhabilitados` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `variable` varchar(35) NOT NULL DEFAULT ''
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `campos_inmuebles` (
    `id` int(9) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `grupo_id` int(9) DEFAULT 1,
    `uso` enum('publico', 'interno') NOT NULL DEFAULT 'publico',
    `variable` varchar(35) NOT NULL DEFAULT '',
    `nombre` mediumtext DEFAULT NULL,
    `nombre_esp` varchar(35) NOT NULL DEFAULT '',
    `nombre_ing` varchar(35) NOT NULL DEFAULT '',
    `nombre_fra` varchar(35) NOT NULL DEFAULT '',
    `tipo_inmueble` int(3) NOT NULL DEFAULT 0,
    `tipo` enum(
        'caracter',
        'numerico',
        'selector',
        'texto'
    ) NOT NULL DEFAULT 'caracter',
    `longitud` varchar(250) NOT NULL DEFAULT '',
    `decimales` int(2) NOT NULL DEFAULT 0,
    `complemento_al_valor` varchar(10) NOT NULL DEFAULT '',
    `nulo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `orden` double(4, 1) NOT NULL DEFAULT 0.0,
    `activo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `forzoso` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `ti_relation` mediumtext DEFAULT NULL COMMENT 'JSON que establece con cuales tipos de inmuebles se relaciona, si es NULL o vacío, se relaciona con TODOS',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `campos_inmuebles_grupos` (
    `id` int(9) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `ids` varchar(50) NOT NULL,
    `name` mediumtext NOT NULL COMMENT 'JSON con los nombres en diferentes idiomas',
    `orden` int(2) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `casas_compartidas` (
    `id` bigint(20) NOT NULL,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `autorizado` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `public_key` varchar(50) DEFAULT NULL COMMENT 'Clave del inmueble que usará el socio que publicará en su web',
    `quien_solicita` int(9) NOT NULL DEFAULT 0,
    `metodo` enum('tradicional', 'ampi_plus') NOT NULL DEFAULT 'tradicional'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

DELIMITER $$

CREATE TRIGGER `after_casas_compartidas_delete` AFTER DELETE ON `casas_compartidas` FOR EACH ROW BEGIN
    DELETE FROM widget_inmuebles 
    WHERE 
        inmueble_id = OLD.clave_sistema AND
        contrato_id = OLD.contrato;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `after_casas_compartidas_update_202503300654` AFTER UPDATE ON `casas_compartidas` FOR EACH ROW BEGIN
    DECLARE v_terreno_m2 DECIMAL(10,2);
    IF NEW.autorizado = 'Si' THEN

        -- Obtener el valor del terreno en metros cuadrados
        SELECT 
            CASE 
                WHEN REGEXP_REPLACE(REGEXP_REPLACE(valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9]', '') = '' THEN NULL
                ELSE CAST(REGEXP_REPLACE(REGEXP_REPLACE(valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9.]', '') AS DECIMAL(10,2))
            END
        FROM valores_campos WHERE clave_sistema = NEW.clave_sistema AND variable_id = 1 
        INTO v_terreno_m2;

        -- Verificar si el inmueble ya existe en widget_inmuebles
        IF NOT EXISTS (
            SELECT 1 FROM widget_inmuebles 
            WHERE inmueble_id = NEW.clave_sistema 
            AND contrato_id = NEW.contrato
        ) THEN

            -- Insertar el inmueble en widget_inmuebles
            INSERT INTO widget_inmuebles (contrato_id, inmueble_id, public_key, tipo, status_id, venta, renta, eventual, precio_venta, precio_renta, precio_eventual, moneda, estado_id, ciudad_id, colonia_id, sucursal_id, terreno_m2, habitaciones, banos, construccion_m2, published_at, created_at, updated_at, precio_por_metro)
            SELECT 
                NEW.contrato,
                p.clave_sistema,
                CASE 
                    WHEN NEW.public_key IS NULL THEN CONCAT('CC-', NEW.contrato, '-', NEW.clave_sistema)
                    ELSE NEW.public_key
                END,
                p.tipo,
                p.status_id,
                CASE WHEN p.enventa = 'Si' THEN 1 ELSE 0 END,
                CASE WHEN p.enrenta = 'Si' THEN 1 ELSE 0 END,
                CASE WHEN p.endiaria = 'Si' THEN 1 ELSE 0 END,
                CASE 
                    WHEN p.precio_por_metro = 'Si' AND v_terreno_m2 IS NOT NULL THEN p.precio_venta * v_terreno_m2 * m.equiv_mxp
                    ELSE p.precio_venta * m.equiv_mxp
                END,
                p.precio_renta * m.equiv_mxp,
                p.precio_diaria * m.equiv_mxp,
                p.moneda,
                e.id,
                c.id,
                p.id_colonia,
                p.sucursal,
                v_terreno_m2, -- terreno_m2
                CASE -- habitaciones
                    WHEN REGEXP_REPLACE(vch.valor_esp, '[^0-9]', '') = '' THEN NULL
                    ELSE CAST(REGEXP_REPLACE(vch.valor_esp, '[^0-9]', '') AS UNSIGNED) % 1000
                END,
                CASE -- banos
                    WHEN REGEXP_REPLACE(vcb.valor_esp, '[^0-9½]', '') = '' THEN NULL
                    ELSE REGEXP_REPLACE(vcb.valor_esp, '[^0-9½]', '')
                END,
                CASE -- construccion_m2
                    WHEN REGEXP_REPLACE(REGEXP_REPLACE(vcc.valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9.]', '') = '.' THEN NULL
                    ELSE CAST(NULLIF(REGEXP_REPLACE(REGEXP_REPLACE(vcc.valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9.]', ''), '') AS DECIMAL(10,2))
                END,
                NOW(),
                p.fecha_ingreso,
                p.fecha_modificaciones,
                CASE 
                    WHEN p.precio_por_metro = 'Si' THEN p.precio_venta * m.equiv_mxp
                    ELSE NULL
                END
            FROM propiedades p
            INNER JOIN new_colonias nc ON nc.id = p.id_colonia
            INNER JOIN ciudades c ON c.id = nc.id_ciudad
            INNER JOIN estados e ON e.id = c.estado
            INNER JOIN monedas m ON m.siglas = p.moneda
            LEFT JOIN valores_campos vcc ON vcc.clave_sistema = p.clave_sistema AND vcc.variable_id = 2
            LEFT JOIN valores_campos vch ON vch.clave_sistema = p.clave_sistema AND vch.variable_id = 11
            LEFT JOIN valores_campos vcb ON vcb.clave_sistema = p.clave_sistema AND vcb.variable_id = 12
            WHERE p.clave_sistema = NEW.clave_sistema
            AND e.id > 0 
            AND c.id > 0
            AND p.id_colonia > 0;
        END IF;
    ELSEIF NEW.autorizado = 'No' THEN
        DELETE FROM widget_inmuebles 
        WHERE 
            inmueble_id = NEW.clave_sistema AND
            contrato_id = NEW.contrato;
    END IF;
END
$$

DELIMITER;

CREATE TABLE `citas` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `tipo` enum('propiedad', 'desarrollo') NOT NULL DEFAULT 'propiedad',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(15) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `observaciones` mediumtext DEFAULT NULL,
    `fecha_hora_solicitada` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_hora_cita` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `solicitud` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cancelada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `sesion` varchar(100) NOT NULL DEFAULT '',
    `respuesta` mediumtext DEFAULT NULL,
    `tipo_respuesta` varchar(7) NOT NULL DEFAULT '',
    `lugar_cita` int(7) NOT NULL DEFAULT 0,
    `como_llegar` mediumtext DEFAULT NULL,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `como_se_entero` varchar(50) NOT NULL DEFAULT '',
    `cita_por` varchar(20) NOT NULL DEFAULT 'Internet',
    `confirmo_cliente` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_buscar` mediumtext DEFAULT NULL,
    `cc_asesor` int(7) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `citas_20240319` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `tipo` enum('propiedad', 'desarrollo') NOT NULL DEFAULT 'propiedad',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(15) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `observaciones` text DEFAULT NULL,
    `fecha_hora_solicitada` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_hora_cita` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `solicitud` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cancelada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `sesion` varchar(100) NOT NULL DEFAULT '',
    `respuesta` text DEFAULT NULL,
    `tipo_respuesta` varchar(7) NOT NULL DEFAULT '',
    `lugar_cita` int(7) NOT NULL DEFAULT 0,
    `como_llegar` text DEFAULT NULL,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `como_se_entero` varchar(50) NOT NULL DEFAULT '',
    `cita_por` varchar(20) NOT NULL DEFAULT 'Internet',
    `confirmo_cliente` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `p_buscar` text DEFAULT NULL,
    `cc_asesor` int(7) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ciudades` (
    `id` int(5) NOT NULL,
    `estado` int(3) NOT NULL DEFAULT 0,
    `ciudad` varchar(70) NOT NULL DEFAULT '',
    `visitas` bigint(11) NOT NULL,
    `map_lng` varchar(30) NOT NULL,
    `map_lat` varchar(30) NOT NULL,
    `slug` varchar(70) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `colonias` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `colonia` varchar(70) NOT NULL DEFAULT '',
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(30) NOT NULL DEFAULT '',
    `pais` varchar(30) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `componentes` (
    `id` int(3) NOT NULL,
    `nombre` varchar(35) NOT NULL,
    `descripcion` text NOT NULL,
    `instrucciones` text NOT NULL,
    `clase` varchar(35) NOT NULL,
    `tipo` enum('panel', 'web', 'todo') NOT NULL,
    `recarga_cada` int(4) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_general_ci;

CREATE TABLE `componentes_campos` (
    `id` int(5) NOT NULL,
    `id_componente` int(3) NOT NULL,
    `campo` varchar(50) NOT NULL,
    `variable` varchar(15) NOT NULL,
    `tipo` enum(
        'caracter',
        'numerico',
        'texto'
    ) NOT NULL,
    `longitud` int(3) NOT NULL,
    `password` enum('No', 'Si') NOT NULL DEFAULT 'No',
    `en_titulo` enum('No', 'Si') NOT NULL DEFAULT 'No'
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_general_ci;

CREATE TABLE `componentes_usuarios` (
    `id` int(7) NOT NULL,
    `contrato` int(5) NOT NULL,
    `asesor` int(5) NOT NULL,
    `componente` int(3) NOT NULL,
    `columna` int(1) NOT NULL,
    `orden` int(2) NOT NULL,
    `donde` varchar(50) NOT NULL,
    `fijo` enum('Si', 'No') NOT NULL DEFAULT 'Si'
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_general_ci;

CREATE TABLE `componentes_valores` (
    `id` int(7) NOT NULL,
    `id_campo` int(5) NOT NULL,
    `id_comp_usuario` int(5) NOT NULL,
    `valor` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_general_ci;

CREATE TABLE `config` (
    `contrato` int(9) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `correo_ventas` varchar(60) NOT NULL DEFAULT '',
    `correo_contratacion` varchar(60) NOT NULL DEFAULT '',
    `correo_comentarios` varchar(60) NOT NULL DEFAULT '',
    `correo_dirgral` varchar(60) NOT NULL DEFAULT '',
    `dominio` varchar(50) NOT NULL DEFAULT '',
    `dominio2` varchar(50) DEFAULT NULL,
    `redes_sociales` mediumtext DEFAULT NULL COMMENT 'JSON con los usuarios para diferentes redes sociales, se usa par render sobre todo',
    `ruta` varchar(100) NOT NULL DEFAULT '',
    `inmuebles` int(5) NOT NULL DEFAULT 99999,
    `foto_ancho` int(3) NOT NULL DEFAULT 320,
    `por_ciudades` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `prop_por_pag` int(2) NOT NULL DEFAULT 7,
    `prop_por_linea` int(1) NOT NULL DEFAULT 1,
    `tipo_tema` enum('Generico', 'Personalizado') NOT NULL DEFAULT 'Personalizado',
    `theme` varchar(30) NOT NULL DEFAULT 'Default',
    `theme_hash` varchar(100) DEFAULT NULL,
    `theme_dev` mediumtext DEFAULT NULL,
    `nueva_ventana` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `ligas_comunes` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `ultimo_acceso` timestamp NULL DEFAULT NULL,
    `combinar_ampi` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `notifica_telefono` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `telefono_props` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `transparencia_fotos` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `tipo_fotos` enum(
        'galeria',
        'navegador',
        'ambos'
    ) NOT NULL DEFAULT 'ambos',
    `fotos_por_linea` int(2) NOT NULL DEFAULT 4,
    `tipo_galeria` enum('dinamica', 'estatica') NOT NULL DEFAULT 'dinamica',
    `socio_ampi` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `funcion_ampi` int(2) NOT NULL DEFAULT 0,
    `ampi_admin` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `estado` varchar(50) NOT NULL DEFAULT '',
    `orden_inmuebles` enum(
        'precio',
        'colonia',
        'fecha',
        'claves'
    ) NOT NULL DEFAULT 'fecha',
    `forma_orden_inmuebles` enum('ascendente', 'descendente') NOT NULL DEFAULT 'descendente',
    `enviar_nextel` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `fotos` enum('purplehaze', 'otro') NOT NULL DEFAULT 'otro',
    `moneda_predeterminada` char(3) NOT NULL DEFAULT 'MXP',
    `mostrar_monedas` varchar(250) NOT NULL DEFAULT 'MXP,USD',
    `idioma` enum('esp', 'ing') NOT NULL DEFAULT 'esp',
    `campos_por_linea` int(2) NOT NULL DEFAULT 2,
    `leyenda_b_favoritos` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `lat_club_negocios` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `ultimo_cobro` date DEFAULT NULL,
    `dias_novedades` int(3) NOT NULL DEFAULT 30,
    `notificar_propietarios` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `detalles_new` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Determina el status del contrato',
    `servicio_contratado` varchar(15) NOT NULL DEFAULT 'SI-M-7001',
    `pagado_hasta` date DEFAULT NULL,
    `vencido_si` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `vencimiento` date DEFAULT NULL,
    `prorroga` date DEFAULT NULL,
    `saldo` double(9, 2) NOT NULL DEFAULT 0.00,
    `adeudo` double(9, 2) NOT NULL DEFAULT 0.00,
    `a_vencer` double(9, 2) NOT NULL DEFAULT 0.00,
    `saldo_a_favor` double(9, 2) NOT NULL DEFAULT 0.00,
    `credito` double(9, 2) NOT NULL DEFAULT 0.00,
    `msg_panel_desactivado` date DEFAULT NULL,
    `presentar_con` enum('fotos', 'tour') NOT NULL DEFAULT 'fotos',
    `front` enum('0', '1') NOT NULL DEFAULT '0' COMMENT 'Determina si tiene front (website) activo o inactivo el contrato',
    `deptID_livephp` int(10) DEFAULT NULL,
    `custom_data` mediumtext DEFAULT NULL COMMENT 'JSON con estructura de datos a renderizar en los templates',
    `external_connections` mediumtext DEFAULT NULL COMMENT 'JSON con los API_KEY de servicios de externos (portales, etc)',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `config_fotos` (
    `contrato` int(7) NOT NULL,
    `logo` text NOT NULL COMMENT 'Especifica la posición donde se coloca el logo, si=superior-izquierda'
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `config_mulbin` (
    `id` int(7) NOT NULL,
    `contrato` int(7) NOT NULL COMMENT 'numero de contrato',
    `ase` int(7) NOT NULL COMMENT 'id de asesor',
    `panel` text NOT NULL,
    `site` text NOT NULL,
    `tobi` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `config_si` (
    `sistemas_activados` int(7) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `contactos` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `trato` varchar(10) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `sexo` enum('hombre', 'mujer') NOT NULL DEFAULT 'hombre',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(15) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `fax` varchar(30) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `domicilio` varchar(150) NOT NULL DEFAULT '',
    `ciudad` varchar(50) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `tipo` varchar(20) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `telefono_trabajo` varchar(30) NOT NULL DEFAULT '',
    `observaciones` text DEFAULT NULL,
    `p_buscar` text DEFAULT NULL,
    `quien_registro` int(5) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `contactos_etq` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL,
    `etiqueta` varchar(50) NOT NULL,
    `descripcion` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `contactos_etq_rel` (
    `contrato` int(9) NOT NULL,
    `etiqueta` int(7) NOT NULL,
    `contacto` int(7) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `contacto_telefonico` (
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `contrato` int(9) NOT NULL DEFAULT 0,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `ip` varchar(30) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `contenidos` (
    `id` bigint(11) NOT NULL,
    `contrato` int(9) NOT NULL,
    `bloque` varchar(50) NOT NULL COMMENT 'Bloque al que pertenece el enlace',
    `lng` varchar(3) NOT NULL COMMENT 'Idioma del contenido',
    `head` text NOT NULL COMMENT 'JSON para definir title, keywords y description de los contenidos',
    `body` text NOT NULL COMMENT 'Contenido de la página o enlace en el caso sea',
    `btn` varchar(100) NOT NULL COMMENT 'Texto del botón o enlace para el contenido',
    `llamada` varchar(100) NOT NULL COMMENT 'Es la llamada URL para btn',
    `tipo` enum(
        'contenido',
        'enlace',
        'seccion'
    ) NOT NULL DEFAULT 'contenido',
    `home` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Si está como home del website del corredor',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'activa o inactiva',
    `pos` int(3) NOT NULL COMMENT 'Orden para los enlaces',
    `rel_lng` text NOT NULL COMMENT 'Es la equivalencia del enlace o contenido con otros idiomas'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `desarrollos` (
    `clave_sistema` int(15) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `claveprop` varchar(25) NOT NULL DEFAULT '',
    `nombredes` varchar(75) NOT NULL DEFAULT '',
    `colonia` varchar(70) NOT NULL DEFAULT '',
    `muestra_colonia` varchar(30) NOT NULL DEFAULT '',
    `ciudad` varchar(70) NOT NULL DEFAULT '',
    `provincia` varchar(30) NOT NULL DEFAULT '',
    `pais` varchar(30) NOT NULL DEFAULT 'México',
    `zona` varchar(30) NOT NULL DEFAULT '',
    `terreno` int(7) NOT NULL DEFAULT 0,
    `num_casas` int(5) NOT NULL DEFAULT 0,
    `casas_disponibles` int(5) NOT NULL DEFAULT 0,
    `casas_vendidas` int(5) NOT NULL DEFAULT 0,
    `i_propietario` int(9) NOT NULL DEFAULT 0,
    `i_calle_numero` varchar(70) NOT NULL DEFAULT '',
    `i_entre_calles` varchar(70) NOT NULL DEFAULT '',
    `i_colonia` varchar(30) NOT NULL DEFAULT '',
    `i_municipio` varchar(25) NOT NULL DEFAULT '',
    `i_delegacion` varchar(25) NOT NULL DEFAULT '',
    `compartir_desarrollo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `asesor` int(7) NOT NULL DEFAULT 0,
    `tipo_promocion` enum('EXCLUSIVA', 'OPCION') NOT NULL DEFAULT 'EXCLUSIVA',
    `anuncio_esp` mediumtext NOT NULL,
    `anuncio_ing` mediumtext NOT NULL,
    `anuncio_fra` mediumtext NOT NULL,
    `ubicacion` mediumtext NOT NULL,
    `sucursal` int(7) NOT NULL DEFAULT 0,
    `status_web` enum('publicado', 'sin publicar') NOT NULL DEFAULT 'publicado',
    `fecha_expiracion` date NOT NULL DEFAULT '0000-00-00',
    `en_resumen` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `i_observaciones` mediumtext NOT NULL,
    `alta` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cambios` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `buscar_pub` mediumtext NOT NULL,
    `buscar_int` mediumtext NOT NULL,
    `orden` int(3) NOT NULL DEFAULT 999
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `desarrollos_20240316` (
    `clave_sistema` int(15) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `claveprop` varchar(25) NOT NULL DEFAULT '',
    `nombredes` varchar(75) NOT NULL DEFAULT '',
    `colonia` varchar(70) NOT NULL DEFAULT '',
    `muestra_colonia` varchar(30) NOT NULL DEFAULT '',
    `ciudad` varchar(70) NOT NULL DEFAULT '',
    `provincia` varchar(30) NOT NULL DEFAULT '',
    `pais` varchar(30) NOT NULL DEFAULT 'México',
    `zona` varchar(30) NOT NULL DEFAULT '',
    `terreno` int(7) NOT NULL DEFAULT 0,
    `num_casas` int(5) NOT NULL DEFAULT 0,
    `casas_disponibles` int(5) NOT NULL DEFAULT 0,
    `casas_vendidas` int(5) NOT NULL DEFAULT 0,
    `i_propietario` int(9) NOT NULL DEFAULT 0,
    `i_calle_numero` varchar(70) NOT NULL DEFAULT '',
    `i_entre_calles` varchar(70) NOT NULL DEFAULT '',
    `i_colonia` varchar(30) NOT NULL DEFAULT '',
    `i_municipio` varchar(25) NOT NULL DEFAULT '',
    `i_delegacion` varchar(25) NOT NULL DEFAULT '',
    `compartir_desarrollo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `asesor` int(7) NOT NULL DEFAULT 0,
    `tipo_promocion` enum('EXCLUSIVA', 'OPCION') NOT NULL DEFAULT 'EXCLUSIVA',
    `anuncio_esp` text NOT NULL,
    `anuncio_ing` text NOT NULL,
    `anuncio_fra` text NOT NULL,
    `ubicacion` text NOT NULL,
    `sucursal` int(7) NOT NULL DEFAULT 0,
    `status_web` enum('publicado', 'sin publicar') NOT NULL DEFAULT 'publicado',
    `fecha_expiracion` date NOT NULL DEFAULT '0000-00-00',
    `en_resumen` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `i_observaciones` text NOT NULL,
    `alta` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cambios` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `buscar_pub` text NOT NULL,
    `buscar_int` text NOT NULL,
    `orden` int(3) NOT NULL DEFAULT 999
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `desarrollos_compartidos` (
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `autorizado` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `desarrollos_etq` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL,
    `etiqueta` varchar(50) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `desarrollos_fotos` (
    `id` bigint(20) NOT NULL,
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `foto_num` int(2) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `desarrollos_fotos_20240316` (
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `foto_num` int(2) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `des_rel_etq` (
    `contrato` int(9) NOT NULL,
    `etiqueta` int(7) NOT NULL,
    `clave_sistema` int(15) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `des_v_d` (
    `id` int(9) NOT NULL,
    `desarrollo` int(15) NOT NULL DEFAULT 0,
    `casa` varchar(15) NOT NULL DEFAULT '',
    `prop_clave_sistema` bigint(20) DEFAULT NULL,
    `status` enum(
        'disponible',
        'vendida',
        'apartada'
    ) NOT NULL DEFAULT 'disponible',
    `precio` int(9) NOT NULL DEFAULT 0,
    `observaciones` varchar(250) NOT NULL DEFAULT ''
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `des_v_d_20240317` (
    `id` int(9) NOT NULL,
    `desarrollo` int(15) NOT NULL DEFAULT 0,
    `casa` varchar(15) NOT NULL DEFAULT '',
    `prop_clave_sistema` int(15) NOT NULL DEFAULT 0,
    `status` enum(
        'disponible',
        'vendida',
        'apartada'
    ) NOT NULL DEFAULT 'disponible',
    `precio` int(9) NOT NULL DEFAULT 0,
    `observaciones` varchar(250) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `directorio_inmobiliario` (
    `id` bigint(20) NOT NULL,
    `ids` varchar(43) NOT NULL,
    `contrato_id` int(9) DEFAULT NULL,
    `name` varchar(34) NOT NULL,
    `lastname` varchar(43) NOT NULL,
    `company` varchar(50) DEFAULT NULL,
    `phone` text DEFAULT NULL COMMENT 'JSON con números de contacto',
    `email` varchar(50) DEFAULT NULL,
    `picture` varchar(5) DEFAULT NULL,
    `seccion` varchar(50) DEFAULT NULL COMMENT 'Ciudad o identificador al que pertenece',
    `origin` varchar(50) DEFAULT NULL COMMENT 'De donde viene sun captura, AMPI, por usuario, cliente SI',
    `updates` text DEFAULT NULL COMMENT 'JSON con marcas de TS de los updates que ha tenido el registro',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=activo,0=revisión,-1=inválido',
    `created` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `pabuscar` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `documentos` (
    `id` bigint(20) NOT NULL,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `tipo_documento` varchar(15) NOT NULL DEFAULT '',
    `periodo` int(3) NOT NULL DEFAULT 0,
    `inicio_contrato` date NOT NULL DEFAULT '0000-00-00',
    `firma_contrato` date NOT NULL DEFAULT '0000-00-00',
    `arrendatario` int(9) NOT NULL DEFAULT 0,
    `fiador` int(9) NOT NULL DEFAULT 0,
    `uso_del_inmueble` varchar(30) NOT NULL DEFAULT '',
    `precio` int(12) NOT NULL DEFAULT 0,
    `incremento_al_finalizar` int(3) NOT NULL DEFAULT 0,
    `deposito` int(12) NOT NULL DEFAULT 0,
    `linea_telefonica` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `i_dep_telefono` int(9) NOT NULL DEFAULT 0,
    `contenido` text NOT NULL,
    `tipo_garantia` varchar(15) NOT NULL DEFAULT '',
    `garantia` text NOT NULL,
    `domicilio_inmueble_garantia` varchar(150) NOT NULL DEFAULT '',
    `catastral_inmueble_garantia` varchar(30) NOT NULL DEFAULT '',
    `registro_inmueble_garantia` varchar(100) NOT NULL DEFAULT '',
    `nombre_afianzadora` varchar(50) NOT NULL DEFAULT '',
    `domicilio_afianzadora` varchar(150) NOT NULL DEFAULT '',
    `telefono_afianzadora` varchar(30) NOT NULL DEFAULT '',
    `numero_fianza` varchar(50) NOT NULL DEFAULT '',
    `cantidad_fianza` int(9) NOT NULL DEFAULT 0,
    `ciudad_firma` varchar(30) NOT NULL DEFAULT '',
    `estado_firma` varchar(25) NOT NULL DEFAULT '',
    `d_max_personas` int(3) NOT NULL DEFAULT 0,
    `d_personas_a_hospedar` int(3) NOT NULL DEFAULT 0,
    `d_precio_por_extra` int(7) NOT NULL DEFAULT 0,
    `forma_de_pago` text NOT NULL,
    `comision` int(3) NOT NULL DEFAULT 0,
    `observaciones` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `encuestas` (
    `id` int(3) NOT NULL,
    `fecha` date NOT NULL,
    `nombre` varchar(50) NOT NULL,
    `url` varchar(250) NOT NULL,
    `caducidad` date NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `encuestas_r` (
    `id` int(7) NOT NULL,
    `contrato` int(7) NOT NULL,
    `encuesta_id` int(3) NOT NULL,
    `fecha_hora` datetime NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `envios_nextel` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `numero` varchar(10) NOT NULL DEFAULT '0',
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `quien_envia` varchar(70) NOT NULL DEFAULT '',
    `seccion` varchar(15) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `estados` (
    `id` int(5) NOT NULL,
    `pais` int(3) NOT NULL DEFAULT 0,
    `estado` varchar(50) NOT NULL DEFAULT '',
    `slug` varchar(50) DEFAULT NULL,
    `short` varchar(5) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `favoritos` (
    `usuario` varchar(100) NOT NULL DEFAULT '',
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `claves` text DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `fav_historial` (
    `clave_sistema` bigint(20) NOT NULL DEFAULT 0,
    `contador` int(7) NOT NULL DEFAULT 0,
    `ultima_alta` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `contrato` int(9) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `fav_historial_20240316` (
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `contador` int(7) NOT NULL DEFAULT 0,
    `ultima_alta` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `contrato` int(9) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `files` (
    `id` bigint(20) NOT NULL,
    `contrato_id` int(9) NOT NULL,
    `type` enum(
        'image',
        'document',
        'audio',
        'video',
        'other'
    ) NOT NULL DEFAULT 'image',
    `tag` varchar(35) DEFAULT NULL,
    `filename` varchar(100) NOT NULL,
    `extension` varchar(5) NOT NULL,
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=disabled,1=enabled',
    `metadata` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `f_des_fotos` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` text NOT NULL,
    `enlaces` text NOT NULL,
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL,
    `foto_num` int(2) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `orden` int(3) NOT NULL,
    `archivo` varchar(100) NOT NULL,
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `f_des_panoramicas` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` text NOT NULL,
    `enlaces` text NOT NULL,
    `tipo` enum('360', '180') NOT NULL DEFAULT '360',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `pan_num` int(2) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `t_pan` enum('cilindrica', 'esferica') NOT NULL DEFAULT 'cilindrica',
    `orden` int(3) NOT NULL,
    `archivo` varchar(100) NOT NULL,
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `f_des_planos` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` text NOT NULL,
    `enlaces` text NOT NULL,
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL,
    `foto_num` int(2) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `orden` int(3) NOT NULL,
    `archivo` varchar(100) NOT NULL,
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `f_fotos` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` mediumtext DEFAULT NULL COMMENT 'JSON con la descripción de la foto en varios idiomas',
    `enlaces` mediumtext DEFAULT NULL COMMENT 'JSON con info de los enlaces que tiene la foto',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `foto_num` smallint(4) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `con_logo` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `orden` int(3) NOT NULL DEFAULT 0,
    `archivo` varchar(100) NOT NULL DEFAULT '',
    `sitio_web` tinyint(1) NOT NULL DEFAULT 1,
    `bolsa` tinyint(1) NOT NULL DEFAULT 1,
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `f_fotos_20240317` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` text DEFAULT NULL COMMENT 'JSON con la descripción de la foto en varios idiomas',
    `enlaces` text DEFAULT NULL COMMENT 'JSON con info de los enlaces que tiene la foto',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `foto_num` smallint(4) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `con_logo` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `orden` int(3) NOT NULL DEFAULT 0,
    `archivo` varchar(100) NOT NULL DEFAULT '',
    `sitio_web` tinyint(1) NOT NULL DEFAULT 1,
    `bolsa` tinyint(1) NOT NULL DEFAULT 1,
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `f_panoramicas` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` mediumtext DEFAULT NULL COMMENT 'JSON con la descripción de la pano en varios idiomas',
    `enlaces` mediumtext DEFAULT NULL COMMENT 'JSON con info de los enlaces que tiene la pano',
    `tipo` enum('360', '180') NOT NULL DEFAULT '360',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `pan_num` smallint(4) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `t_pan` enum('cilindrica', 'esferica') NOT NULL DEFAULT 'cilindrica',
    `orden` int(3) NOT NULL,
    `archivo` varchar(100) NOT NULL,
    `sitio_web` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `bolsa` enum('Si', 'No') DEFAULT 'Si',
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `f_panoramicas_20240317` (
    `id` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `descripcion` text DEFAULT NULL COMMENT 'JSON con la descripción de la pano en varios idiomas',
    `enlaces` text DEFAULT NULL COMMENT 'JSON con info de los enlaces que tiene la pano',
    `tipo` enum('360', '180') NOT NULL DEFAULT '360',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `pan_num` smallint(4) NOT NULL DEFAULT 0,
    `foto_principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `t_pan` enum('cilindrica', 'esferica') NOT NULL DEFAULT 'cilindrica',
    `orden` int(3) NOT NULL,
    `archivo` varchar(100) NOT NULL,
    `sitio_web` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `bolsa` enum('Si', 'No') DEFAULT 'Si',
    `checado` date NOT NULL DEFAULT '0000-00-00',
    `mig` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `gpt` (
    `id` bigint(20) NOT NULL,
    `contrato` int(9) NOT NULL,
    `prop_id` bigint(20) DEFAULT NULL,
    `claveprop` varchar(25) DEFAULT NULL,
    `response` text DEFAULT NULL,
    `prompt` text NOT NULL COMMENT 'JSON con: template, chars_prompt_sended, usage',
    `total_tokens` int(9) DEFAULT NULL,
    `created` timestamp NOT NULL DEFAULT current_timestamp(),
    `ts_responsed` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `ips_busca` (
    `id` bigint(15) NOT NULL,
    `fecha` date NOT NULL,
    `ip` varchar(35) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `languages` (
    `id` int(2) NOT NULL,
    `code` varchar(5) NOT NULL,
    `subdom` varchar(2) NOT NULL,
    `lang` varchar(25) NOT NULL,
    `orden` int(2) NOT NULL DEFAULT 99,
    `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `lateral` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `buscador` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `secciones` enum('Si', 'No') NOT NULL DEFAULT 'Si'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `lateral_secciones` (
    `id` int(9) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `tit_esp` varchar(35) NOT NULL DEFAULT '',
    `tit_ing` varchar(35) NOT NULL DEFAULT '',
    `tit_fra` varchar(35) NOT NULL DEFAULT '',
    `link_html_esp` mediumtext DEFAULT NULL,
    `link_html_ing` mediumtext DEFAULT NULL,
    `contenido_esp` mediumtext DEFAULT NULL,
    `contenido_ing` mediumtext DEFAULT NULL,
    `contenido_fra` mediumtext DEFAULT NULL,
    `posicion` int(2) NOT NULL DEFAULT 0,
    `tipo` enum(
        'contenido',
        'link',
        'section',
        'webpage'
    ) NOT NULL DEFAULT 'contenido',
    `activa` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `bloque_esp` varchar(30) NOT NULL DEFAULT '',
    `bloque_ing` varchar(30) NOT NULL DEFAULT '',
    `bloque_fra` varchar(30) NOT NULL DEFAULT '',
    `camuflaje` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `titpg_esp` varchar(100) NOT NULL DEFAULT '',
    `titpg_ing` varchar(100) NOT NULL DEFAULT '',
    `titpg_fra` varchar(100) NOT NULL DEFAULT '',
    `target` varchar(25) NOT NULL DEFAULT '',
    `webpage` varchar(120) DEFAULT NULL COMMENT 'JSON que define {p:página a cargar, it:boolean 1=integrado a la plantilla}',
    `titles` mediumtext DEFAULT NULL,
    `theme` varchar(35) DEFAULT NULL,
    `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `lateral_secciones_20231129` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `id` int(9) NOT NULL,
    `tit_esp` varchar(35) NOT NULL DEFAULT '',
    `tit_ing` varchar(35) NOT NULL DEFAULT '',
    `tit_fra` varchar(35) NOT NULL DEFAULT '',
    `link_html_esp` text DEFAULT NULL,
    `link_html_ing` text DEFAULT NULL,
    `contenido_esp` text DEFAULT NULL,
    `contenido_ing` text DEFAULT NULL,
    `contenido_fra` text DEFAULT NULL,
    `posicion` int(2) NOT NULL DEFAULT 0,
    `tipo` enum(
        'contenido',
        'link',
        'section',
        'webpage'
    ) NOT NULL DEFAULT 'contenido',
    `activa` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `bloque_esp` varchar(30) NOT NULL DEFAULT '',
    `bloque_ing` varchar(30) NOT NULL DEFAULT '',
    `bloque_fra` varchar(30) NOT NULL DEFAULT '',
    `camuflaje` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `titpg_esp` varchar(100) NOT NULL DEFAULT '',
    `titpg_ing` varchar(100) NOT NULL DEFAULT '',
    `titpg_fra` varchar(100) NOT NULL DEFAULT '',
    `target` varchar(25) NOT NULL DEFAULT '',
    `webpage` varchar(120) DEFAULT NULL COMMENT 'JSON que define {p:página a cargar, it:boolean 1=integrado a la plantilla}',
    `titles` text DEFAULT NULL,
    `theme` varchar(35) DEFAULT NULL,
    `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `lateral_secciones_20250203` (
    `contrato` int(9) DEFAULT NULL,
    `id` int(9) NOT NULL,
    `tit_esp` varchar(35) NOT NULL DEFAULT '',
    `tit_ing` varchar(35) NOT NULL DEFAULT '',
    `tit_fra` varchar(35) NOT NULL DEFAULT '',
    `link_html_esp` mediumtext DEFAULT NULL,
    `link_html_ing` mediumtext DEFAULT NULL,
    `contenido_esp` mediumtext DEFAULT NULL,
    `contenido_ing` mediumtext DEFAULT NULL,
    `contenido_fra` mediumtext DEFAULT NULL,
    `posicion` int(2) NOT NULL DEFAULT 0,
    `tipo` enum(
        'contenido',
        'link',
        'section',
        'webpage'
    ) NOT NULL DEFAULT 'contenido',
    `activa` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `principal` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `bloque_esp` varchar(30) NOT NULL DEFAULT '',
    `bloque_ing` varchar(30) NOT NULL DEFAULT '',
    `bloque_fra` varchar(30) NOT NULL DEFAULT '',
    `camuflaje` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `titpg_esp` varchar(100) NOT NULL DEFAULT '',
    `titpg_ing` varchar(100) NOT NULL DEFAULT '',
    `titpg_fra` varchar(100) NOT NULL DEFAULT '',
    `target` varchar(25) NOT NULL DEFAULT '',
    `webpage` varchar(120) DEFAULT NULL COMMENT 'JSON que define {p:página a cargar, it:boolean 1=integrado a la plantilla}',
    `titles` mediumtext DEFAULT NULL,
    `theme` varchar(35) DEFAULT NULL,
    `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `links` (
    `id` bigint(20) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `subdomain` varchar(30) NOT NULL,
    `menu` varchar(35) NOT NULL,
    `link` varchar(250) NOT NULL,
    `text` varchar(50) DEFAULT NULL,
    `llamada` mediumtext NOT NULL,
    `enabled` tinyint(1) NOT NULL DEFAULT 1,
    `orden` int(3) NOT NULL DEFAULT 999,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `machotes` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `documento` varchar(25) NOT NULL DEFAULT '',
    `contenido` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `machotes_clausulas` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `documento` varchar(25) NOT NULL DEFAULT '',
    `clausula` varchar(25) NOT NULL DEFAULT '',
    `contenido` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `monedas` (
    `id` int(2) NOT NULL,
    `siglas` char(3) NOT NULL DEFAULT '',
    `nombre_esp` varchar(20) NOT NULL DEFAULT '',
    `nombre_ing` varchar(20) NOT NULL DEFAULT '',
    `p_nombre_esp` varchar(20) NOT NULL DEFAULT '',
    `p_nombre_ing` varchar(20) NOT NULL DEFAULT '',
    `equiv_mxp` double(9, 5) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

DELIMITER $$

CREATE TRIGGER `after_monedas_update` AFTER UPDATE ON `monedas` FOR EACH ROW BEGIN
    IF NEW.equiv_mxp <> OLD.equiv_mxp THEN
        UPDATE widget_inmuebles 
        SET 
            precio_venta = precio_venta / OLD.equiv_mxp,
            precio_renta = precio_renta / OLD.equiv_mxp,
            precio_eventual = precio_eventual / OLD.equiv_mxp,
            precio_por_metro = CASE 
                WHEN precio_por_metro IS NOT NULL THEN precio_por_metro / OLD.equiv_mxp
                ELSE NULL
            END
        WHERE moneda = NEW.siglas;

        UPDATE widget_inmuebles 
        SET 
            precio_venta = precio_venta * NEW.equiv_mxp,
            precio_renta = precio_renta * NEW.equiv_mxp,
            precio_eventual = precio_eventual * NEW.equiv_mxp,
            precio_por_metro = CASE 
                WHEN precio_por_metro IS NOT NULL THEN precio_por_metro * NEW.equiv_mxp
                ELSE NULL
            END
        WHERE moneda = NEW.siglas;
    END IF;
END
$$

DELIMITER;

CREATE TABLE `movimientos` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `aid` int(7) NOT NULL DEFAULT 0,
    `quien_fue` varchar(50) NOT NULL,
    `datos_aid` varchar(150) NOT NULL DEFAULT '',
    `movimiento` varchar(100) NOT NULL DEFAULT '',
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `id_mov_prop` bigint(15) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `movpropiedades` (
    `id` bigint(15) NOT NULL,
    `contrato` int(9) NOT NULL COMMENT 'Contrato de la inmobiliaria',
    `clave_sistema` int(15) NOT NULL COMMENT 'Clave de la propiedad en el Sistema',
    `fecha` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Fecha del Movimiento',
    `movimiento` varchar(45) NOT NULL COMMENT 'Movimiento realizado insert, update, delete'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `mov_propiedades` (
    `id` bigint(15) NOT NULL,
    `clave_sistema` int(7) NOT NULL,
    `claveprop` varchar(50) NOT NULL,
    `info` text NOT NULL,
    `quien_fue` varchar(50) NOT NULL,
    `fecha_hora` datetime NOT NULL,
    `datos_maquina` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `new_colonias` (
    `contrato` int(9) DEFAULT NULL,
    `id` int(10) NOT NULL,
    `id_ciudad` int(5) NOT NULL DEFAULT 0,
    `colonia` varchar(70) NOT NULL DEFAULT '',
    `map_lng` varchar(30) DEFAULT NULL,
    `map_lat` varchar(30) DEFAULT NULL,
    `slug` varchar(140) DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `no_mostrar_aviso` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `usuario` varchar(15) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `paises` (
    `id` int(3) NOT NULL,
    `pais` varchar(30) NOT NULL DEFAULT '',
    `activo` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `map_lng` varchar(30) NOT NULL,
    `map_lat` varchar(30) NOT NULL,
    `short` varchar(3) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `panoramicas` (
    `id` int(9) NOT NULL,
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `tipo` enum('360', '180') NOT NULL DEFAULT '180',
    `de_donde_esp` varchar(30) NOT NULL DEFAULT '',
    `de_donde_ing` varchar(30) NOT NULL DEFAULT '',
    `de_donde_fra` varchar(30) NOT NULL DEFAULT '',
    `pan_num` int(2) NOT NULL DEFAULT 0,
    `principal` enum('Si', 'No') NOT NULL DEFAULT 'No'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `pano_enlaces` (
    `id` int(9) NOT NULL DEFAULT 0,
    `id_pan` int(9) NOT NULL DEFAULT 0,
    `x` int(5) NOT NULL DEFAULT 0,
    `y` int(5) NOT NULL DEFAULT 0,
    `enlaza` int(9) NOT NULL DEFAULT 0,
    `img` varchar(70) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `preguntas` (
    `numero` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `pregunta` text DEFAULT NULL,
    `respuesta` text DEFAULT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `publicar` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_hora_p` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_hora_r` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `tipo_respuesta` varchar(7) NOT NULL DEFAULT '',
    `asesor` int(7) DEFAULT NULL,
    `p_buscar` text DEFAULT NULL,
    `cc_asesor` int(7) DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `preguntas_20250114` (
    `numero` int(9) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `pregunta` text DEFAULT NULL,
    `respuesta` text DEFAULT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `publicar` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_hora_p` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_hora_r` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `tipo_respuesta` varchar(7) NOT NULL DEFAULT '',
    `asesor` int(7) NOT NULL DEFAULT 0,
    `p_buscar` text DEFAULT NULL,
    `cc_asesor` int(7) DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `propiedades` (
    `clave_sistema` bigint(20) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `contrato` int(9) DEFAULT NULL,
    `status_id` int(5) DEFAULT 1,
    `claveprop` varchar(25) NOT NULL DEFAULT '',
    `nombreprop` varchar(50) NOT NULL,
    `tipo` int(2) UNSIGNED ZEROFILL NOT NULL DEFAULT 00,
    `residencial` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `comercial` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `industrial` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `vacacional` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `precio_venta` int(12) NOT NULL DEFAULT 0,
    `precio_renta` int(12) NOT NULL DEFAULT 0,
    `precio_diaria` int(12) NOT NULL DEFAULT 0,
    `precio_traspaso` int(12) NOT NULL DEFAULT 0,
    `precio_venta_mxp` double(15, 5) DEFAULT NULL,
    `precio_vta_total_mxp` double(15, 5) DEFAULT NULL,
    `precio_renta_mxp` double(15, 5) DEFAULT NULL,
    `precio_diaria_mxp` double(15, 5) DEFAULT NULL,
    `precio_traspaso_mxp` double(15, 5) DEFAULT NULL,
    `id_colonia` int(10) DEFAULT NULL,
    `colonia` varchar(70) DEFAULT NULL,
    `muestra_colonia` varchar(30) NOT NULL DEFAULT '',
    `zona` varchar(30) DEFAULT NULL,
    `intro_corta_esp` mediumtext DEFAULT NULL,
    `intro_corta_ing` mediumtext DEFAULT NULL,
    `anuncio_esp` mediumtext DEFAULT NULL,
    `anuncio_ing` mediumtext DEFAULT NULL,
    `anuncio_fra` mediumtext DEFAULT NULL,
    `caract_esp` mediumtext DEFAULT NULL,
    `caract_ing` mediumtext DEFAULT '',
    `caract_fra` mediumtext DEFAULT '',
    `ciudad` varchar(70) DEFAULT NULL,
    `provincia` varchar(70) DEFAULT NULL,
    `pais` varchar(30) NOT NULL DEFAULT 'México',
    `moneda` char(3) NOT NULL DEFAULT '',
    `enventa` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `enrenta` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `endiaria` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `entraspaso` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `precio_por_metro` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_ingreso` timestamp NOT NULL DEFAULT current_timestamp(),
    `codigo_postal` int(5) UNSIGNED ZEROFILL DEFAULT 00000,
    `operacion_hecha` varchar(10) NOT NULL DEFAULT '',
    `fecha_modificaciones` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `eventos` mediumtext DEFAULT NULL COMMENT 'JSON que describe la historia del inmueble',
    `contador` int(5) NOT NULL DEFAULT 0,
    `fecha_expiracion` date NOT NULL DEFAULT '0000-00-00',
    `i_calle_numero` varchar(100) NOT NULL DEFAULT '',
    `i_num_ext` varchar(10) DEFAULT NULL,
    `i_num_int` varchar(10) DEFAULT NULL,
    `i_entre_calles` varchar(100) DEFAULT '',
    `publica_ubicacion` enum('interno', 'bolsa', 'web') NOT NULL DEFAULT 'interno',
    `i_propietario` int(9) NOT NULL DEFAULT 0,
    `i_observaciones` mediumtext DEFAULT NULL,
    `i_inventario` mediumtext DEFAULT NULL,
    `comision_ampi` double(5, 2) NOT NULL DEFAULT 0.00,
    `sucursal` int(7) DEFAULT NULL,
    `comparto_comision` double(5, 2) NOT NULL DEFAULT 0.00,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `i_porcentaje_comision` double(5, 2) NOT NULL DEFAULT 0.00,
    `tipo_promocion` enum(
        'DE PALABRA',
        'CARTA AUTORIZACION',
        'EN OPCION',
        'EN EXCLUSIVA'
    ) DEFAULT NULL,
    `i_requisito_renta` enum(
        'Fianza',
        'Fiador',
        'Negociable'
    ) NOT NULL DEFAULT 'Fiador',
    `desarrollo` int(15) NOT NULL DEFAULT 0,
    `aid` int(7) NOT NULL DEFAULT 0,
    `keywords_esp` varchar(150) NOT NULL DEFAULT '',
    `keywords_ing` varchar(150) NOT NULL DEFAULT '',
    `keywords_fra` varchar(150) NOT NULL DEFAULT '',
    `en_resumen` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `presentar_con` enum('tour', 'fotos') NOT NULL DEFAULT 'fotos',
    `status_web` enum('publicado', 'sin publicar') NOT NULL DEFAULT 'publicado',
    `i_tipo_comision_rta` enum('1 mes', 'cantidad fija') NOT NULL DEFAULT '1 mes',
    `i_comision_rta` int(9) NOT NULL DEFAULT 0,
    `t_comision_ampi` enum(
        'sobre comision',
        'sobre precio'
    ) NOT NULL DEFAULT 'sobre precio',
    `t_comparto_comision` enum(
        'sobre comision',
        'sobre precio'
    ) NOT NULL DEFAULT 'sobre precio',
    `rta_comparto_comision` double(5, 2) NOT NULL DEFAULT 0.00,
    `rta_comision_ampi` double(5, 2) NOT NULL DEFAULT 0.00,
    `fin_contrato` date NOT NULL DEFAULT '0000-00-00',
    `ampi_plus` enum('Si', 'No', 'Ya') NOT NULL DEFAULT 'No',
    `conf_portal_ampi` date DEFAULT NULL,
    `msg_conf_portal_ampi` date DEFAULT NULL,
    `clave_externa` varchar(100) DEFAULT NULL,
    `map_lng` varchar(30) DEFAULT NULL,
    `map_lat` varchar(30) DEFAULT NULL,
    `notas_mapa` mediumtext DEFAULT NULL,
    `map_lng_despista` varchar(30) DEFAULT NULL,
    `map_lat_despista` varchar(30) DEFAULT NULL,
    `ocultar_mapa` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `html_especial` varchar(1024) DEFAULT NULL COMMENT 'Campo para enlace de inserción de videos de youtube por ejemplo',
    `CamposMC` mediumtext DEFAULT NULL COMMENT 'JSON que relaciona como se debe mostrar un determinado campo, por ejemplo Venta -> Preventa',
    `title_seo` mediumtext DEFAULT NULL COMMENT 'JSON con el title para SEO en distintos idiomas',
    `desc_seo` mediumtext DEFAULT NULL,
    `key_str` varchar(50) DEFAULT NULL,
    `external_relations` mediumtext DEFAULT NULL COMMENT 'JSON con la relación a servicios externos',
    `generated_by` enum('human', 'ai') NOT NULL DEFAULT 'human'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

DELIMITER $$

CREATE TRIGGER `after_inmueble_delete` AFTER DELETE ON `propiedades` FOR EACH ROW BEGIN
    DELETE FROM widget_inmuebles 
    WHERE 
        inmueble_id = OLD.clave_sistema;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `after_propiedades_insert` AFTER INSERT ON `propiedades` FOR EACH ROW BEGIN
    DECLARE v_equiv_mxp DECIMAL(10, 2);
    DECLARE v_estado_id INT;
    DECLARE v_ciudad_id INT;

    -- Obtener el valor de la variable monedas.equiv_mxp
    SELECT equiv_mxp INTO v_equiv_mxp FROM monedas WHERE siglas = NEW.moneda;

    -- Obtener el id de la ciudad y el estado
    SELECT id_ciudad INTO v_ciudad_id FROM new_colonias WHERE id = NEW.id_colonia;
    SELECT estado INTO v_estado_id FROM ciudades WHERE id = v_ciudad_id;

    -- No consideramos precio por metro ya que a este punto 
    -- no se ha creado su registro en la tabla de valores de campos
    INSERT INTO widget_inmuebles (contrato_id, public_key, inmueble_id, tipo, status_id, venta, renta, eventual, precio_venta, precio_renta, precio_eventual, moneda, estado_id, ciudad_id, colonia_id, sucursal_id, published_at, created_at, updated_at, precio_por_metro)
    VALUES (
        NEW.contrato,
        NEW.claveprop,
        NEW.clave_sistema, 
        NEW.tipo, 
        NEW.status_id, 
        CASE WHEN NEW.enventa = 'Si' THEN 1 ELSE 0 END,
        CASE WHEN NEW.enrenta = 'Si' THEN 1 ELSE 0 END,
        CASE WHEN NEW.endiaria = 'Si' THEN 1 ELSE 0 END,
        NEW.precio_venta * v_equiv_mxp,
        NEW.precio_renta * v_equiv_mxp,
        NEW.precio_diaria * v_equiv_mxp,
        NEW.moneda, 
        v_estado_id,
        v_ciudad_id,
        NEW.id_colonia,
        NEW.sucursal, 
        NOW(), 
        NEW.fecha_ingreso, 
        NEW.fecha_modificaciones,
        CASE 
            WHEN NEW.precio_por_metro = 'Si' THEN NEW.precio_venta * v_equiv_mxp
            ELSE NULL
        END
    );
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `after_propiedades_update_202503291734` AFTER UPDATE ON `propiedades` FOR EACH ROW BEGIN
    DECLARE v_equiv_mxp DECIMAL(10, 2);
    DECLARE v_estado_id INT;
    DECLARE v_ciudad_id INT;

    -- Obtener el valor de la variable monedas.equiv_mxp
    SELECT equiv_mxp INTO v_equiv_mxp FROM monedas WHERE siglas = NEW.moneda;

    -- Obtener el id de la ciudad y el estado
    SELECT id_ciudad INTO v_ciudad_id FROM new_colonias WHERE id = NEW.id_colonia;
    SELECT estado INTO v_estado_id FROM ciudades WHERE id = v_ciudad_id;

    -- Verificar si existe el registro en widget_inmuebles
    IF EXISTS (SELECT 1 FROM widget_inmuebles WHERE inmueble_id = NEW.clave_sistema) THEN
        -- Si existe, actualizar
        UPDATE widget_inmuebles
        SET contrato_id = NEW.contrato,
            public_key = NEW.claveprop,
            tipo = NEW.tipo,
            status_id = NEW.status_id,
            venta = CASE WHEN NEW.enventa = 'Si' THEN 1 ELSE 0 END,
            renta = CASE WHEN NEW.enrenta = 'Si' THEN 1 ELSE 0 END,
            eventual = CASE WHEN NEW.endiaria = 'Si' THEN 1 ELSE 0 END,
            precio_venta = NEW.precio_venta * v_equiv_mxp,
            precio_renta = NEW.precio_renta * v_equiv_mxp,
            precio_eventual = NEW.precio_diaria * v_equiv_mxp,
            moneda = NEW.moneda,
            estado_id = v_estado_id,
            ciudad_id = v_ciudad_id, 
            colonia_id = NEW.id_colonia,
            sucursal_id = NEW.sucursal,
            terreno_m2 = NULL,
            habitaciones = NULL,
            banos = NULL,
            construccion_m2 = NULL,
            updated_at = NEW.fecha_modificaciones,
            precio_por_metro = CASE 
                WHEN NEW.precio_por_metro = 'Si' THEN NEW.precio_venta * v_equiv_mxp
                ELSE NULL
            END
        WHERE inmueble_id = NEW.clave_sistema;
    ELSE
        -- Si no existe, insertar uno nuevo
        INSERT INTO widget_inmuebles (
            contrato_id, inmueble_id, public_key, tipo, status_id, 
            venta, renta, eventual, precio_venta, precio_renta, 
            precio_eventual, moneda, estado_id, ciudad_id, colonia_id, 
            sucursal_id, terreno_m2, habitaciones, banos, construccion_m2, 
            published_at, created_at, updated_at, precio_por_metro
        )
        VALUES (
            NEW.contrato,
            NEW.clave_sistema,
            CASE 
                WHEN NEW.claveprop IS NULL THEN CONCAT('P-', NEW.contrato, '-', NEW.clave_sistema)
                ELSE NEW.claveprop
            END,
            NEW.tipo,
            NEW.status_id,
            CASE WHEN NEW.enventa = 'Si' THEN 1 ELSE 0 END,
            CASE WHEN NEW.enrenta = 'Si' THEN 1 ELSE 0 END,
            CASE WHEN NEW.endiaria = 'Si' THEN 1 ELSE 0 END,
            NEW.precio_venta * v_equiv_mxp,
            NEW.precio_renta * v_equiv_mxp,
            NEW.precio_diaria * v_equiv_mxp,
            NEW.moneda,
            v_estado_id,
            v_ciudad_id,
            NEW.id_colonia,
            NEW.sucursal,
            NULL, -- terreno_m2
            NULL, -- habitaciones
            NULL, -- banos
            NULL, -- construccion_m2
            NOW(), -- published_at
            NEW.fecha_ingreso,
            NEW.fecha_modificaciones,
            CASE 
                WHEN NEW.precio_por_metro = 'Si' THEN NEW.precio_venta * v_equiv_mxp
                ELSE NULL
            END
        );
    END IF;
END
$$

DELIMITER;

CREATE TABLE `propiedades_edit` (
    `clave_sistema` int(9) NOT NULL,
    `tiempo` int(9) NOT NULL,
    `session_id` varchar(200) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `props_eliminadas` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `claveprop` varchar(15) NOT NULL DEFAULT '',
    `ingresado` date NOT NULL DEFAULT '0000-00-00',
    `modificado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `eliminado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `props_eliminadas_20240314` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `clave_sistema` int(15) NOT NULL DEFAULT 0,
    `claveprop` varchar(15) NOT NULL DEFAULT '',
    `ingresado` date NOT NULL DEFAULT '0000-00-00',
    `modificado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `eliminado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `prop_status` (
    `id` int(5) NOT NULL,
    `user_id` int(9) DEFAULT NULL,
    `status` varchar(23) NOT NULL,
    `view` text DEFAULT NULL,
    `listed` tinyint(1) NOT NULL DEFAULT 1,
    `mulbin_listed` tinyint(1) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `rel_prop_des` (
    `id` bigint(20) NOT NULL,
    `prop_clave_sistema` bigint(20) DEFAULT NULL,
    `des_clave_sistema` int(15) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `rel_prop_des_20231212` (
    `id` bigint(20) NOT NULL,
    `prop_clave_sistema` int(15) NOT NULL DEFAULT 0,
    `des_clave_sistema` int(15) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `reportes_visitas` (
    `llave` bigint(15) NOT NULL,
    `id` varchar(30) NOT NULL DEFAULT '',
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) DEFAULT NULL,
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `id_asesor` int(9) NOT NULL,
    `asesor` varchar(101) NOT NULL DEFAULT '',
    `id_visitante` int(7) NOT NULL DEFAULT 0,
    `visitante` varchar(101) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `observaciones` varchar(150) NOT NULL DEFAULT '',
    `completada` enum('Si', 'Pendiente') NOT NULL DEFAULT 'Pendiente',
    `aid` int(7) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `reportes_visitas_20231212` (
    `llave` bigint(15) NOT NULL,
    `id` varchar(30) NOT NULL DEFAULT '',
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` int(9) NOT NULL DEFAULT 0,
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `id_asesor` int(9) NOT NULL,
    `asesor` varchar(101) NOT NULL DEFAULT '',
    `id_visitante` int(7) NOT NULL DEFAULT 0,
    `visitante` varchar(101) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `observaciones` varchar(150) NOT NULL DEFAULT '',
    `completada` enum('Si', 'Pendiente') NOT NULL DEFAULT 'Pendiente',
    `aid` int(7) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas` (
    `id` int(3) NOT NULL,
    `usuario` varchar(15) NOT NULL DEFAULT '',
    `password` varchar(15) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(70) NOT NULL DEFAULT '',
    `txt01` text NOT NULL,
    `color_fondo_portal` varchar(15) NOT NULL DEFAULT '#F0F0F0'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_articulos_noticias` (
    `id` int(11) NOT NULL,
    `revista_id` int(3) NOT NULL DEFAULT 0,
    `titulo` varchar(50) NOT NULL DEFAULT '',
    `primer_parrafo` text NOT NULL,
    `contenido` text NOT NULL,
    `tipo` enum('noticia', 'articulo') NOT NULL DEFAULT 'noticia',
    `tema` varchar(30) NOT NULL DEFAULT '',
    `activada` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `en_principal` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_clasificaciones` (
    `id` int(7) NOT NULL,
    `revista_id` int(3) NOT NULL DEFAULT 0,
    `clasificacion` varchar(50) NOT NULL DEFAULT '',
    `area` varchar(30) NOT NULL DEFAULT 'OTROS'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_clasificados` (
    `id` int(5) NOT NULL,
    `revista_id` int(3) NOT NULL DEFAULT 0,
    `clasificacion` varchar(50) NOT NULL DEFAULT '0',
    `titulo` varchar(50) NOT NULL DEFAULT '',
    `contenido` text NOT NULL,
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `telefono` varchar(50) NOT NULL DEFAULT '',
    `email` varchar(70) NOT NULL DEFAULT '',
    `url` varchar(70) NOT NULL DEFAULT '',
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `origen` varchar(30) NOT NULL DEFAULT 'Formulario',
    `autorizado` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_registro` datetime NOT NULL,
    `fecha_autorizacion` datetime NOT NULL,
    `clave` varchar(100) NOT NULL,
    `plan` int(3) NOT NULL,
    `precio` varchar(35) NOT NULL,
    `pabusqueda` text NOT NULL,
    `status` enum('activo', 'inactivo') NOT NULL DEFAULT 'activo'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_dir_anunciantes` (
    `id` int(5) NOT NULL,
    `revista_id` int(5) NOT NULL DEFAULT 0,
    `seccion_id` int(5) NOT NULL DEFAULT 0,
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `descripcion` text NOT NULL,
    `domicilio` varchar(250) NOT NULL DEFAULT '',
    `telefono` varchar(35) NOT NULL DEFAULT '',
    `sitio_web` varchar(60) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `usuario_si` varchar(25) NOT NULL DEFAULT '',
    `status` enum('activo', 'inactivo') NOT NULL DEFAULT 'activo'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_dir_secciones` (
    `id` int(5) NOT NULL,
    `revista_id` int(5) NOT NULL DEFAULT 0,
    `nombre` varchar(35) NOT NULL DEFAULT '',
    `area` varchar(35) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_paginas_graficas` (
    `id` bigint(12) NOT NULL,
    `revista_id` int(3) NOT NULL,
    `orden` int(4) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_planes` (
    `id` int(5) NOT NULL,
    `revista_id` int(2) NOT NULL,
    `plan` varchar(35) NOT NULL,
    `num_fotos` int(2) NOT NULL,
    `precio` double(7, 2) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `revistas_usuarios` (
    `revista` int(3) NOT NULL DEFAULT 0,
    `contrato` int(7) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `sucursales` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `calle_numero` varchar(70) NOT NULL DEFAULT '',
    `colonia` varchar(30) NOT NULL DEFAULT '',
    `codigo_postal` int(5) NOT NULL DEFAULT 0,
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `phone_numbers` text DEFAULT NULL,
    `fax` varchar(15) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `como_llegar` text NOT NULL,
    `slug` varchar(50) DEFAULT NULL,
    `matriz` tinyint(1) NOT NULL DEFAULT 0,
    `orden` int(3) NOT NULL DEFAULT 999,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `table_fields` (
    `id` bigint(20) NOT NULL,
    `contrato_id` int(9) DEFAULT NULL,
    `tabla` varchar(35) NOT NULL,
    `campo` varchar(100) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `table_field_values` (
    `id` bigint(20) NOT NULL,
    `contrato_id` int(9) DEFAULT NULL,
    `table_field_id` bigint(20) NOT NULL,
    `valor` text NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tags` (
    `id` bigint(20) NOT NULL,
    `id_string` varchar(43) NOT NULL COMMENT 'Sin acentos, sin caract. esp. y en minúsculas',
    `user_id` int(9) DEFAULT NULL,
    `name` text NOT NULL COMMENT 'Puede ser String o JSON en el caso de tags multidiomas con user_id NULL'
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tags_pivot` (
    `id` bigint(20) NOT NULL,
    `tag_id` bigint(20) NOT NULL,
    `prop_id` bigint(20) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tipo_inmuebles` (
    `clave` int(2) UNSIGNED ZEROFILL NOT NULL DEFAULT 00,
    `id_string` varchar(23) NOT NULL,
    `ES` text NOT NULL,
    `EN` text NOT NULL,
    `tipo` text NOT NULL,
    `p_tipo` text NOT NULL,
    `tipo_esp` varchar(30) NOT NULL DEFAULT '',
    `tipo_ing` varchar(30) NOT NULL DEFAULT '',
    `tipo_fra` varchar(30) NOT NULL DEFAULT '',
    `p_tipo_esp` varchar(30) NOT NULL DEFAULT '',
    `p_tipo_ing` varchar(30) NOT NULL DEFAULT '',
    `p_tipo_fra` varchar(30) NOT NULL DEFAULT '',
    `visitas` bigint(11) NOT NULL,
    `slug` text DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tipo_inm_pivot` (
    `tipo_id` int(2) UNSIGNED ZEROFILL NOT NULL,
    `prop_id` bigint(20) NOT NULL,
    `priority` int(1) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tips` (
    `id` int(3) NOT NULL,
    `tip` text NOT NULL,
    `link` varchar(100) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `tmp001` (
    `clave_sistema_ori` int(7) NOT NULL,
    `clave_sistema_cop` int(7) NOT NULL,
    `claveprop` varchar(50) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `txt_imgs` (
    `clave` varchar(100) NOT NULL DEFAULT '',
    `palabra` varchar(50) NOT NULL DEFAULT '',
    `ip` varchar(15) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `users` (
    `id` mediumint(7) NOT NULL,
    `contrato` mediumint(7) NOT NULL,
    `type` enum(
        'asesor',
        'admin',
        'super',
        'god'
    ) NOT NULL DEFAULT 'asesor',
    `user` varchar(16) DEFAULT NULL,
    `pass` varchar(50) NOT NULL,
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `privileges` text NOT NULL,
    `mig_from` varchar(12) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `valores_campos` (
    `id` bigint(20) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) NOT NULL,
    `variable_id` int(9) NOT NULL,
    `variable` varchar(25) NOT NULL DEFAULT '',
    `valor` mediumtext DEFAULT NULL,
    `valor_esp` mediumtext DEFAULT NULL,
    `valor_ing` mediumtext DEFAULT NULL,
    `valor_fra` mediumtext DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

DELIMITER $$

CREATE TRIGGER `250330_valores_campos_banos_after_insert` AFTER INSERT ON `valores_campos` FOR EACH ROW BEGIN
    -- Verificar si es un registro de banos (variable_id = 12), SIEMPRE trae un valor en valor_esp
    IF NEW.variable_id = 12 THEN
        UPDATE widget_inmuebles
        SET 
            banos = NEW.valor_esp,
            updated_at = NOW()
        WHERE 
            inmueble_id = NEW.clave_sistema;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250331_valores_campos_banos_before_insert` BEFORE INSERT ON `valores_campos` FOR EACH ROW BEGIN
    DECLARE v_valor_limpio_12 VARCHAR(10);
    DECLARE v_mensaje_error_12 VARCHAR(255);
    DECLARE v_es_valido_12 BOOLEAN DEFAULT TRUE;
    
    IF NEW.variable_id = 12 THEN
        -- Primero limpiamos el valor
        SET v_valor_limpio_12 = TRIM(REGEXP_REPLACE(REGEXP_REPLACE(NEW.valor_esp, '[^0-9½\./\s]', ''), '\s+', ' '));
        
        -- Convertimos valores como "3.5" o "3 1/2" a "3½"
        IF v_valor_limpio_12 REGEXP '^[0-9]+\.5$' THEN
            SET v_valor_limpio_12 = REGEXP_REPLACE(v_valor_limpio_12, '([0-9]+)\.5', '\1½');
        ELSEIF v_valor_limpio_12 REGEXP '^[0-9]+ 1/2$' THEN
            SET v_valor_limpio_12 = REGEXP_REPLACE(v_valor_limpio_12, '([0-9]+) 1/2', '\1½');
        END IF;
        -- Quito los espacios
        SET v_valor_limpio_12 = REGEXP_REPLACE(v_valor_limpio_12, '\s+', '');
        
        -- Verificamos que solo contenga números enteros y ½
        IF NOT v_valor_limpio_12 REGEXP '^[0-9]+½?$' THEN
            SET v_es_valido_12 = FALSE;
            
            -- -- Elimino de 'valores_campos_aprendidos' el registro que tiene NEW.valor_esp y NEW.variable
            -- DELETE FROM valores_campos_aprendidos
            -- WHERE 
            --     contrato = NEW.contrato AND 
            --     valor_esp = NEW.valor_esp AND 
            --     variable = NEW.variable
            -- LIMIT 1;

            -- -- Genero un error para que se pueda ver en el log de MySQL
            -- SET v_mensaje_error_12 = 'Error: valor_esp para baños debe ser un número entero o terminar con ½, intentaste insertar el valor: ';
            -- SET v_mensaje_error_12 = CONCAT(v_mensaje_error_12, NEW.valor_esp, ' : ', NEW.variable, ' : ', NEW.contrato);
            
            -- -- Ahora lanzamos el error después de haber ejecutado el DELETE
            -- SIGNAL SQLSTATE '45000' 
            -- SET MESSAGE_TEXT = v_mensaje_error_12;
        END IF;

        -- SELECT * FROM valores_campos_aprendidos WHERE (contrato=33 OR contrato IS NULL) AND variable = 'ci_banos' AND valor_esp NOT REGEXP '^[0-9½[:space:]]+$'

        -- Borramos de valores_campos_aprendidos los valores que no tengan tengan caracteres no válidos, o sea que solo validan números enteros y ½ y espacios en blanco
        DELETE FROM valores_campos_aprendidos
        WHERE contrato=NEW.contrato
            AND variable = 'ci_banos'
            AND valor_esp NOT REGEXP '^[0-9½[:space:]]+$';

        -- Borramos de valores_campos_aprendidos los valores que no tengan tengan caracteres no válidos, o sea que solo validan números enteros y ½ y espacios en blanco

        IF v_es_valido_12 THEN
            SET NEW.valor_esp = v_valor_limpio_12;
        ELSE
            SET NEW.valor_esp = NULL;
        END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250331_valores_campos_construccion_after_insert` AFTER INSERT ON `valores_campos` FOR EACH ROW BEGIN
    -- Declaración de todas las variables al inicio
    DECLARE v_construccion_m2_cai DECIMAL(10, 2);

    -- Si el registro es de construccion_m2 (variable_id = 2) se actualiza
    IF NEW.variable_id = 2 THEN
        -- Ya no necesitamos limpiar el valor_esp porque el trigger BEFORE ya lo hizo
        -- Simplemente convertimos el valor ya limpio
        SET v_construccion_m2_cai = CAST(NEW.valor_esp AS DECIMAL(10,2));

        -- Si hay un valor en construccion_m2 se actualiza
        IF v_construccion_m2_cai > 0 THEN
            UPDATE widget_inmuebles
            SET 
                construccion_m2 = v_construccion_m2_cai,
                updated_at = NOW()
            WHERE 
                inmueble_id = NEW.clave_sistema;
        -- Si el construccion_m2 es 0 o NULL se actualiza a NULL
        ELSE
            UPDATE widget_inmuebles
            SET 
                construccion_m2 = NULL,
                updated_at = NOW()
            WHERE 
                inmueble_id = NEW.clave_sistema;
        END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250331_valores_campos_construccion_before_insert` BEFORE INSERT ON `valores_campos` FOR EACH ROW BEGIN
    -- Declarar todas las variables al inicio
    DECLARE v_valor_limpio_cbi DECIMAL(10,2);
    DECLARE v_es_numero_cbi BOOLEAN;
    
    -- Solo validar si es un registro de construccion_m2
    IF NEW.variable_id = 2 THEN
        -- Inicializar variables (no declarar)
        SET v_es_numero_cbi = TRUE;
        
        BEGIN
            -- Usar manejo de errores para detectar conversiones inválidas
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
            BEGIN
                SET v_es_numero_cbi = FALSE;
            END;
            
            -- Limpiar el valor y convertirlo
            SET v_valor_limpio_cbi = CAST(REGEXP_REPLACE(REGEXP_REPLACE(NEW.valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9.]', '') AS DECIMAL(10,2));
        END;
        
        -- Si es un número válido, formatear el valor correctamente
        IF v_es_numero_cbi THEN
            SET NEW.valor_esp = CAST(v_valor_limpio_cbi AS CHAR);
        ELSE
            -- Si no es un número válido, puedes:
            -- 1. Rechazar la inserción:
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Error: valor_esp debe ser un número decimal válido para construccion';
            
            -- 2. O establecer un valor predeterminado:
            -- SET NEW.valor_esp = '0.00';
        END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250331_valores_campos_terreno_after_insert` AFTER INSERT ON `valores_campos` FOR EACH ROW BEGIN
    DECLARE v_precio_por_metro_tai VARCHAR(2);
    DECLARE v_precio_venta_tai DECIMAL(10, 2);
    DECLARE v_moneda_tai VARCHAR(5);
    DECLARE v_equiv_mxp_tai DECIMAL(10, 2);
    DECLARE v_terreno_m2_tai DECIMAL(10, 2);
    DECLARE v_precio_total_venta_tai DECIMAL(16, 2);

    -- Si el registro es de terreno_m2 se actualiza
    IF NEW.variable_id = 1 THEN
        -- Ya no necesitamos limpiar el valor_esp porque el trigger BEFORE ya lo hizo
        -- Simplemente convertimos el valor ya limpio
        SET v_terreno_m2_tai = CAST(NEW.valor_esp AS DECIMAL(10,2));

        -- Obtener datos de la propiedad
        SELECT precio_por_metro, precio_venta, moneda
        INTO v_precio_por_metro_tai, v_precio_venta_tai, v_moneda_tai
        FROM propiedades 
        WHERE clave_sistema = NEW.clave_sistema;
        
        -- Obtener el valor de equivalencia a MXP
        SELECT equiv_mxp INTO v_equiv_mxp_tai
        FROM monedas 
        WHERE siglas = v_moneda_tai;
        
        -- Si el el precio es por metro y el terreno_m2 es mayor a 0 se actualiza
        IF v_precio_por_metro_tai = 'Si' AND v_precio_venta_tai > 0 AND v_equiv_mxp_tai > 0 AND v_terreno_m2_tai > 0 THEN
            -- Calcular el precio por metro cuadrado
            SET v_precio_total_venta_tai = v_precio_venta_tai * v_terreno_m2_tai * v_equiv_mxp_tai;

            UPDATE widget_inmuebles
            SET 
                -- Si el precio total es mayor a 99999999999 se tomará v_precio_venta_tai
                precio_venta = CASE 
                    WHEN v_precio_total_venta_tai > 99999999999 THEN v_precio_venta_tai
                    ELSE v_precio_total_venta_tai
                END,
                -- Y el precio por metro cuadrado será el precio de venta
                precio_por_metro = v_precio_venta_tai * v_equiv_mxp_tai,
                terreno_m2 = v_terreno_m2_tai,
                updated_at = NOW()
            WHERE 
                inmueble_id = NEW.clave_sistema;

        -- Si hay un valor en terreno_m2 se actualiza
        ELSEIF v_terreno_m2_tai > 0 THEN
            UPDATE widget_inmuebles
            SET 
                terreno_m2 = v_terreno_m2_tai,
                updated_at = NOW()
            WHERE 
                inmueble_id = NEW.clave_sistema;

        -- Si el terreno_m2 es 0 o NULL se actualiza a NULL
        ELSE
            UPDATE widget_inmuebles
            SET 
                terreno_m2 = NULL,
                updated_at = NOW()
            WHERE 
                inmueble_id = NEW.clave_sistema;
        END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250331_valores_campos_terreno_before_insert` BEFORE INSERT ON `valores_campos` FOR EACH ROW BEGIN
    -- Declarar todas las variables al inicio
    DECLARE v_valor_limpio_tbi DECIMAL(10,2);
    DECLARE v_es_numero_tbi BOOLEAN;
    
    -- Solo validar si es un registro de terreno_m2
    IF NEW.variable_id = 1 THEN
        -- Inicializar variables (no declarar)
        SET v_es_numero_tbi = TRUE;
        
        BEGIN
            -- Usar manejo de errores para detectar conversiones inválidas
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
            BEGIN
                SET v_es_numero_tbi = FALSE;
            END;
            
            -- Limpiar el valor y convertirlo
            SET v_valor_limpio_tbi = CAST(REGEXP_REPLACE(REGEXP_REPLACE(NEW.valor_esp, '([0-9]*\.[0-9]*).*', '\1'), '[^0-9.]', '') AS DECIMAL(10,2));
        END;
        
        -- Si es un número válido, formatear el valor correctamente
        IF v_es_numero_tbi THEN
            SET NEW.valor_esp = CAST(v_valor_limpio_tbi AS CHAR);
        ELSE
            -- Si no es un número válido, puedes:
            -- 1. Rechazar la inserción:
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Error: valor_esp debe ser un número decimal válido para terrenos';
            
            -- 2. O establecer un valor predeterminado:
            -- SET NEW.valor_esp = '0.00';
        END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250401_valores_campos_habitaciones_after_insert` AFTER INSERT ON `valores_campos` FOR EACH ROW BEGIN
    -- Verificar si es un registro de habitaciones (variable_id = 11)
    IF NEW.variable_id = 11 THEN
        -- El trigger before ya ha limpiado y validado el valor
        -- Simplemente actualizamos widget_inmuebles con el valor ya procesado
        UPDATE widget_inmuebles
        SET 
            habitaciones = NEW.valor_esp,
            updated_at = NOW()
        WHERE 
            inmueble_id = NEW.clave_sistema;

        -- IF NEW.valor_esp IS NULL THEN
        --     DELETE FROM valores_campos
        --     WHERE contrato = NEW.contrato
        --         AND clave_sistema = NEW.clave_sistema
        --         AND variable_id = 11;
        -- END IF;
    END IF;
END
$$

DELIMITER;

DELIMITER $$

CREATE TRIGGER `250401_valores_campos_habitaciones_before_insert` BEFORE INSERT ON `valores_campos` FOR EACH ROW BEGIN
    DECLARE v_valor_limpio_11 VARCHAR(10);
    
    IF NEW.variable_id = 11 THEN
        -- Limpiamos el valor, solo permitimos números
        SET v_valor_limpio_11 = REGEXP_REPLACE(NEW.valor_esp, '[^0-9]', '');
        
        -- Borramos de valores_campos_aprendidos los valores que no sean números enteros
        DELETE FROM valores_campos_aprendidos
        WHERE contrato=NEW.contrato
            AND variable = 'ci_recamaras'
            AND valor_esp NOT REGEXP '^[0-9]+$';

        -- Si hay un valor válido lo asignamos, de lo contrario NULL
        IF v_valor_limpio_11 != '' THEN
            SET NEW.valor_esp = v_valor_limpio_11;
        ELSE
            SET NEW.valor_esp = NULL;
        END IF;
    END IF;
END
$$

DELIMITER;

CREATE TABLE `valores_campos_aprendidos` (
    `id` int(10) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `variable_id` int(9) DEFAULT NULL,
    `variable` varchar(35) NOT NULL DEFAULT '',
    `valor_esp` varchar(250) NOT NULL DEFAULT '',
    `valor_ing` varchar(250) NOT NULL DEFAULT '',
    `valor_fra` varchar(250) NOT NULL DEFAULT '',
    `para_impresion` enum('Si', 'No') NOT NULL DEFAULT 'No'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `visitas_propiedades` (
    `id` int(15) NOT NULL,
    `contrato` int(5) NOT NULL,
    `clave_sistema` int(7) NOT NULL,
    `fecha_hora` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `ip` varchar(30) NOT NULL,
    `dominio` varchar(150) NOT NULL,
    `detalles` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `widget_inmuebles` (
    `id` bigint(20) NOT NULL,
    `contrato_id` int(9) NOT NULL COMMENT 'Contrato que promueve el inmueble',
    `inmueble_id` bigint(20) NOT NULL COMMENT 'Inmueble relacionado',
    `public_key` varchar(50) DEFAULT NULL COMMENT 'Clave con la que se promueve el inmueble en la web del contrato_id',
    `tipo` int(2) UNSIGNED ZEROFILL NOT NULL COMMENT 'Tipo de inmueble (ej. Casa, Terreno, Departamento, Edificio, etc.)',
    `status_id` int(5) NOT NULL DEFAULT 1 COMMENT 'Estado actual del inmueble',
    `venta` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'El inmueble se promueve para Venta',
    `renta` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'El inmueble se promueve para Renta',
    `eventual` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'El inmueble se promueve eventualmente',
    `precio_venta` bigint(16) NOT NULL COMMENT 'Precio de venta del inmueble para límites de búsqueda en MXP',
    `precio_renta` bigint(16) NOT NULL COMMENT 'Precio de renta del inmueble en MXP',
    `precio_eventual` bigint(16) NOT NULL COMMENT 'Precio eventual del inmueble en MXP',
    `precio_por_metro` bigint(16) DEFAULT NULL COMMENT 'Precio por metro cuadrado del inmueble en MXP',
    `moneda` char(3) NOT NULL DEFAULT 'MXP' COMMENT 'Moneda en la que se ofrece el inmueble (ej. MXP, USD)',
    `estado_id` int(5) NOT NULL,
    `ciudad_id` int(5) NOT NULL,
    `colonia_id` int(10) NOT NULL,
    `sucursal_id` int(7) DEFAULT NULL COMMENT 'Sucursal inmobiliaria asociada al inmueble',
    `terreno_m2` decimal(10, 2) DEFAULT NULL COMMENT 'Metros cuadrados de terreno',
    `construccion_m2` decimal(10, 2) DEFAULT NULL COMMENT 'Metros cuadrados de construcción',
    `habitaciones` mediumint(3) UNSIGNED DEFAULT NULL COMMENT 'Número de habitaciones',
    `banos` varchar(15) DEFAULT NULL COMMENT 'Número de baños',
    `published_at` timestamp NULL DEFAULT current_timestamp() COMMENT 'TS en que se publica en el contrato de esta tabla',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Fecha en la que se publicó el inmueble',
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Última actualización del inmueble'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Tabla optimizada para el widget de búsqueda de inmuebles';

CREATE TABLE `zonas` (
    `id` int(7) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `id_ciudad` int(5) NOT NULL DEFAULT 0,
    `zona` mediumtext DEFAULT NULL,
    `zona_esp` varchar(30) NOT NULL DEFAULT '',
    `zona_ing` varchar(30) NOT NULL DEFAULT '',
    `zona_fra` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(70) NOT NULL DEFAULT '',
    `ciudad` varchar(70) NOT NULL DEFAULT '',
    `mapa` int(5) NOT NULL DEFAULT 0
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

ALTER TABLE `ampi_articulos_noticias` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_comercializacion` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_encuestas` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_encuestas_respuestas` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_funciones` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_nucal_categories` ADD KEY `id` (`id`);

ALTER TABLE `ampi_nucal_events` ADD KEY `id` (`id`);

ALTER TABLE `ampi_secciones` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_socios_externos` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_tmp_errores_calendario` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_usuarios`
ADD PRIMARY KEY (`id`),
ADD KEY `ampi_usuarios_contrato_config_foreign` (`contrato`);

ALTER TABLE `ampi_usuarios_20240316` ADD PRIMARY KEY (`id`);

ALTER TABLE `antispam` ADD PRIMARY KEY (`ip`);

ALTER TABLE `api_google` ADD PRIMARY KEY (`dominio`);

ALTER TABLE `api_tokens`
ADD PRIMARY KEY (`id`),
ADD KEY `api_tokens_config_foreign` (`contrato_id`);

ALTER TABLE `asesores`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unique_user` (`contrato`, `usuario`),
ADD KEY `asesores_sucursales` (`sucursal_id`);

ALTER TABLE `asesor_cliente`
ADD PRIMARY KEY (`id`),
ADD KEY `asesor_cliente_contrato_config_foreign` (`contrato`),
ADD KEY `asesor_cliente_asesor_id_asesores_foreign` (`asesor_id`);

ALTER TABLE `avisos_bolsa` ADD PRIMARY KEY (`id`);

ALTER TABLE `avisos_respuestas` ADD PRIMARY KEY (`id`);

ALTER TABLE `bolsa_compradores`
ADD PRIMARY KEY (`id`),
ADD KEY `bolsa_compradores_contrato_config_foreign` (`contrato`),
ADD KEY `bolsa_compradores_asesor_asesores_foreign` (`asesor`);

ALTER TABLE `bolsa_compradores_r`
ADD PRIMARY KEY (`id`),
ADD KEY `bolsa_compradores_r_rid_bolsa_compradores_foreign` (`rid`),
ADD KEY `bolsa_compradores_r_contrato_config_foreign` (`contrato`),
ADD KEY `bolsa_compradores_r_asesor_asesores_foreign` (`asesor`);

ALTER TABLE `bolsa_grupos` ADD PRIMARY KEY (`id`);

ALTER TABLE `bolsa_grupos_rel` ADD PRIMARY KEY (`id`);

ALTER TABLE `bolsa_inmobiliaria`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `contratos` (
    `contrato_solicitante`,
    `contrato_solicitado`
) USING BTREE,
ADD KEY `bolsa_inmobiliaria_contrato_solicitado_foreign` (`contrato_solicitado`);

ALTER TABLE `busca_historial` ADD PRIMARY KEY (`tiempo`);

ALTER TABLE `busca_propiedades`
ADD PRIMARY KEY (`clave_sistema`),
ADD KEY `contrato` (`contrato`);

ALTER TABLE `campos_bloqueados` ADD KEY `contrato` (`contrato`);

ALTER TABLE `campos_inhabilitados`
ADD PRIMARY KEY (`contrato`, `variable`),
ADD KEY `contrato` (`contrato`);

ALTER TABLE `campos_inmuebles`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `contrato_variable` (`contrato`, `variable`),
ADD KEY `contrato` (`contrato`),
ADD KEY `campos_inmuebles_campos_inmuebles_grupo` (`grupo_id`);

ALTER TABLE `campos_inmuebles_grupos`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `ids_contrato` (`ids`, `contrato`),
ADD KEY `campos_inmuebles_grupos_config` (`contrato`);

ALTER TABLE `casas_compartidas`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `clave_contrato` (
    `clave_sistema`,
    `contrato`,
    `autorizado`
),
ADD KEY `clave_sistema` (`clave_sistema`),
ADD KEY `autorizado` (`autorizado`),
ADD KEY `contrato` (`contrato`),
ADD KEY `casas_compartidas_quien_solicita_config_foreign` (`quien_solicita`);

ALTER TABLE `citas`
ADD PRIMARY KEY (`id`),
ADD KEY `citas_propiedades` (`clave_sistema`);

ALTER TABLE `citas_20240319`
ADD PRIMARY KEY (`id`),
ADD KEY `citas_propiedades` (`clave_sistema`);

ALTER TABLE `ciudades`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `slug` (`slug`),
ADD KEY `ciudad` (`ciudad`),
ADD KEY `estado` (`estado`) USING BTREE;

ALTER TABLE `colonias` ADD PRIMARY KEY (`id`);

ALTER TABLE `componentes` ADD PRIMARY KEY (`id`);

ALTER TABLE `componentes_campos` ADD PRIMARY KEY (`id`);

ALTER TABLE `componentes_usuarios` ADD PRIMARY KEY (`id`);

ALTER TABLE `componentes_valores` ADD PRIMARY KEY (`id`);

ALTER TABLE `config`
ADD PRIMARY KEY (`contrato`) USING BTREE,
ADD UNIQUE KEY `dominio` (`dominio`),
ADD UNIQUE KEY `usuario` (`usuario`) USING BTREE,
ADD UNIQUE KEY `deptID_livephp` (`deptID_livephp`),
ADD UNIQUE KEY `theme_hash` (`theme_hash`),
ADD KEY `dominio2` (`dominio2`),
ADD KEY `activos` (
    `status`,
    `pagado_hasta`,
    `prorroga`
),
ADD KEY `status` (`status`);

ALTER TABLE `config_fotos` ADD PRIMARY KEY (`contrato`);

ALTER TABLE `config_mulbin`
ADD PRIMARY KEY (`id`),
ADD KEY `cnt` (`contrato`);

ALTER TABLE `contactos` ADD PRIMARY KEY (`id`);

ALTER TABLE `contactos_etq` ADD PRIMARY KEY (`id`);

ALTER TABLE `contactos_etq_rel` ADD KEY `etiqueta` (`etiqueta`);

ALTER TABLE `contenidos` ADD PRIMARY KEY (`id`);

ALTER TABLE `desarrollos`
ADD PRIMARY KEY (`clave_sistema`),
ADD KEY `desarrollos_config` (`contrato`);

ALTER TABLE `desarrollos_20240316`
ADD PRIMARY KEY (`clave_sistema`),
ADD KEY `desarrollos_config` (`contrato`);

ALTER TABLE `desarrollos_compartidos`
ADD PRIMARY KEY (`clave_sistema`, `contrato`),
ADD KEY `desarrollos_compartidos_contrato_config_foreign` (`contrato`);

ALTER TABLE `desarrollos_etq` ADD PRIMARY KEY (`id`);

ALTER TABLE `desarrollos_fotos`
ADD PRIMARY KEY (`id`),
ADD KEY `desarrollos_fotos_clave_sistema_desarrollos_foreign` (`clave_sistema`);

ALTER TABLE `des_rel_etq` ADD KEY `etiqueta` (`etiqueta`);

ALTER TABLE `des_v_d`
ADD PRIMARY KEY (`id`),
ADD KEY `des_v_d_desarrollo_desarrollos_foreign` (`desarrollo`);

ALTER TABLE `des_v_d_20240317` ADD PRIMARY KEY (`id`);

ALTER TABLE `directorio_inmobiliario`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `email` (`email`),
ADD KEY `ids` (`ids`),
ADD KEY `directorio_inmobiliario_contrato_id` (`contrato_id`);

ALTER TABLE `documentos`
ADD PRIMARY KEY (`id`),
ADD KEY `documentos_propiedades` (`clave_sistema`);

ALTER TABLE `encuestas` ADD PRIMARY KEY (`id`);

ALTER TABLE `encuestas_r` ADD PRIMARY KEY (`id`);

ALTER TABLE `estados`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `short` (`pais`, `short`) USING BTREE,
ADD UNIQUE KEY `slug` (`slug`);

ALTER TABLE `fav_historial`
ADD PRIMARY KEY (`clave_sistema`, `contrato`),
ADD KEY `fav_historial_contrato_config_foreign` (`contrato`);

ALTER TABLE `files`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unique_resource` (`contrato_id`, `type`, `tag`);

ALTER TABLE `f_des_fotos` ADD PRIMARY KEY (`id`);

ALTER TABLE `f_des_panoramicas` ADD PRIMARY KEY (`id`);

ALTER TABLE `f_des_planos` ADD PRIMARY KEY (`id`);

ALTER TABLE `f_fotos`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`),
ADD KEY `contrato` (`contrato`);

ALTER TABLE `f_fotos_20240317`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`),
ADD KEY `contrato` (`contrato`);

ALTER TABLE `f_panoramicas`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`),
ADD KEY `f_panoramicas_contrato_config_foreign` (`contrato`);

ALTER TABLE `f_panoramicas_20240317`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`);

ALTER TABLE `gpt`
ADD PRIMARY KEY (`id`),
ADD KEY `gpt_contrato` (`contrato`);

ALTER TABLE `ips_busca` ADD PRIMARY KEY (`id`);

ALTER TABLE `languages`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `code` (`code`);

ALTER TABLE `lateral` ADD PRIMARY KEY (`contrato`);

ALTER TABLE `lateral_secciones`
ADD PRIMARY KEY (`id`),
ADD KEY `lateral_secciones_config_foreign` (`contrato`);

ALTER TABLE `lateral_secciones_20231129` ADD PRIMARY KEY (`id`);

ALTER TABLE `lateral_secciones_20250203` ADD PRIMARY KEY (`id`);

ALTER TABLE `links`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unique_link` (
    `contrato`,
    `subdomain`,
    `menu`,
    `link`
) USING BTREE;

ALTER TABLE `machotes_clausulas` ADD PRIMARY KEY (`id`);

ALTER TABLE `monedas`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `siglas` (`siglas`);

ALTER TABLE `movpropiedades` ADD PRIMARY KEY (`id`);

ALTER TABLE `mov_propiedades` ADD PRIMARY KEY (`id`);

ALTER TABLE `new_colonias`
ADD PRIMARY KEY (`id`),
ADD KEY `id_ciudad` (`id_ciudad`),
ADD KEY `new_colonias_config_foreign` (`contrato`);

ALTER TABLE `paises`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `short` (`short`);

ALTER TABLE `panoramicas`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`);

ALTER TABLE `preguntas`
ADD PRIMARY KEY (`numero`),
ADD KEY `preguntas_propiedades` (`clave_sistema`),
ADD KEY `preguntas_cc_asesor_foreign` (`cc_asesor`),
ADD KEY `preguntas_asesor_foreign` (`asesor`);

ALTER TABLE `preguntas_20250114`
ADD PRIMARY KEY (`numero`),
ADD KEY `preguntas_propiedades` (`clave_sistema`);

ALTER TABLE `propiedades`
ADD PRIMARY KEY (`clave_sistema`),
ADD UNIQUE KEY `key_str` (`key_str`),
ADD KEY `contrato` (`contrato`),
ADD KEY `tipo` (`tipo`),
ADD KEY `colonia` (`colonia`),
ADD KEY `asesor` (`asesor`),
ADD KEY `propiedades_prop_status` (`status_id`),
ADD KEY `cmp_vta` (
    `comparto_comision`,
    `enventa`
),
ADD KEY `cmp_rta` (
    `rta_comparto_comision`,
    `enrenta`
),
ADD KEY `id_op` (
    `clave_sistema`,
    `operacion_hecha`
),
ADD KEY `idx_owner` (
    `clave_sistema`,
    `contrato`,
    `status_id`,
    `fecha_expiracion`
),
ADD KEY `idx_venta` (`enventa`, `precio_venta`),
ADD KEY `idx_renta` (`enrenta`, `precio_renta`),
ADD KEY `idx_diaria` (`endiaria`, `precio_diaria`),
ADD KEY `idx_partners` (
    `clave_sistema`,
    `contrato`,
    `status_id`
),
ADD KEY `propiedades_index` (
    `clave_sistema`,
    `contrato`,
    `operacion_hecha`,
    `comparto_comision`,
    `rta_comparto_comision`,
    `enventa`,
    `enrenta`,
    `endiaria`,
    `status_web`,
    `fecha_expiracion`,
    `precio_venta`,
    `precio_renta`,
    `precio_diaria`
),
ADD KEY `propiedades_sucursales_foreign` (`sucursal`),
ADD KEY `propiedades_new_colonias_foreign` (`id_colonia`);

ALTER TABLE `propiedades_edit` ADD PRIMARY KEY (`clave_sistema`);

ALTER TABLE `props_eliminadas` ADD PRIMARY KEY (`id`);

ALTER TABLE `props_eliminadas_20240314` ADD PRIMARY KEY (`id`);

ALTER TABLE `prop_status`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unicos` (`user_id`, `status`);

ALTER TABLE `rel_prop_des`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `prop_des_unique` (
    `prop_clave_sistema`,
    `des_clave_sistema`
),
ADD KEY `rel_prop_des_desarrollos` (`des_clave_sistema`);

ALTER TABLE `rel_prop_des_20231212` ADD PRIMARY KEY (`id`);

ALTER TABLE `reportes_visitas`
ADD PRIMARY KEY (`llave`),
ADD KEY `clave_sistema` (`clave_sistema`);

ALTER TABLE `reportes_visitas_20231212`
ADD PRIMARY KEY (`llave`),
ADD KEY `clave_sistema` (`clave_sistema`);

ALTER TABLE `revistas` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_articulos_noticias` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_clasificaciones` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_clasificados` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_dir_anunciantes` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_dir_secciones` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_paginas_graficas` ADD PRIMARY KEY (`id`);

ALTER TABLE `revistas_planes` ADD PRIMARY KEY (`id`);

ALTER TABLE `sucursales`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `slugs` (`contrato`, `slug`);

ALTER TABLE `table_fields`
ADD PRIMARY KEY (`id`),
ADD KEY `table_fields_config_foreign` (`contrato_id`);

ALTER TABLE `table_field_values` ADD PRIMARY KEY (`id`);

ALTER TABLE `tags`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unicos` (`id_string`, `user_id`),
ADD KEY `tags_user_id` (`user_id`);

ALTER TABLE `tags_pivot`
ADD PRIMARY KEY (`id`),
ADD KEY `tag_tags` (`tag_id`),
ADD KEY `prop_props` (`prop_id`);

ALTER TABLE `tipo_inmuebles`
ADD PRIMARY KEY (`clave`),
ADD UNIQUE KEY `id_string` (`id_string`),
ADD KEY `p_tipo_esp` (`p_tipo_esp`);

ALTER TABLE `tipo_inm_pivot`
ADD PRIMARY KEY (`tipo_id`, `prop_id`),
ADD KEY `prop_id` (`prop_id`),
ADD KEY `tipo_id` (`tipo_id`);

ALTER TABLE `tips` ADD PRIMARY KEY (`id`);

ALTER TABLE `users` ADD PRIMARY KEY (`id`);

ALTER TABLE `valores_campos`
ADD PRIMARY KEY (`id`),
ADD KEY `clave_sistema` (`clave_sistema`),
ADD KEY `contrato` (`contrato`),
ADD KEY `variable` (`variable`),
ADD KEY `valores_campos_variable_id_campos_inmuebles_foreign` (`variable_id`);

ALTER TABLE `valores_campos_aprendidos`
ADD PRIMARY KEY (`id`),
ADD KEY `variable` (`variable`),
ADD KEY `contrato` (`contrato`),
ADD KEY `variable_id` (`variable_id`);

ALTER TABLE `visitas_propiedades` ADD PRIMARY KEY (`id`);

ALTER TABLE `widget_inmuebles`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `unique_registry` (`contrato_id`, `inmueble_id`),
ADD KEY `idx_contrato` (`contrato_id`),
ADD KEY `idx_estado` (`status_id`),
ADD KEY `idx_precio` (`precio_venta`),
ADD KEY `idx_sucursal` (`sucursal_id`),
ADD KEY `idx_tipo` (`tipo`),
ADD KEY `idx_inmueble` (`inmueble_id`),
ADD KEY `idx_venta` (`venta`),
ADD KEY `idx_renta` (`renta`),
ADD KEY `idx_colonia_id` (`colonia_id`) USING BTREE,
ADD KEY `idx_ciudad_id` (`ciudad_id`) USING BTREE,
ADD KEY `idx_estado_id` (`estado_id`) USING BTREE,
ADD KEY `idx_precio_renta` (`precio_renta`) USING BTREE,
ADD KEY `idx_precio_eventual` (`precio_eventual`) USING BTREE,
ADD KEY `idx_eventual` (`eventual`) USING BTREE,
ADD KEY `idx_precio_por_metro` (`precio_por_metro`) USING BTREE;

ALTER TABLE `zonas` ADD PRIMARY KEY (`id`);

ALTER TABLE `ampi_articulos_noticias`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_comercializacion`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_encuestas`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_encuestas_respuestas`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_funciones`
MODIFY `id` int(2) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_nucal_categories`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_nucal_events`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_secciones`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_socios_externos`
MODIFY `id` int(4) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_tmp_errores_calendario`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_usuarios`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_usuarios_20240316`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `api_tokens`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `asesores` MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `asesor_cliente`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `avisos_bolsa`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `avisos_respuestas`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `bolsa_compradores`
MODIFY `id` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `bolsa_compradores_r`
MODIFY `id` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `bolsa_grupos`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `bolsa_grupos_rel`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `bolsa_inmobiliaria`
MODIFY `id` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `campos_inmuebles`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `campos_inmuebles_grupos`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `casas_compartidas`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `citas` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `citas_20240319`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ciudades` MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `colonias` MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `componentes`
MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `componentes_campos`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `componentes_usuarios`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `componentes_valores`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `config_mulbin`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `contactos` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `contactos_etq`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `contenidos`
MODIFY `id` bigint(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `desarrollos`
MODIFY `clave_sistema` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `desarrollos_20240316`
MODIFY `clave_sistema` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `desarrollos_etq`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `desarrollos_fotos`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `des_v_d` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `des_v_d_20240317`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `directorio_inmobiliario`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `documentos`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `encuestas` MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `encuestas_r`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `estados` MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `files` MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_des_fotos`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_des_panoramicas`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_des_planos`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_fotos` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_fotos_20240317`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_panoramicas`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `f_panoramicas_20240317`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `gpt` MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ips_busca`
MODIFY `id` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `languages` MODIFY `id` int(2) NOT NULL AUTO_INCREMENT;

ALTER TABLE `lateral_secciones`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `lateral_secciones_20231129`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `lateral_secciones_20250203`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `links` MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `machotes_clausulas`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `monedas` MODIFY `id` int(2) NOT NULL AUTO_INCREMENT;

ALTER TABLE `movpropiedades`
MODIFY `id` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `mov_propiedades`
MODIFY `id` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `new_colonias`
MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

ALTER TABLE `paises` MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `panoramicas`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `preguntas`
MODIFY `numero` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `preguntas_20250114`
MODIFY `numero` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `propiedades`
MODIFY `clave_sistema` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `props_eliminadas`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `props_eliminadas_20240314`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `prop_status`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `rel_prop_des`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `rel_prop_des_20231212`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `reportes_visitas`
MODIFY `llave` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `reportes_visitas_20231212`
MODIFY `llave` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas` MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_articulos_noticias`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_clasificaciones`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_clasificados`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_dir_anunciantes`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_dir_secciones`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_paginas_graficas`
MODIFY `id` bigint(12) NOT NULL AUTO_INCREMENT;

ALTER TABLE `revistas_planes`
MODIFY `id` int(5) NOT NULL AUTO_INCREMENT;

ALTER TABLE `sucursales`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `table_fields`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `table_field_values`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `tags` MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `tags_pivot`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `tips` MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `users`
MODIFY `id` mediumint(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `valores_campos`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `valores_campos_aprendidos`
MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

ALTER TABLE `visitas_propiedades`
MODIFY `id` int(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `widget_inmuebles`
MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

ALTER TABLE `zonas` MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ampi_usuarios`
ADD CONSTRAINT `ampi_usuarios_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `api_tokens`
ADD CONSTRAINT `api_tokens_config_foreign` FOREIGN KEY (`contrato_id`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `asesores`
ADD CONSTRAINT `asesores_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `asesores_sucursales` FOREIGN KEY (`sucursal_id`) REFERENCES `sucursales` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `asesor_cliente`
ADD CONSTRAINT `asesor_cliente_asesor_id_asesores_foreign` FOREIGN KEY (`asesor_id`) REFERENCES `asesores` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `asesor_cliente_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `bolsa_compradores`
ADD CONSTRAINT `bolsa_compradores_asesor_asesores_foreign` FOREIGN KEY (`asesor`) REFERENCES `asesores` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `bolsa_compradores_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `bolsa_compradores_r`
ADD CONSTRAINT `bolsa_compradores_r_asesor_asesores_foreign` FOREIGN KEY (`asesor`) REFERENCES `asesores` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `bolsa_compradores_r_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `bolsa_compradores_r_rid_bolsa_compradores_foreign` FOREIGN KEY (`rid`) REFERENCES `bolsa_compradores` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `bolsa_inmobiliaria`
ADD CONSTRAINT `bolsa_inmobiliaria_contrato_solicitado_foreign` FOREIGN KEY (`contrato_solicitado`) REFERENCES `config` (`contrato`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `bolsa_inmobiliaria_contrato_solicitante_config_foreign` FOREIGN KEY (`contrato_solicitante`) REFERENCES `config` (`contrato`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `busca_propiedades`
ADD CONSTRAINT `busca_propiedades_clave_sistema_propiedades_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `busca_propiedades_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `campos_inhabilitados`
ADD CONSTRAINT `campos_inhabilitados_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `campos_inmuebles`
ADD CONSTRAINT `campos_inmuebles_campos_inmuebles_grupo` FOREIGN KEY (`grupo_id`) REFERENCES `campos_inmuebles_grupos` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `campos_inmuebles_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `campos_inmuebles_grupos`
ADD CONSTRAINT `campos_inmuebles_grupos_config` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `casas_compartidas`
ADD CONSTRAINT `casas_compartidas_config` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `casas_compartidas_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `casas_compartidas_quien_solicita_config_foreign` FOREIGN KEY (`quien_solicita`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `citas`
ADD CONSTRAINT `citas_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `desarrollos`
ADD CONSTRAINT `desarrollos_config` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `desarrollos_compartidos`
ADD CONSTRAINT `desarrollos_compartidos_clave_sistema_desarrollos_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `desarrollos` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `desarrollos_compartidos_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `desarrollos_fotos`
ADD CONSTRAINT `desarrollos_fotos_clave_sistema_desarrollos_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `desarrollos` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `des_v_d`
ADD CONSTRAINT `des_v_d_desarrollo_desarrollos_foreign` FOREIGN KEY (`desarrollo`) REFERENCES `desarrollos` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `directorio_inmobiliario`
ADD CONSTRAINT `directorio_inmobiliario_contrato_id` FOREIGN KEY (`contrato_id`) REFERENCES `config` (`contrato`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `documentos`
ADD CONSTRAINT `documentos_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `fav_historial`
ADD CONSTRAINT `fav_historial_clave_sistema_propiedades_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fav_historial_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `files`
ADD CONSTRAINT `files_config_foreign` FOREIGN KEY (`contrato_id`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `f_fotos`
ADD CONSTRAINT `f_fotos_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `f_fotos_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `f_panoramicas`
ADD CONSTRAINT `f_panoramicas_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `f_panoramicas_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `gpt`
ADD CONSTRAINT `gpt_contrato` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`);

ALTER TABLE `lateral_secciones`
ADD CONSTRAINT `lateral_secciones_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `new_colonias`
ADD CONSTRAINT `new_colonias_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `preguntas`
ADD CONSTRAINT `preguntas_asesor_foreign` FOREIGN KEY (`asesor`) REFERENCES `asesores` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `preguntas_cc_asesor_foreign` FOREIGN KEY (`cc_asesor`) REFERENCES `asesores` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `preguntas_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `propiedades`
ADD CONSTRAINT `propiedades_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `propiedades_new_colonias_foreign` FOREIGN KEY (`id_colonia`) REFERENCES `new_colonias` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `propiedades_prop_status` FOREIGN KEY (`status_id`) REFERENCES `prop_status` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `propiedades_sucursales_foreign` FOREIGN KEY (`sucursal`) REFERENCES `sucursales` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `rel_prop_des`
ADD CONSTRAINT `rel_prop_des_desarrollos` FOREIGN KEY (`des_clave_sistema`) REFERENCES `desarrollos` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `rel_prop_des_propiedades` FOREIGN KEY (`prop_clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `reportes_visitas`
ADD CONSTRAINT `reportes_visitas_propiedades` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `table_fields`
ADD CONSTRAINT `table_fields_config_foreign` FOREIGN KEY (`contrato_id`) REFERENCES `config` (`contrato`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `tags`
ADD CONSTRAINT `tags_user_id` FOREIGN KEY (`user_id`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `tags_pivot`
ADD CONSTRAINT `prop_props` FOREIGN KEY (`prop_id`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `tag_tags` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `tipo_inm_pivot`
ADD CONSTRAINT `tipo_inm_pivot_propiedades` FOREIGN KEY (`prop_id`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `tipo_inm_pivot_tipo_inmuebles` FOREIGN KEY (`tipo_id`) REFERENCES `tipo_inmuebles` (`clave`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `valores_campos`
ADD CONSTRAINT `valores_campos_clave_sistema_propiedades_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `valores_campos_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `valores_campos_variable_id_campos_inmuebles_foreign` FOREIGN KEY (`variable_id`) REFERENCES `campos_inmuebles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `valores_campos_aprendidos`
ADD CONSTRAINT `valores_campos_aprendidos_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `widget_inmuebles`
ADD CONSTRAINT `widgets_inmuebles_config_foreign` FOREIGN KEY (`contrato_id`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `widgets_inmuebles_propiedades_foreign` FOREIGN KEY (`inmueble_id`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE;

COMMIT;