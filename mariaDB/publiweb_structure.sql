SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";

START TRANSACTION;

SET time_zone = "+00:00";

CREATE TABLE `act_si` (
    `id` int(6) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `tema` varchar(50) NOT NULL DEFAULT '',
    `personalizar_tema` varchar(75) NOT NULL DEFAULT '',
    `plan` varchar(75) NOT NULL DEFAULT '',
    `vende_en_linea` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `meses` int(2) NOT NULL DEFAULT 0,
    `total_a_pagar` int(7) NOT NULL DEFAULT 0,
    `observaciones` text NOT NULL,
    `status` enum(
        'sin confirmar',
        'activado',
        'en proceso'
    ) NOT NULL DEFAULT 'sin confirmar',
    `clave` varchar(100) NOT NULL DEFAULT '',
    `cobros` varchar(100) NOT NULL DEFAULT '',
    `fecha_registro` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_activacion` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `admin` (
    `usuario` varchar(15) NOT NULL DEFAULT '',
    `password` varchar(100) NOT NULL DEFAULT '',
    `dolar` double(9, 2) NOT NULL DEFAULT 0.00,
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `en_oficina` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `admin_publiweb` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `admin_si` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `contabilidad` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `cobranza` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `pagos` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `email` varchar(70) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `cache` (
    `key` varchar(255) NOT NULL,
    `value` mediumtext NOT NULL,
    `expiration` int(11) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `cache_locks` (
    `key` varchar(255) NOT NULL,
    `owner` varchar(255) NOT NULL,
    `expiration` int(11) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `checador` (
    `id` int(7) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `fecha_hora` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `tipo` enum('Entrada', 'Salida') NOT NULL DEFAULT 'Entrada',
    `observaciones` varchar(250) NOT NULL DEFAULT '',
    `actividades` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `clientes` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `usuario` varchar(25) DEFAULT NULL,
    `logmail` varchar(75) DEFAULT NULL,
    `password` varchar(100) NOT NULL,
    `remember_token` varchar(100) DEFAULT NULL,
    `google_id` varchar(255) DEFAULT NULL,
    `google_token` varchar(255) DEFAULT NULL,
    `google_refresh_token` varchar(255) DEFAULT NULL,
    `facebook_id` varchar(255) DEFAULT NULL,
    `facebook_token` varchar(255) DEFAULT NULL,
    `as_cnt` int(7) DEFAULT NULL COMMENT 'Número de contrato de S.I. al que este usuario pertenece como asesor inmobiliario',
    `name` varchar(255) DEFAULT NULL,
    `nombre` varchar(100) NOT NULL,
    `apellidos` varchar(100) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `calle_numero` varchar(70) NOT NULL DEFAULT '',
    `colonia` varchar(30) NOT NULL DEFAULT '',
    `codigo_postal` int(5) NOT NULL DEFAULT 0,
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `code_alpha2` varchar(5) NOT NULL DEFAULT 'es-MX' COMMENT 'Código del país (es-MX)',
    `como_llegar` mediumtext DEFAULT NULL,
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `phone_country_code` varchar(5) DEFAULT NULL,
    `phone_number` varchar(20) DEFAULT NULL,
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(10) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `fax` varchar(15) NOT NULL DEFAULT '',
    `email` varchar(60) DEFAULT NULL,
    `email_verified_at` timestamp NULL DEFAULT NULL,
    `website_activated_at` timestamp NULL DEFAULT NULL,
    `avatar` varchar(255) DEFAULT NULL,
    `email_sec` varchar(60) NOT NULL DEFAULT '',
    `sitio_web` varchar(60) NOT NULL DEFAULT '',
    `fact_nombre` varchar(100) NOT NULL DEFAULT '',
    `fact_domicilio` varchar(150) NOT NULL DEFAULT '',
    `fact_rfc` varchar(15) NOT NULL DEFAULT '',
    `activo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `observaciones` mediumtext DEFAULT NULL,
    `quien_registro` varchar(50) NOT NULL DEFAULT '',
    `conekta_id` varchar(255) DEFAULT NULL COMMENT 'Customer ID en Conekta',
    `pabusqueda` mediumtext NOT NULL DEFAULT '',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `clientes_20250418` (
    `id` int(7) NOT NULL,
    `usuario` varchar(25) DEFAULT NULL,
    `logmail` varchar(75) DEFAULT NULL,
    `pass` varchar(100) DEFAULT NULL,
    `password` varchar(100) NOT NULL DEFAULT '',
    `as_cnt` int(7) DEFAULT NULL COMMENT 'Número de contrato de S.I. al que este usuario pertenece como asesor inmobiliario',
    `nombre` varchar(100) NOT NULL,
    `apellidos` varchar(100) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `calle_numero` varchar(70) NOT NULL DEFAULT '',
    `colonia` varchar(30) NOT NULL DEFAULT '',
    `codigo_postal` int(5) NOT NULL DEFAULT 0,
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `code_alpha2` varchar(5) NOT NULL DEFAULT 'es-MX' COMMENT 'Código del país (es-MX)',
    `como_llegar` mediumtext DEFAULT NULL,
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(10) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `fax` varchar(15) NOT NULL DEFAULT '',
    `email` varchar(60) DEFAULT NULL,
    `email_sec` varchar(60) NOT NULL DEFAULT '',
    `sitio_web` varchar(60) NOT NULL DEFAULT '',
    `fact_nombre` varchar(100) NOT NULL DEFAULT '',
    `fact_domicilio` varchar(150) NOT NULL DEFAULT '',
    `fact_rfc` varchar(15) NOT NULL DEFAULT '',
    `activo` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `observaciones` mediumtext DEFAULT NULL,
    `quien_registro` varchar(50) NOT NULL DEFAULT '',
    `conekta_id` varchar(255) DEFAULT NULL COMMENT 'Customer ID en Conekta',
    `pabusqueda` mediumtext NOT NULL DEFAULT '',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `clientes_preregistros` (
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `password` varchar(150) NOT NULL DEFAULT '',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `apellidos` varchar(50) NOT NULL DEFAULT '',
    `ocupacion` varchar(25) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `calle_numero` varchar(70) NOT NULL DEFAULT '',
    `colonia` varchar(30) NOT NULL DEFAULT '',
    `codigo_postal` int(5) NOT NULL DEFAULT 0,
    `ciudad` varchar(30) NOT NULL DEFAULT '',
    `estado` varchar(25) NOT NULL DEFAULT '',
    `pais` varchar(25) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `celular` varchar(30) NOT NULL DEFAULT '',
    `nextel_tel` varchar(10) NOT NULL DEFAULT '',
    `nextel_radio` varchar(15) NOT NULL DEFAULT '',
    `fax` varchar(15) NOT NULL DEFAULT '',
    `email` varchar(60) NOT NULL DEFAULT '',
    `email_sec` varchar(60) NOT NULL DEFAULT '',
    `sitio_web` varchar(60) NOT NULL DEFAULT '',
    `fact_nombre` varchar(100) NOT NULL DEFAULT '',
    `fact_domicilio` varchar(150) NOT NULL DEFAULT '',
    `fact_rfc` varchar(15) NOT NULL DEFAULT '',
    `clave` varchar(100) NOT NULL,
    `datos_registrante` text NOT NULL,
    `ontoy` varchar(250) NOT NULL,
    `fecha_hora` datetime NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `conekta_customers` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `conekta_id` varchar(255) NOT NULL,
    `payment_sources` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`payment_sources`)),
    `default_payment_source_id` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `contratos` (
    `numero` int(7) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `servicio` varchar(15) DEFAULT NULL,
    `dominio` varchar(60) NOT NULL DEFAULT '',
    `s_usuario` varchar(25) NOT NULL DEFAULT '',
    `s_password` varchar(15) NOT NULL DEFAULT '',
    `observaciones` mediumtext DEFAULT NULL,
    `forma_pago` int(3) NOT NULL DEFAULT 1,
    `en_precio` double(9, 2) NOT NULL DEFAULT 0.00,
    `por_adelantado` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `dias_tregua` int(3) NOT NULL DEFAULT 0,
    `cobro_dia` int(2) NOT NULL DEFAULT 0,
    `cobro_siguiente` tinyint(2) NOT NULL DEFAULT 0 COMMENT 'Número de días para realizar el siguiente cobro relacionado al contrato, 0 hace el cobro el día del vencimiento, 1 un día antes..., -1 dejará un cobro pendiente de pago con inicio después de now()',
    `pago_automatizado` tinyint(1) NOT NULL DEFAULT 0,
    `cobranza_generada` date DEFAULT NULL COMMENT 'Fecha en que se hace cobranza al contrato',
    `tipo` enum('empresarial', 'particular') NOT NULL DEFAULT 'particular',
    `fecha` timestamp NULL DEFAULT NULL,
    `pagado_hasta` timestamp NULL DEFAULT NULL,
    `prorroga` timestamp NULL DEFAULT NULL,
    `history` text DEFAULT NULL,
    `pabusqueda` mediumtext DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `contratos_cobranza` (
    `id` int(7) NOT NULL,
    `Fecha` date NOT NULL DEFAULT '0000-00-00',
    `Hora` time NOT NULL DEFAULT '00:00:00'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `controlclic_clics` (
    `id` bigint(15) NOT NULL,
    `id_usuario` int(7) NOT NULL,
    `de_donde` varchar(250) NOT NULL,
    `a_donde` varchar(250) NOT NULL,
    `ip` varchar(25) NOT NULL,
    `fecha` date NOT NULL,
    `hora` time NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `control_diario` (
    `id` int(9) NOT NULL,
    `QuienRecibio` varchar(55) NOT NULL DEFAULT '',
    `Seccion` varchar(35) NOT NULL DEFAULT '',
    `Tipo` enum(
        'Tarea',
        'Recado',
        'Seguimiento'
    ) NOT NULL DEFAULT 'Tarea',
    `Usuario` varchar(25) NOT NULL DEFAULT '',
    `NombreCompleto` varchar(100) NOT NULL DEFAULT '',
    `Telefono` varchar(50) NOT NULL DEFAULT '',
    `Capturado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `TentativaDeRespuesta` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `Comenzado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `Terminado` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `Asunto` varchar(75) NOT NULL DEFAULT '',
    `Status` enum(
        'Recibido',
        'En proceso',
        'Terminado'
    ) NOT NULL DEFAULT 'Recibido'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `control_notas` (
    `id` int(9) NOT NULL,
    `control_id` int(9) NOT NULL DEFAULT 0,
    `Contenido` text NOT NULL,
    `HoraFecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `QuienCaptura` varchar(55) NOT NULL DEFAULT '',
    `Leido` enum('Si', 'No') NOT NULL DEFAULT 'No'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `departamentos` (
    `id` int(3) NOT NULL,
    `Nombre` varchar(35) NOT NULL DEFAULT '',
    `Descripcion` varchar(250) NOT NULL DEFAULT '',
    `Responsable` varchar(55) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `depositos` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `id_usuario` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
    `banco` varchar(20) NOT NULL DEFAULT '',
    `cantidad` double(11, 2) NOT NULL DEFAULT 0.00,
    `fecha_deposito` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_registro` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `observaciones` mediumtext DEFAULT NULL,
    `verificado` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `item_paypal` int(7) DEFAULT NULL,
    `movimiento` varchar(25) DEFAULT NULL,
    `user_admin` varchar(25) DEFAULT NULL,
    `factura_mbi` int(7) DEFAULT NULL,
    `api_response` text DEFAULT NULL COMMENT 'Respuesta de la API de la plataforma en donde se hace el cargo en línea'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `depositos_20240830` (
    `id` int(9) NOT NULL,
    `id_usuario` int(7) NOT NULL DEFAULT 0,
    `banco` varchar(20) NOT NULL DEFAULT '',
    `cantidad` double(11, 2) NOT NULL DEFAULT 0.00,
    `fecha_deposito` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fecha_registro` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `observaciones` text DEFAULT NULL,
    `verificado` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `item_paypal` int(7) DEFAULT NULL,
    `movimiento` varchar(25) DEFAULT NULL,
    `user_admin` varchar(25) DEFAULT NULL,
    `factura_mbi` int(7) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `dep_fact` (
    `id_deposito` int(9) NOT NULL DEFAULT 0,
    `id_factura` int(9) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `dep_rel` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `id_deposito` bigint(20) UNSIGNED NOT NULL,
    `id_cobro` bigint(20) UNSIGNED NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `dep_rel_240909` (
    `id` int(7) NOT NULL,
    `id_deposito` int(7) NOT NULL,
    `id_cobro` int(7) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `dominios` (
    `dominio` varchar(100) NOT NULL DEFAULT '',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `creado` date NOT NULL DEFAULT '0000-00-00',
    `expira` date NOT NULL DEFAULT '0000-00-00',
    `servicio` enum(
        'ninguno',
        'Sistema Inmobiliario',
        'Web Hosting',
        'Redireccionamiento'
    ) NOT NULL DEFAULT 'ninguno',
    `registrante` int(3) NOT NULL DEFAULT 0,
    `nic_usuario` varchar(35) NOT NULL DEFAULT '',
    `nic_password` varchar(35) NOT NULL DEFAULT '',
    `status` enum(
        'Nosotros administramos',
        'Con otro',
        'Lo perdimos',
        'Ya no renueva'
    ) NOT NULL DEFAULT 'Nosotros administramos',
    `ultima_actualizacion` date NOT NULL DEFAULT '0000-00-00',
    `observaciones` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `dominios_reg` (
    `id` int(3) NOT NULL,
    `registrante` varchar(35) NOT NULL DEFAULT '',
    `url` varchar(70) NOT NULL DEFAULT '',
    `txt_identificador` varchar(100) NOT NULL DEFAULT '',
    `txt_id2` varchar(100) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `emails_publicidad` (
    `email` varchar(75) NOT NULL DEFAULT '',
    `nombre` varchar(100) NOT NULL DEFAULT '',
    `empresa` varchar(50) NOT NULL DEFAULT '',
    `fecha_registro` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `grp_pruebas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `grp_sistema_inmobiliario` enum('Si', 'No') NOT NULL DEFAULT 'Si'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `emails_pub_noenviar` (
    `email` varchar(75) NOT NULL DEFAULT '',
    `grp_sistema_inmobiliario` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `grp_pruebas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `razon_exclusion` varchar(50) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `facturas` (
    `id` int(9) NOT NULL,
    `numero` varchar(50) NOT NULL DEFAULT '',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `cancelada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `fecha_cancelacion` date NOT NULL DEFAULT '0000-00-00',
    `fact_nombre` varchar(100) NOT NULL DEFAULT '',
    `fact_domicilio` varchar(150) NOT NULL DEFAULT '',
    `fact_rfc` varchar(15) NOT NULL DEFAULT '',
    `telefono` varchar(30) NOT NULL DEFAULT '',
    `impresa` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `pagada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `imagen` text DEFAULT NULL COMMENT 'JSON con los nombres de archivo relacionados a la factura',
    `enviada` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `observaciones` text DEFAULT NULL,
    `pabusqueda` text DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `failed_jobs` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `uuid` varchar(255) NOT NULL,
    `connection` text NOT NULL,
    `queue` text NOT NULL,
    `payload` longtext NOT NULL,
    `exception` longtext NOT NULL,
    `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `gastos` (
    `id` int(9) NOT NULL,
    `fecha` date NOT NULL DEFAULT '0000-00-00',
    `nombre` varchar(50) NOT NULL DEFAULT '',
    `cuenta` int(3) NOT NULL DEFAULT 0,
    `num_cheque` varchar(12) NOT NULL DEFAULT '0',
    `concepto` varchar(150) NOT NULL DEFAULT '',
    `importe` double(11, 2) NOT NULL DEFAULT 0.00,
    `observaciones` text NOT NULL,
    `pago_cheque` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `creado_por` varchar(25) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `gastos_cuentas` (
    `id` int(3) NOT NULL,
    `nombre` varchar(40) NOT NULL DEFAULT '',
    `metodo_pago` enum('Cheque', 'Otro') NOT NULL DEFAULT 'Cheque',
    `moneda` enum('MXP', 'USD') NOT NULL DEFAULT 'MXP',
    `saldo` double(11, 2) NOT NULL DEFAULT 0.00,
    `status` enum('activo', 'inactivo') NOT NULL,
    `orden` int(2) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `jobs` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `queue` varchar(255) NOT NULL,
    `payload` longtext NOT NULL,
    `attempts` tinyint(3) UNSIGNED NOT NULL,
    `reserved_at` int(10) UNSIGNED DEFAULT NULL,
    `available_at` int(10) UNSIGNED NOT NULL,
    `created_at` int(10) UNSIGNED NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `job_batches` (
    `id` varchar(255) NOT NULL,
    `name` varchar(255) NOT NULL,
    `total_jobs` int(11) NOT NULL,
    `pending_jobs` int(11) NOT NULL,
    `failed_jobs` int(11) NOT NULL,
    `failed_job_ids` longtext NOT NULL,
    `options` mediumtext DEFAULT NULL,
    `cancelled_at` int(11) DEFAULT NULL,
    `created_at` int(11) NOT NULL,
    `finished_at` int(11) DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `llamadas` (
    `id` int(11) NOT NULL,
    `registro` timestamp NOT NULL DEFAULT current_timestamp(),
    `dominio` varchar(150) DEFAULT NULL,
    `llamo` varchar(250) NOT NULL,
    `telefono` varchar(50) DEFAULT NULL,
    `celular` varchar(50) DEFAULT NULL,
    `email` varchar(200) DEFAULT NULL,
    `contesto` varchar(250) NOT NULL,
    `busco` varchar(250) DEFAULT NULL,
    `asunto` varchar(250) NOT NULL,
    `nota` text DEFAULT NULL,
    `ultnota` varchar(250) DEFAULT NULL,
    `id_ticket` int(11) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci COMMENT = 'Registro de llamadas';

CREATE TABLE `migrations` (
    `id` int(10) UNSIGNED NOT NULL,
    `migration` varchar(255) NOT NULL,
    `batch` int(11) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `nombres_recados` (
    `NombreCompleto` varchar(150) NOT NULL DEFAULT ''
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `notificaciones_cobros` (
    `id` int(9) NOT NULL,
    `fecha_hora` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `facturas` varchar(150) NOT NULL DEFAULT '',
    `metodo` enum(
        'Telefono',
        'Sistema',
        'Email',
        'Otro'
    ) NOT NULL DEFAULT 'Telefono',
    `notificacion` text NOT NULL,
    `respuesta` text NOT NULL,
    `adeudo_notificado` double(9, 2) NOT NULL DEFAULT 0.00
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `pagos_paypal` (
    `ci` int(7) NOT NULL,
    `nombre_pago` varchar(250) NOT NULL,
    `id` varchar(100) NOT NULL,
    `usuario` varchar(25) NOT NULL,
    `inicio` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `fin` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `contrato` int(9) NOT NULL,
    `mxp` double(9, 2) NOT NULL,
    `usd` double(9, 2) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `password_reset_tokens` (
    `email` varchar(255) NOT NULL,
    `token` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `por_cobrar` (
    `numero` bigint(20) UNSIGNED NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `servicio` varchar(15) NOT NULL DEFAULT '',
    `cantidad` int(5) NOT NULL DEFAULT 0,
    `descripcion` varchar(100) NOT NULL DEFAULT '',
    `dominio` varchar(60) NOT NULL DEFAULT '',
    `adicional` mediumtext DEFAULT NULL,
    `precio` double(11, 2) NOT NULL DEFAULT 0.00,
    `desde` date NOT NULL DEFAULT '0000-00-00',
    `hasta` date NOT NULL DEFAULT '0000-00-00',
    `pagado` enum('No', 'Si') NOT NULL DEFAULT 'No',
    `factura` varchar(50) NOT NULL DEFAULT '',
    `notificacion_pago` timestamp NULL DEFAULT NULL,
    `forma_pago` varchar(50) NOT NULL DEFAULT '',
    `observaciones_pago` mediumtext DEFAULT NULL,
    `vencimiento` date NOT NULL DEFAULT '0000-00-00',
    `ultima_notificacion` date DEFAULT NULL,
    `detener` enum('No', 'Si') NOT NULL DEFAULT 'No',
    `cantidad_pagada` int(5) NOT NULL DEFAULT 0,
    `autorizacion_de_pago` timestamp NULL DEFAULT NULL,
    `real_creado` timestamp NOT NULL DEFAULT current_timestamp(),
    `real_modificado` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
    `creado_por` varchar(20) NOT NULL DEFAULT '',
    `modificado_por` varchar(20) NOT NULL DEFAULT '',
    `pabusqueda` mediumtext DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `por_cobrar_20240727` (
    `numero` int(9) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `fecha` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `servicio` varchar(15) NOT NULL DEFAULT '',
    `cantidad` int(5) NOT NULL DEFAULT 0,
    `descripcion` varchar(100) NOT NULL DEFAULT '',
    `dominio` varchar(60) NOT NULL DEFAULT '',
    `adicional` mediumtext DEFAULT NULL,
    `precio` double(11, 2) NOT NULL DEFAULT 0.00,
    `desde` date NOT NULL DEFAULT '0000-00-00',
    `hasta` date NOT NULL DEFAULT '0000-00-00',
    `pagado` enum('No', 'Si') NOT NULL DEFAULT 'No',
    `factura` varchar(50) NOT NULL DEFAULT '',
    `notificacion_pago` timestamp NULL DEFAULT NULL,
    `forma_pago` varchar(50) NOT NULL DEFAULT '',
    `observaciones_pago` mediumtext DEFAULT NULL,
    `vencimiento` date NOT NULL DEFAULT '0000-00-00',
    `ultima_notificacion` date DEFAULT NULL,
    `detener` enum('No', 'Si') NOT NULL DEFAULT 'No',
    `cantidad_pagada` int(5) NOT NULL DEFAULT 0,
    `autorizacion_de_pago` timestamp NULL DEFAULT NULL,
    `real_creado` timestamp NOT NULL DEFAULT current_timestamp(),
    `real_modificado` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
    `creado_por` varchar(20) NOT NULL DEFAULT '',
    `modificado_por` varchar(20) NOT NULL DEFAULT '',
    `pabusqueda` mediumtext DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `promociones` (
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `contrato` int(9) NOT NULL DEFAULT 0
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `prorrogas` (
    `id` int(7) NOT NULL,
    `fecha` datetime NOT NULL,
    `contrato` int(9) NOT NULL,
    `vencimiento` date NOT NULL,
    `autorizado_por` varchar(55) NOT NULL,
    `notas` text NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `recibos` (
    `folio` bigint(12) NOT NULL,
    `dominio` varchar(60) NOT NULL,
    `contrato` int(9) NOT NULL,
    `numero` int(9) NOT NULL,
    `pago` varchar(100) NOT NULL,
    `recibio` varchar(100) NOT NULL,
    `cantidad` int(5) NOT NULL,
    `letra` varchar(200) NOT NULL,
    `tipopago` varchar(50) NOT NULL,
    `concepto` text NOT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp(),
    `edito` varchar(50) DEFAULT NULL,
    `fechaedit` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cancelado` char(1) NOT NULL DEFAULT 'N',
    `cancelo` varchar(50) DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `relacion_servicios` (
    `proyecto` varchar(50) NOT NULL,
    `servicio` varchar(15) NOT NULL,
    `tipo` varchar(20) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `respuestas` (
    `id` int(11) NOT NULL,
    `id_ticket` int(11) NOT NULL,
    `usuario` varchar(15) NOT NULL,
    `nota` text DEFAULT NULL,
    `respant` int(11) DEFAULT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `interno` char(1) NOT NULL DEFAULT 'S'
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci COMMENT = 'Respuesta a los Tickets';

CREATE TABLE `servicios` (
    `servicio` varchar(15) NOT NULL DEFAULT '',
    `descripcion` varchar(100) DEFAULT NULL,
    `precio` double(11, 2) NOT NULL DEFAULT 0.00,
    `observaciones` longtext DEFAULT NULL,
    `notifica_vencimiento` int(2) NOT NULL DEFAULT 0,
    `notifica_cada` int(2) NOT NULL DEFAULT 0,
    `cobro_automatico` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `cobro_mensual` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `desc_trimestral` double(4, 2) NOT NULL DEFAULT 0.00,
    `desc_semestral` double(4, 2) NOT NULL DEFAULT 0.00,
    `desc_anual` double(4, 2) NOT NULL DEFAULT 0.00,
    `renovacion_cada` int(3) NOT NULL DEFAULT 0,
    `inmuebles` int(5) NOT NULL DEFAULT 0,
    `publicado` varchar(15) DEFAULT NULL,
    `acepta_prorrogas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `NotificandoVencidos` int(3) NOT NULL DEFAULT 0,
    `QueHacerVencido` int(3) NOT NULL DEFAULT 0,
    `tipo` varchar(10) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE `servicios_20240518` (
    `servicio` varchar(15) NOT NULL DEFAULT '',
    `descripcion` varchar(100) NOT NULL DEFAULT '',
    `precio` double(11, 2) NOT NULL DEFAULT 0.00,
    `observaciones` longtext NOT NULL,
    `notifica_vencimiento` int(2) NOT NULL DEFAULT 0,
    `notifica_cada` int(2) NOT NULL DEFAULT 0,
    `cobro_automatico` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `cobro_mensual` enum('Si', 'No') NOT NULL DEFAULT 'Si',
    `desc_trimestral` double(4, 2) NOT NULL DEFAULT 0.00,
    `desc_semestral` double(4, 2) NOT NULL DEFAULT 0.00,
    `desc_anual` double(4, 2) NOT NULL DEFAULT 0.00,
    `renovacion_cada` int(3) NOT NULL DEFAULT 0,
    `inmuebles` int(5) NOT NULL DEFAULT 0,
    `publicado` varchar(15) NOT NULL DEFAULT '',
    `acepta_prorrogas` enum('Si', 'No') NOT NULL DEFAULT 'No',
    `NotificandoVencidos` int(3) NOT NULL DEFAULT 0,
    `QueHacerVencido` int(3) NOT NULL DEFAULT 0,
    `tipo` varchar(10) NOT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `sessions` (
    `id` varchar(255) NOT NULL,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `payload` longtext NOT NULL,
    `last_activity` int(11) NOT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `subscriptions` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `conekta_id` varchar(255) NOT NULL,
    `conekta_plan` varchar(255) NOT NULL,
    `conekta_customer` varchar(255) NOT NULL,
    `status` varchar(255) NOT NULL,
    `trial_ends_at` timestamp NULL DEFAULT NULL,
    `ends_at` timestamp NULL DEFAULT NULL,
    `billing_period` varchar(255) DEFAULT NULL,
    `plan_price` decimal(10, 2) NOT NULL,
    `total_price` decimal(10, 2) NOT NULL,
    `personalization` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `tickets` (
    `id` int(11) NOT NULL,
    `dominio` varchar(250) NOT NULL,
    `asunto` varchar(250) NOT NULL,
    `fecha` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `levanto` varchar(50) NOT NULL,
    `cerrado` char(1) NOT NULL,
    `cerro` varchar(50) DEFAULT NULL,
    `asignado` varchar(50) DEFAULT NULL,
    `detalle` text DEFAULT NULL
) ENGINE = MyISAM DEFAULT CHARSET = latin1 COLLATE = latin1_swedish_ci;

CREATE TABLE `webhooks` (
    `id` bigint(20) UNSIGNED NOT NULL,
    `cliente_ids` varchar(25) DEFAULT NULL,
    `cobro_id` bigint(20) UNSIGNED DEFAULT NULL,
    `origin` varchar(150) DEFAULT NULL COMMENT 'Desde donde se genera este WebHook',
    `type` varchar(100) DEFAULT NULL COMMENT 'Tipo de webhook, ej. order.created | charge.paid',
    `id_item` varchar(250) DEFAULT NULL,
    `data` mediumtext DEFAULT NULL COMMENT 'Generalmente el POST con el cual es invocado',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

ALTER TABLE `act_si`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `usuario` (`usuario`);

ALTER TABLE `admin` ADD PRIMARY KEY (`usuario`);

ALTER TABLE `cache` ADD PRIMARY KEY (`key`);

ALTER TABLE `cache_locks` ADD PRIMARY KEY (`key`);

ALTER TABLE `checador` ADD PRIMARY KEY (`id`);

ALTER TABLE `clientes`
ADD PRIMARY KEY (`id`) USING BTREE,
ADD UNIQUE KEY `usuario` (`usuario`) USING BTREE,
ADD UNIQUE KEY `conekta_id` (`conekta_id`),
ADD UNIQUE KEY `logmail` (`logmail`) USING BTREE;

ALTER TABLE `clientes_20250418`
ADD PRIMARY KEY (`id`) USING BTREE,
ADD UNIQUE KEY `usuario` (`usuario`) USING BTREE,
ADD UNIQUE KEY `conekta_id` (`conekta_id`),
ADD KEY `logmail` (`logmail`);

ALTER TABLE `clientes_preregistros` ADD PRIMARY KEY (`usuario`);

ALTER TABLE `conekta_customers`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `conekta_customers_conekta_id_unique` (`conekta_id`),
ADD KEY `conekta_customers_user_id_foreign` (`user_id`);

ALTER TABLE `contratos`
ADD PRIMARY KEY (`numero`),
ADD KEY `contratos_usuario_clientes_foreign` (`usuario`),
ADD KEY `contratos_servicio_servicios_foreign` (`servicio`);

ALTER TABLE `contratos_cobranza` ADD PRIMARY KEY (`id`);

ALTER TABLE `controlclic_clics` ADD PRIMARY KEY (`id`);

ALTER TABLE `control_diario` ADD PRIMARY KEY (`id`);

ALTER TABLE `control_notas` ADD PRIMARY KEY (`id`);

ALTER TABLE `departamentos` ADD PRIMARY KEY (`id`);

ALTER TABLE `depositos`
ADD PRIMARY KEY (`id`),
ADD KEY `depositos_id_usuario_foreign` (`id_usuario`);

ALTER TABLE `depositos_20240830` ADD PRIMARY KEY (`id`);

ALTER TABLE `dep_rel`
ADD PRIMARY KEY (`id`),
ADD KEY `dep_rel_id_deposito_foreign` (`id_deposito`),
ADD KEY `dep_rel_id_cobro_foreign` (`id_cobro`);

ALTER TABLE `dep_rel_240909` ADD PRIMARY KEY (`id`);

ALTER TABLE `dominios` ADD PRIMARY KEY (`dominio`);

ALTER TABLE `dominios_reg` ADD PRIMARY KEY (`id`);

ALTER TABLE `emails_publicidad` ADD PRIMARY KEY (`email`);

ALTER TABLE `emails_pub_noenviar` ADD PRIMARY KEY (`email`);

ALTER TABLE `facturas`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `numero` (`numero`);

ALTER TABLE `failed_jobs`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

ALTER TABLE `gastos` ADD PRIMARY KEY (`id`);

ALTER TABLE `gastos_cuentas` ADD PRIMARY KEY (`id`);

ALTER TABLE `jobs`
ADD PRIMARY KEY (`id`),
ADD KEY `jobs_queue_index` (`queue`);

ALTER TABLE `job_batches` ADD PRIMARY KEY (`id`);

ALTER TABLE `llamadas` ADD PRIMARY KEY (`id`);

ALTER TABLE `migrations` ADD PRIMARY KEY (`id`);

ALTER TABLE `nombres_recados` ADD PRIMARY KEY (`NombreCompleto`);

ALTER TABLE `notificaciones_cobros` ADD PRIMARY KEY (`id`);

ALTER TABLE `pagos_paypal` ADD PRIMARY KEY (`ci`);

ALTER TABLE `password_reset_tokens` ADD PRIMARY KEY (`email`);

ALTER TABLE `por_cobrar`
ADD PRIMARY KEY (`numero`),
ADD KEY `usuario` (`usuario`),
ADD KEY `por_cobrar_contrato_contratos_foreign` (`contrato`),
ADD KEY `hasta` (`hasta`);

ALTER TABLE `por_cobrar_20240727`
ADD PRIMARY KEY (`numero`),
ADD KEY `usuario` (`usuario`),
ADD KEY `por_cobrar_contrato_contratos_foreign` (`contrato`),
ADD KEY `hasta` (`hasta`);

ALTER TABLE `prorrogas` ADD PRIMARY KEY (`id`);

ALTER TABLE `recibos` ADD PRIMARY KEY (`folio`);

ALTER TABLE `relacion_servicios` ADD KEY `proyecto` (`proyecto`);

ALTER TABLE `respuestas` ADD PRIMARY KEY (`id`);

ALTER TABLE `servicios` ADD PRIMARY KEY (`servicio`);

ALTER TABLE `servicios_20240518` ADD PRIMARY KEY (`servicio`);

ALTER TABLE `sessions`
ADD PRIMARY KEY (`id`),
ADD KEY `sessions_user_id_index` (`user_id`),
ADD KEY `sessions_last_activity_index` (`last_activity`);

ALTER TABLE `subscriptions`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `subscriptions_conekta_id_unique` (`conekta_id`),
ADD KEY `subscriptions_user_id_foreign` (`user_id`);

ALTER TABLE `tickets` ADD PRIMARY KEY (`id`);

ALTER TABLE `webhooks`
ADD PRIMARY KEY (`id`),
ADD KEY `cliente_ids_webhooks_clientes` (`cliente_ids`),
ADD KEY `webhooks_cobro_id_foreign` (`cobro_id`);

ALTER TABLE `act_si` MODIFY `id` int(6) NOT NULL AUTO_INCREMENT;

ALTER TABLE `checador` MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `clientes`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `clientes_20250418`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `conekta_customers`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `contratos`
MODIFY `numero` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `contratos_cobranza`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `controlclic_clics`
MODIFY `id` bigint(15) NOT NULL AUTO_INCREMENT;

ALTER TABLE `control_diario`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `control_notas`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `departamentos`
MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `depositos`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `depositos_20240830`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `dep_rel`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `dep_rel_240909`
MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `dominios_reg`
MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `facturas` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `failed_jobs`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `gastos` MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `gastos_cuentas`
MODIFY `id` int(3) NOT NULL AUTO_INCREMENT;

ALTER TABLE `jobs`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `llamadas` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `migrations`
MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `notificaciones_cobros`
MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `pagos_paypal`
MODIFY `ci` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `por_cobrar`
MODIFY `numero` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `por_cobrar_20240727`
MODIFY `numero` int(9) NOT NULL AUTO_INCREMENT;

ALTER TABLE `prorrogas` MODIFY `id` int(7) NOT NULL AUTO_INCREMENT;

ALTER TABLE `recibos`
MODIFY `folio` bigint(12) NOT NULL AUTO_INCREMENT;

ALTER TABLE `respuestas`
MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `subscriptions`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `tickets` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `webhooks`
MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `conekta_customers`
ADD CONSTRAINT `conekta_customers_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `clientes` (`id`) ON DELETE CASCADE;

ALTER TABLE `contratos`
ADD CONSTRAINT `contratos_servicio_servicios_foreign` FOREIGN KEY (`servicio`) REFERENCES `servicios` (`servicio`) ON UPDATE CASCADE,
ADD CONSTRAINT `contratos_usuario_clientes_foreign` FOREIGN KEY (`usuario`) REFERENCES `clientes` (`usuario`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `depositos`
ADD CONSTRAINT `depositos_clientes_foreign` FOREIGN KEY (`id_usuario`) REFERENCES `clientes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `dep_rel`
ADD CONSTRAINT `dep_rel_depositos_foreign` FOREIGN KEY (`id_deposito`) REFERENCES `depositos` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `dep_rel_por_cobrar_foreign` FOREIGN KEY (`id_cobro`) REFERENCES `por_cobrar` (`numero`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `por_cobrar`
ADD CONSTRAINT `por_cobrar_contrato_contratos_foreign` FOREIGN KEY (`contrato`) REFERENCES `contratos` (`numero`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `subscriptions`
ADD CONSTRAINT `subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `clientes` (`id`) ON DELETE CASCADE;

ALTER TABLE `webhooks`
ADD CONSTRAINT `cliente_ids_webhooks_clientes` FOREIGN KEY (`cliente_ids`) REFERENCES `clientes` (`usuario`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `webhooks_por_cobrar_foreign` FOREIGN KEY (`cobro_id`) REFERENCES `por_cobrar` (`numero`) ON DELETE SET NULL ON UPDATE CASCADE;

COMMIT;