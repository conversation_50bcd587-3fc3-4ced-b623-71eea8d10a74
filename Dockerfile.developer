FROM php:8.2-fpm-alpine

# Instalar dependencias necesarias para desarrollo
RUN apk add --no-cache \
    nginx \
    supervisor \
    libpng \
    libjpeg \
    libzip \
    git \
    $PHPIZE_DEPS \
    libpng-dev \
    jpeg-dev \
    libzip-dev \
    oniguruma-dev \
    && docker-php-ext-configure gd --with-jpeg \
    && docker-php-ext-install pdo_mysql zip gd mbstring \
    && mkdir -p /run/nginx \
    && mkdir -p /var/log/supervisor

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar Nginx y PHP
COPY docker/nginx/default.conf /etc/nginx/http.d/default.conf
COPY docker/php/php-dev.ini /usr/local/etc/php/php.ini
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Configurar directorio de trabajo
WORKDIR /var/www/html

# En desarrollo, el código se montará en /var/www/html desde el host
# Preparamos los directorios de almacenamiento para tener los permisos correctos
RUN mkdir -p /var/www/html/storage/app /var/www/html/storage/framework/cache \
    /var/www/html/storage/framework/sessions /var/www/html/storage/framework/views \
    /var/www/html/storage/logs /var/www/html/bootstrap/cache \
    && chown -R www-data:www-data /var/www/html

# Script de inicio para desarrollo
COPY docker/php-dev-start.sh /usr/local/bin/php-dev-start.sh
RUN chmod +x /usr/local/bin/php-dev-start.sh

EXPOSE 80

CMD ["/usr/local/bin/php-dev-start.sh"] 