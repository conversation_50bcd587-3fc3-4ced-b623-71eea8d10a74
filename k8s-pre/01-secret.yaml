apiVersion: v1
kind: Secret
metadata:
  name: pre-landing-laravel-secret
  namespace: mulbin-landing
type: Opaque
stringData:
  APP_KEY: "base64:Yww4gHEHQq9bUdOLPqswPxqXrD+wdZ6RzxfcV0FTdU0="
  # Configuración de Google Maps API Key
  GOOGLE_MAPS_API_KEY: "AIzaSyDgQdlUFnaCbDh_IkzROBQhjrIycG-8eYM"
  # Configuración de Google OAuth
  GOOGLE_CLIENT_ID: "491454724906-6f8fkpoqpboagtqd7futu91r1vclr42v.apps.googleusercontent.com"
  GOOGLE_CLIENT_SECRET: "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"
  GOOGLE_REDIRECT_URI: "https://pre.mulbin.com/auth/google/callback"
  # Configuración de reCAPTCHA
  RECAPTCHA_SITE_KEY: "6Lc1sR4rAAAAAKTeVT-VZkCkhglT1n6kpJ4oJsiw"
  RECAPTCHA_SECRET_KEY: "6Lc1sR4rAAAAAFcZFQ7WksS5CPll-U-KfOzGLEmz"
  # Configuración de Facebook OAuth
  FACEBOOK_CLIENT_ID: "650491671146325"
  FACEBOOK_CLIENT_SECRET: "********************************"
  FACEBOOK_REDIRECT_URI: "https://pre.mulbin.com/auth/facebook/callback"
  # Configuración de Conekta
  CONEKTA_PUBLIC_KEY: "key_HK8PyepcfRNxp5WdrzfsWwQ"
  CONEKTA_PRIVATE_KEY: "key_neq5cxH5UREuP8Tcg0VB7lZ"
  # Configuración de correo
  MAIL_MAILER: "smtp"
  MAIL_SCHEME: "smtp"
  MAIL_HOST: "smtp-relay.brevo.com"
  MAIL_PORT: "587"
  MAIL_USERNAME: "<EMAIL>"
  MAIL_PASSWORD: "xsmtpsib-56281fc4d62a4543971d1c4eab4905517995abf39d8da4056232ac8fee0dbc3f-PCj8TARmwFrW17dD"
  MAIL_ENCRYPTION: "tls"
  # Bases de datos
  PW_DB_HOST: "*************"
  PW_DB_DATABASE: "devPW"
  PW_DB_USERNAME: "publidev"
  PW_DB_PASSWORD: "sipubliweb777"
  SI_DB_HOST: "*************"
  SI_DB_DATABASE: "devSI"
  SI_DB_USERNAME: "publidev"
  SI_DB_PASSWORD: "sipubliweb777"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pre-landing-laravel-config-map
  namespace: mulbin-landing
data:
  APP_NAME: "Multibolsa Inmobiliaria"
  APP_ENV: "development"
  APP_DOMAIN: "pre.mulbin.com"
  APP_URL: "https://pre.mulbin.com"
  MAIL_FROM_NAME: "Multibolsa Inmobiliaria - MULBIN"
  MAIL_FROM_ADDRESS: "<EMAIL>"
  MASTER_WEB_DOMAIN: "web.pre.mulbin.com"
  MASTER_PANEL_DOMAIN: "panel.pre.mulbin.com"
  MULBIN_SUPPORT_EMAIL: "<EMAIL>"
  MULBIN_DOMAIN: "pre.mulbin.com"
  APP_DEBUG: "false"
  APP_LOCALE: "es"
  APP_FAKER_LOCALE: "es_MX"
  DB_CONNECTION: "publiweb"
  FORCE_HTTPS: "true"
  FORCE_LIGHT_MODE: "true"
  SESSION_DOMAIN: "null"
  SESSION_DRIVER: "file"
  SESSION_ENCRYPT: "false"
  SESSION_LIFETIME: "120"
  SESSION_PATH: "/"
---
apiVersion: v1
kind: Secret
metadata:
  name: docker-hub-secret
  namespace: mulbin-landing
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************************************************************************************
