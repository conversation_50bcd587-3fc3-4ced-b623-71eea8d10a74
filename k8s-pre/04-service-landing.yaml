apiVersion: v1
kind: Service
metadata:
  name: pre-landing-laravel-service
  namespace: mulbin-landing
  labels:
    app: pre-landing-laravel
    component: web
    framework: laravel
  annotations:
    # # Anotaciones específicas de Rancher para referenciar workloads directamente
    # field.cattle.io/ipAddresses: "null"
    # field.cattle.io/targetDnsRecordIds: "null"
    # field.cattle.io/targetWorkloadIds: '["deployment:mulbin-landing:pre-landing-laravel"]'
    # Anotaciones de Prometheus
    prometheus.io/scrape: "true"
    prometheus.io/port: "9113"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: pre-landing-laravel
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
    - name: metrics
      protocol: TCP
      port: 9113
      targetPort: 9113
  type: ClusterIP
  sessionAffinity: None
