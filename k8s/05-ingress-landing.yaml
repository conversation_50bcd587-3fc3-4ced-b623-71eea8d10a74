apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: landing-laravel-ingress
  namespace: mulbin-landing
  labels:
    app: landing-laravel
    component: ingress
    framework: laravel
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    # Configuraciones específicas para Laravel
    nginx.ingress.kubernetes.io/rewrite-target: "/"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "8k"
spec:
  rules:
    - host: mulbin.com
      http:
        paths:
          - path: /
            backend:
              serviceName: landing-laravel-service
              servicePort: 80
