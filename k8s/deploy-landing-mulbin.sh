#!/bin/bash

# 🚀 Script de Deployment - Landing Laravel mulbin-landing
# Compatible con Rancher v1.13.10 y Kubernetes 1.13.10

set -e

# Configuración
NAMESPACE="mulbin-landing"
DEPLOYMENT_NAME="landing-laravel"
SERVICE_NAME="landing-laravel-service"
INGRESS_NAME="landing-laravel-ingress"
IMAGE_NAME="publiweb/website2025-mulbin:latest"
HPA_NAME="landing-laravel-hpa"
PVC_NAME="pvc-mulbin-landing-storage"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[✅] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[⚠️] $1${NC}"
}

error() {
    echo -e "${RED}[❌] $1${NC}"
}

# Función para verificar prerequisites
check_prerequisites() {
    log "Verificando prerequisites..."
    
    # Verificar kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl no está instalado"
        exit 1
    fi
    
    # Verificar conexión al cluster
    if ! kubectl cluster-info &> /dev/null; then
        error "No se puede conectar al cluster de Kubernetes"
        exit 1
    fi
    
    # Verificar archivos de configuración
    local required_files=("01-namespace.yaml" "02-pv-landing.yaml" "03-deployment-landing.yaml" "04-service-landing.yaml" "05-ingress-landing.yaml" "06-hpa-landing.yaml" "00-secret.yaml")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Archivo requerido no encontrado: $file"
            exit 1
        fi
    done
    
    success "Prerequisites verificados correctamente"
}

# Función para aplicar configuraciones
apply_configs() {
    log "Aplicando configuraciones de Kubernetes..."
    
    # Aplicar en orden específico
    log "1. Aplicando namespace..."
    kubectl apply -f 01-namespace.yaml
    
    log "2. Aplicando secrets y configmaps..."
    kubectl apply -f 00-secret.yaml
    
    log "3. Aplicando persistent volumes..."
    kubectl apply -f 02-pv-landing.yaml
    
    # Esperar un poco para que el PV esté disponible
    log "Esperando que el Persistent Volume esté disponible..."
    sleep 5
    
    log "4. Aplicando deployment..."
    kubectl apply -f 03-deployment-landing.yaml
    
    log "5. Aplicando service..."
    kubectl apply -f 04-service-landing.yaml
    
    log "6. Aplicando ingress..."
    kubectl apply -f 05-ingress-landing.yaml
    
    log "7. Aplicando HPA..."
    kubectl apply -f 06-hpa-landing.yaml
    
    success "Todas las configuraciones aplicadas correctamente"
}

# Función para verificar el deployment
verify_deployment() {
    log "Verificando estado del deployment..."
    
    # Verificar namespace
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        success "Namespace $NAMESPACE existe"
    else
        error "Namespace $NAMESPACE no encontrado"
        return 1
    fi
    
    # Verificar deployment
    log "Verificando deployment $DEPLOYMENT_NAME..."
    kubectl rollout status deployment/$DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
    
    if [[ $? -eq 0 ]]; then
        success "Deployment $DEPLOYMENT_NAME está corriendo correctamente"
    else
        error "Deployment $DEPLOYMENT_NAME falló"
        return 1
    fi
    
    # Verificar pods
    log "Verificando pods..."
    local pods=$(kubectl get pods -n $NAMESPACE -l app=landing-laravel --no-headers | wc -l)
    if [[ $pods -gt 0 ]]; then
        success "$pods pod(s) corriendo en $NAMESPACE"
        kubectl get pods -n $NAMESPACE -l app=landing-laravel
    else
        error "No hay pods corriendo para $DEPLOYMENT_NAME"
        return 1
    fi
    
    # Verificar service
    if kubectl get service $SERVICE_NAME -n $NAMESPACE &> /dev/null; then
        success "Service $SERVICE_NAME está activo"
    else
        warning "Service $SERVICE_NAME no encontrado"
    fi
    
    # Verificar ingress
    if kubectl get ingress $INGRESS_NAME -n $NAMESPACE &> /dev/null; then
        success "Ingress $INGRESS_NAME está configurado"
        kubectl get ingress $INGRESS_NAME -n $NAMESPACE
    else
        warning "Ingress $INGRESS_NAME no encontrado"
    fi
    
    # Verificar HPA
    if kubectl get hpa $HPA_NAME -n $NAMESPACE &> /dev/null; then
        success "HPA $HPA_NAME está configurado"
        kubectl get hpa $HPA_NAME -n $NAMESPACE
    else
        warning "HPA $HPA_NAME no encontrado"
    fi
}

# Función para mostrar información del deployment
show_deployment_info() {
    log "Información del deployment:"
    echo
    echo "🏷️  Namespace: $NAMESPACE"
    echo "🚀 Deployment: $DEPLOYMENT_NAME"
    echo "🔧 Service: $SERVICE_NAME"
    echo "🌐 Ingress: $INGRESS_NAME"
    echo "📊 HPA: $HPA_NAME"
    echo "🐳 Image: $IMAGE_NAME"
    echo
    
    log "URLs de acceso:"
    echo "🌍 Aplicación: https://mulbin.com"
    echo
    
    log "Comandos útiles:"
    echo "📋 Ver pods: kubectl get pods -n $NAMESPACE"
    echo "📋 Ver logs: kubectl logs -f deployment/$DEPLOYMENT_NAME -n $NAMESPACE"
    echo "📋 Ver eventos: kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp'"
    echo "📋 Escalar: kubectl scale deployment $DEPLOYMENT_NAME --replicas=2 -n $NAMESPACE"
    echo
}

# Función para limpieza (opcional)
cleanup() {
    warning "Iniciando limpieza del deployment..."
    
    kubectl delete ingress $INGRESS_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete hpa $HPA_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete service $SERVICE_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete deployment $DEPLOYMENT_NAME -n $NAMESPACE --ignore-not-found=true
    kubectl delete pvc $PVC_NAME -n $NAMESPACE --ignore-not-found=true
    
    warning "Limpieza completada"
}

# Función principal
main() {
    log "🚀 Iniciando deployment de Landing Laravel en Rancher"
    log "============================================="
    
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            apply_configs
            verify_deployment
            show_deployment_info
            success "🎉 Deployment completado exitosamente!"
            ;;
        "verify")
            verify_deployment
            ;;
        "info")
            show_deployment_info
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "Uso: $0 [deploy|verify|info|cleanup]"
            echo "  deploy  - Ejecuta el deployment completo (default)"
            echo "  verify  - Verifica el estado del deployment"
            echo "  info    - Muestra información del deployment"
            echo "  cleanup - Limpia todos los recursos"
            exit 1
            ;;
    esac
}

# Manejar señales para limpieza
trap 'error "Script interrumpido"; exit 1' INT TERM

# Ejecutar función principal
main "$@" 