apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-mulbin-landing-storage
  labels:
    app: landing-laravel
    environment: production
spec:
  capacity:
    storage: 10Gi # Para sesiones y logs de Laravel - mucho menor que photos
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs
  mountOptions:
    - nolock
    - nfsvers=3
    - tcp
    - intr
    - hard
    - rsize=32768
    - wsize=32768
    - timeo=600
    - retrans=2
  nfs:
    server: 10.138.252.210
    path: /mnt/volume_nodo_1/mulbin-landing/production

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pvc-mulbin-landing-storage
  namespace: mulbin-landing
  labels:
    app: landing-laravel
    environment: production
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: nfs
  resources:
    requests:
      storage: 10Gi
  selector:
    matchLabels:
      app: landing-laravel
      environment: production
