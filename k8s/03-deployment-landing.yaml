apiVersion: apps/v1
kind: Deployment
metadata:
  name: landing-laravel
  namespace: mulbin-landing
  labels:
    app: landing-laravel
    version: v1.0.0
    component: web
    framework: laravel
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: landing-laravel
  template:
    metadata:
      labels:
        app: landing-laravel
        version: v1.0.0
        component: web
        framework: laravel
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9113"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
        - name: dc-publiweb
      containers:
        - name: landing-laravel
          image: publiweb/landing-mulbin:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
            - name: metrics
              containerPort: 9113
              protocol: TCP
          resources:
            requests:
              cpu: "200m"
              memory: "512Mi"
            limits:
              cpu: "500m"
              memory: "1Gi"
          volumeMounts:
            - mountPath: /tmp/nginx-cache
              name: nginx-cache
              # En PVC
            - mountPath: /var/www/html/storage/app/public
              name: laravel-storage
              subPath: storage/app/public
            - mountPath: /var/www/html/storage/app/private
              name: laravel-storage
              subPath: storage/app/private
            - mountPath: /var/www/html/storage/framework/sessions
              name: laravel-storage
              subPath: storage/framework/sessions
            # - mountPath: /var/www/html/storage/framework/views
            #   name: laravel-storage
            #   subPath: storage/framework/views
            - mountPath: /var/www/html/storage/framework/cache
              name: laravel-storage
              subPath: storage/framework/cache
            - mountPath: /var/www/html/storage/logs
              name: laravel-storage
              subPath: storage/logs

          # Variables de entorno desde secrets y configmap
          envFrom:
            - secretRef:
                name: landing-laravel-secret
            - configMapRef:
                name: landing-laravel-config-map
          env:
            - name: NGINX_WORKER_PROCESSES
              value: "auto"
            - name: NGINX_WORKER_CONNECTIONS
              value: "4096"
            - name: PHP_FPM_MAX_CHILDREN
              value: "8"
            - name: ENVIRONMENT
              value: "production"
      volumes:
        # Cache temporal con almacenamiento local de alta velocidad
        - name: nginx-cache
          emptyDir:
            sizeLimit: 512Mi
        # Almacenamiento persistente para Laravel storage (incluye sesiones)
        - name: laravel-storage
          persistentVolumeClaim:
            claimName: pvc-mulbin-landing-storage
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext:
        runAsNonRoot: false
        runAsUser: 0 # root para crear directorios de Laravel
        fsGroup: 0
