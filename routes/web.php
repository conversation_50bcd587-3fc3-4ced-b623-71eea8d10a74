<?php

use App\Http\Controllers\ConektaPlansController;
use App\Http\Controllers\ConektaTestController;
use App\Http\Controllers\CustomFilePreviewController;
use App\Http\Controllers\CustomFileUploadController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SistemaInmobiliarioController;
use App\Http\Controllers\WebhookController;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

// Rutas personalizadas para Livewire (deben estar ANTES que las rutas automáticas de Livewire)
Route::post('livewire/upload-file', [CustomFileUploadController::class, 'handle'])
    ->name('livewire.upload-file')
    ->middleware(['web']);

Route::get('livewire/preview-file/{filename}', [CustomFilePreviewController::class, 'handle'])
    ->name('livewire.preview-file')
    ->middleware(['web']);

// Ruta de home que ahora será la landing page del Sistema Inmobiliario
Route::get('/', [SistemaInmobiliarioController::class, 'index'])->name('home');

// Rutas para políticas de privacidad, eliminación de datos y términos de uso
Route::get('/aviso-de-privacidad', [SistemaInmobiliarioController::class, 'avisoPrivacidad'])->name('privacidad');
Route::get('/eliminacion-de-datos', [SistemaInmobiliarioController::class, 'eliminacionDatos'])->name(
    'eliminacion.datos'
);
Route::get('/terminos-de-uso', [SistemaInmobiliarioController::class, 'terminosUso'])->name('terminos.uso');

// Rutas para el proceso de activación que requieren autenticación
Route::middleware(['auth', 'verified'])->group(function () {
    // Ruta para el formulario de activación
    Route::get('/activar', [SistemaInmobiliarioController::class, 'activar'])->name('activar');

    // Ruta para procesar el formulario de activación
    Route::post('/activar', [SistemaInmobiliarioController::class, 'procesarActivacion'])->name('activar.store');

    // Ruta para la página de confirmación post-activación
    Route::get('/activacion-exitosa', [SistemaInmobiliarioController::class, 'activacionExitosa'])->name(
        'activacion.exitosa'
    );
});

// Rutas para el proceso de cambio de plan que requieren autenticación
Route::middleware(['auth', 'verified'])->group(function () {
    // Ruta para el formulario de cambio de plan
    Route::get('/cambiar-plan', [SistemaInmobiliarioController::class, 'cambiarPlan'])->name('cambiar.plan');

    // Ruta para procesar el formulario de cambio de plan
    Route::post('/cambiar-plan', [SistemaInmobiliarioController::class, 'procesarCambioPlan'])->name(
        'cambiar.plan.store'
    );
});

// Ruta para webhooks de Conekta (sin verificación CSRF)
Route::post('/webhooks/conekta', [WebhookController::class, 'handleConektaWebhook'])
    ->withoutMiddleware([VerifyCsrfToken::class])
    ->name('webhooks.conekta');

// Rutas para pruebas de Conekta
Route::middleware(['auth'])->group(function () {
    Route::get('/conekta-test', [ConektaTestController::class, 'index'])->name('conekta.test');
    Route::post(
        '/conekta-test/create-customer',
        [ConektaTestController::class, 'testCreateCustomer']
    )->name('conekta.test.create-customer');

    // Rutas para gestionar planes de Conekta
    Route::get('/conekta-plans', [ConektaPlansController::class, 'index'])->name('conekta.plans');
    Route::get('/conekta-plans/create', [ConektaPlansController::class, 'createPlans'])->name(
        'conekta.plans.create'
    );

    // Rutas para administrar webhooks
    Route::get('/admin/webhooks', [WebhookController::class, 'index'])->name(
        'admin.webhooks'
    );
    Route::get('/admin/webhooks/{id}', [WebhookController::class, 'show'])->name(
        'admin.webhooks.show'
    );
});

Route::middleware(['auth', 'verified', 'website.activated'])->group(function () {
    // Rutas del dashboard
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/payment', 'settings.payment')->name('settings.payment');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
});

require __DIR__ . '/auth.php';
