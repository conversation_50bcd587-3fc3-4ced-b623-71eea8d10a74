#!/bin/bash

# Colores para una salida más amigable
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Sistema Inmobiliario - Configuración de bases de datos${NC}\n"

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker no está instalado. Por favor, instala Docker primero.${NC}"
    exit 1
fi

# Determinar si usar docker compose o docker-compose
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
    echo -e "${GREEN}Usando nuevo comando 'docker compose'${NC}"
elif docker-compose version &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
    echo -e "${GREEN}Usando comando tradicional 'docker-compose'${NC}"
else
    echo -e "${RED}Docker Compose no está instalado. Por favor, instala Docker Compose primero.${NC}"
    exit 1
fi

# Iniciar los contenedores
echo -e "${GREEN}Iniciando contenedor de MariaDB...${NC}"
$DOCKER_COMPOSE up -d

# Esperar a que MariaDB esté listo
echo -e "${YELLOW}Esperando a que MariaDB esté listo...${NC}"
sleep 10

# Verificar la conexión a la base de datos
echo -e "${GREEN}Verificando conexión a MariaDB...${NC}"
if docker exec si_mariadb mysql -uroot -ppassword -e "SELECT 'Conexión exitosa'" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Conexión a MariaDB establecida correctamente${NC}"
else
    echo -e "${RED}❌ No se pudo conectar a MariaDB. Verifica los logs con '$DOCKER_COMPOSE logs mariadb'${NC}"
    exit 1
fi

# Verificar si las bases de datos existen
echo -e "${GREEN}Verificando bases de datos...${NC}"
if docker exec si_mariadb mysql -uroot -ppassword -e "SHOW DATABASES LIKE 'publiweb'" | grep -q "publiweb"; then
    echo -e "${GREEN}✅ Base de datos 'publiweb' encontrada${NC}"
else
    echo -e "${RED}❌ Base de datos 'publiweb' no encontrada${NC}"
fi

if docker exec si_mariadb mysql -uroot -ppassword -e "SHOW DATABASES LIKE 'sistemainmobiliario'" | grep -q "sistemainmobiliario"; then
    echo -e "${GREEN}✅ Base de datos 'sistemainmobiliario' encontrada${NC}"
else
    echo -e "${RED}❌ Base de datos 'sistemainmobiliario' no encontrada${NC}"
fi

echo -e "\n${YELLOW}Configuración completada.${NC}"
echo -e "${GREEN}Puedes conectarte a MariaDB usando:${NC}"
echo -e "   Host: 127.0.0.1"
echo -e "   Puerto: 3306"
echo -e "   Usuario: root"
echo -e "   Contraseña: password"
echo -e "   Bases de datos: publiweb, sistemainmobiliario" 