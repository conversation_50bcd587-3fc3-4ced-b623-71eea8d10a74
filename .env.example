APP_NAME="Sistema Inmobiliario"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

# Forzar HTTPS para URLs generadas (útil con Cloudflare)
FORCE_HTTPS=false

# OAuth Providers
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI="${APP_URL}/auth/google/callback"

# Facebook OAuth
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
FACEBOOK_REDIRECT_URI="${APP_URL}/auth/facebook/callback"

APP_LOCALE=es
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=es_MX

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Conexión principal para publiweb
PW_DB_HOST=127.0.0.1
PW_DB_PORT=3306
PW_DB_DATABASE=publiweb
PW_DB_USERNAME=root
PW_DB_PASSWORD=password

# Conexión para sistemainmobiliario
SI_DB_CONNECTION=sistemainmobiliario
SI_DB_HOST=127.0.0.1
SI_DB_PORT=3306
SI_DB_DATABASE=sistemainmobiliario
SI_DB_USERNAME=root
SI_DB_PASSWORD=password

# Configuración de base de datos predeterminada (MySQL/MariaDB)
DB_CONNECTION=publiweb
DB_HOST=${PW_DB_HOST}
DB_PORT=${PW_DB_PORT}
DB_DATABASE=${PW_DB_DATABASE}
DB_USERNAME=${PW_DB_USERNAME}
DB_PASSWORD=${PW_DB_PASSWORD}

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

FORCE_LIGHT_MODE=false

# Google reCAPTCHA
RECAPTCHA_SITE_KEY=
RECAPTCHA_SECRET_KEY=

# Conekta API Keys
CONEKTA_PUBLIC_KEY=
CONEKTA_PRIVATE_KEY=
