# Sistema Inmobiliario 2025

Esta es la plataforma web para el Sistema Inmobiliario, la versión 2025.

## Documentación

### Modelos

-   [Modelo User y campo pabusqueda](docs/testing/modelo-user.md) - Documentación sobre el modelo User y el funcionamiento del campo pabusqueda para búsquedas.
-   [Modelo Contrato y campo pabusqueda](docs/testing/modelo-contrato.md) - Documentación sobre el modelo Contrato y el funcionamiento del campo pabusqueda para búsquedas.
-   [Modelo Cobro y campo pabusqueda](docs/testing/modelo-cobro.md) - Documentación sobre el modelo Cobro y el funcionamiento del campo pabusqueda para búsquedas con formato de fechas personalizado.

### Contenedores e Instalación

-   [Documentación Docker](README.docker.md) - Instrucciones para ejecutar el proyecto usando Docker.

## Pruebas

Para ejecutar todas las pruebas del proyecto:

```bash
php artisan test
```

Para ejecutar pruebas específicas:

```bash
# Ejecutar pruebas unitarias
php artisan test --testsuite=Unit

# Ejecutar pruebas de características
php artisan test --testsuite=Feature

# Ejecutar una prueba específica
php artisan test tests/Unit/Models/UserTest.php
```

## Desarrollo

### Requisitos

-   PHP 8.2+
-   Composer
-   Node.js y npm
-   MariaDB

### Instalación local

1. Clonar el repositorio

    ```bash
    <NAME_EMAIL>:tuorganizacion/website2025.git
    cd website2025
    ```

2. Instalar dependencias

    ```bash
    composer install
    npm install
    ```

3. Configurar el entorno

    ```bash
    cp .env.example .env
    php artisan key:generate
    ```

4. Configurar bases de datos

    ```bash
    ./setup-databases.sh
    ```

5. Ejecutar migraciones

    ```bash
    ./migrate.sh
    ```

6. Iniciar el servidor de desarrollo
    ```bash
    php artisan serve
    npm run dev
    ```
