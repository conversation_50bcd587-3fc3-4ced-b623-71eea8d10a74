<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('por_cobrar', function (Blueprint $table) {
            $table->bigInteger('numero', true)->unsigned();
            $table->integer('contrato')->nullable();
            $table->dateTime('fecha');
            $table->string('usuario', 50)->nullable();
            $table->string('servicio', 15);
            $table->integer('cantidad')->default(0);
            $table->string('descripcion', 100);
            $table->string('dominio', 60);
            $table->mediumText('adicional')->nullable();
            $table->decimal('precio', 11, 2)->default(0.00);
            $table->date('desde');
            $table->date('hasta');
            $table->enum('pagado', ['No', 'Si'])->default('No');
            $table->string('factura', 50);
            $table->timestamp('notificacion_pago')->nullable();
            $table->string('forma_pago', 50);
            $table->mediumText('observaciones_pago')->nullable();
            $table->date('vencimiento');
            $table->date('ultima_notificacion')->nullable();
            $table->enum('detener', ['No', 'Si'])->default('No');
            $table->integer('cantidad_pagada')->default(0);
            $table->timestamp('autorizacion_de_pago')->nullable();
            $table->timestamp('real_creado')->useCurrent();
            $table->timestamp('real_modificado')->nullable()->useCurrentOnUpdate();
            $table->string('creado_por', 20);
            $table->string('modificado_por', 20);
            $table->mediumText('pabusqueda')->nullable();
            
            // Índices y relaciones
            $table->index('hasta');
            $table->foreign('contrato')->references('numero')->on('contratos')
                  ->onDelete('set null')->onUpdate('cascade');
            $table->foreign('usuario')->references('usuario')->on('clientes')
                  ->onDelete('set null')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('por_cobrar');
    }
};
