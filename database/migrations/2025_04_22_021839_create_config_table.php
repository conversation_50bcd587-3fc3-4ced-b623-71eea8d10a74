<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('sistemainmobiliario')->create('config', function (Blueprint $table) {
            $table->integer('contrato')->primary();
            $table->string('usuario', 25)->unique();
            $table->string('correo_ventas', 60);
            $table->string('correo_contratacion', 60);
            $table->string('correo_comentarios', 60);
            $table->string('correo_dirgral', 60);
            $table->string('dominio', 50)->unique();
            $table->string('dominio2', 50)->nullable();
            $table->mediumText('redes_sociales')->nullable()->comment('JSON con los usuarios para diferentes redes sociales, se usa par render sobre todo');
            $table->string('ruta', 100);
            $table->integer('inmuebles')->default(99999);
            $table->integer('foto_ancho')->default(320);
            $table->enum('por_ciudades', ['Si', 'No'])->default('Si');
            $table->integer('prop_por_pag')->default(7);
            $table->integer('prop_por_linea')->default(1);
            $table->enum('tipo_tema', ['Generico', 'Personalizado'])->default('Personalizado');
            $table->string('theme', 30)->default('Default');
            $table->string('theme_hash', 100)->nullable()->unique();
            $table->mediumText('theme_dev')->nullable();
            $table->enum('nueva_ventana', ['Si', 'No'])->default('Si');
            $table->enum('ligas_comunes', ['Si', 'No'])->default('Si');
            $table->timestamp('ultimo_acceso')->nullable();
            $table->enum('combinar_ampi', ['Si', 'No'])->default('No');
            $table->enum('notifica_telefono', ['Si', 'No'])->default('Si');
            $table->enum('telefono_props', ['Si', 'No'])->default('No');
            $table->enum('transparencia_fotos', ['Si', 'No'])->default('Si');
            $table->enum('tipo_fotos', ['galeria', 'navegador', 'ambos'])->default('ambos');
            $table->integer('fotos_por_linea')->default(4);
            $table->enum('tipo_galeria', ['dinamica', 'estatica'])->default('dinamica');
            $table->enum('socio_ampi', ['Si', 'No'])->default('No');
            $table->integer('funcion_ampi')->default(0);
            $table->enum('ampi_admin', ['Si', 'No'])->default('No');
            $table->string('estado', 50);
            $table->enum('orden_inmuebles', ['precio', 'colonia', 'fecha', 'claves'])->default('fecha');
            $table->enum('forma_orden_inmuebles', ['ascendente', 'descendente'])->default('descendente');
            $table->enum('enviar_nextel', ['Si', 'No'])->default('Si');
            $table->enum('fotos', ['purplehaze', 'otro'])->default('otro');
            $table->char('moneda_predeterminada', 3)->default('MXP');
            $table->string('mostrar_monedas', 250)->default('MXP,USD');
            $table->enum('idioma', ['esp', 'ing'])->default('esp');
            $table->integer('campos_por_linea')->default(2);
            $table->enum('leyenda_b_favoritos', ['Si', 'No'])->default('Si');
            $table->enum('lat_club_negocios', ['Si', 'No'])->default('Si');
            $table->date('ultimo_cobro')->nullable();
            $table->integer('dias_novedades')->default(30);
            $table->enum('notificar_propietarios', ['Si', 'No'])->default('No');
            $table->enum('detalles_new', ['Si', 'No'])->default('No');
            $table->tinyInteger('status')->default(1)->comment('Determina el status del contrato');
            $table->string('servicio_contratado', 15)->default('SI-M-7001');
            $table->date('pagado_hasta')->nullable();
            $table->enum('vencido_si', ['Si', 'No'])->default('No');
            $table->date('vencimiento')->nullable();
            $table->date('prorroga')->nullable();
            $table->decimal('saldo', 9, 2)->default(0.00);
            $table->decimal('adeudo', 9, 2)->default(0.00);
            $table->decimal('a_vencer', 9, 2)->default(0.00);
            $table->decimal('saldo_a_favor', 9, 2)->default(0.00);
            $table->decimal('credito', 9, 2)->default(0.00);
            $table->date('msg_panel_desactivado')->nullable();
            $table->enum('presentar_con', ['fotos', 'tour'])->default('fotos');
            $table->enum('front', ['0', '1'])->default('0')->comment('Determina si tiene front (website) activo o inactivo el contrato');
            $table->integer('deptID_livephp')->nullable()->unique();
            $table->mediumText('custom_data')->nullable()->comment('JSON con estructura de datos a renderizar en los templates');
            $table->mediumText('external_connections')->nullable()->comment('JSON con los API_KEY de servicios de externos (portales, etc)');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable()->useCurrentOnUpdate();
            
            // Índices adicionales
            $table->index('dominio2');
            $table->index(['status', 'pagado_hasta', 'prorroga'], 'activos');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('sistemainmobiliario')->dropIfExists('config');
    }
};
