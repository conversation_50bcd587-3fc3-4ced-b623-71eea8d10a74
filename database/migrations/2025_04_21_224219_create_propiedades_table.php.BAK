<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('sistemainmobiliario')->create('propiedades', function (Blueprint $table) {
            $table->bigInteger('clave_sistema')->primary();
            $table->string('usuario', 25);
            $table->integer('contrato')->nullable();
            $table->integer('status_id')->default(1)->nullable();
            $table->string('claveprop', 25);
            $table->string('nombreprop', 50);
            $table->unsignedTinyInteger('tipo')->default(0)->zerofill();
            $table->enum('residencial', ['Si', 'No'])->default('No');
            $table->enum('comercial', ['Si', 'No'])->default('No');
            $table->enum('industrial', ['Si', 'No'])->default('No');
            $table->enum('vacacional', ['Si', 'No'])->default('No');
            $table->integer('precio_venta')->default(0);
            $table->integer('precio_renta')->default(0);
            $table->integer('precio_diaria')->default(0);
            $table->integer('precio_traspaso')->default(0);
            $table->decimal('precio_venta_mxp', 15, 5)->nullable();
            $table->decimal('precio_vta_total_mxp', 15, 5)->nullable();
            $table->decimal('precio_renta_mxp', 15, 5)->nullable();
            $table->decimal('precio_diaria_mxp', 15, 5)->nullable();
            $table->decimal('precio_traspaso_mxp', 15, 5)->nullable();
            $table->integer('id_colonia')->nullable();
            $table->string('colonia', 70)->nullable();
            $table->string('muestra_colonia', 30);
            $table->string('zona', 30)->nullable();
            $table->mediumText('intro_corta_esp')->nullable();
            $table->mediumText('intro_corta_ing')->nullable();
            $table->mediumText('anuncio_esp')->nullable();
            $table->mediumText('anuncio_ing')->nullable();
            $table->mediumText('anuncio_fra')->nullable();
            $table->mediumText('caract_esp')->nullable();
            $table->mediumText('caract_ing')->default('');
            $table->mediumText('caract_fra')->default('');
            $table->string('ciudad', 70)->nullable();
            $table->string('provincia', 70)->nullable();
            $table->string('pais', 30)->default('México');
            $table->char('moneda', 3);
            $table->enum('enventa', ['Si', 'No'])->default('No');
            $table->enum('enrenta', ['Si', 'No'])->default('No');
            $table->enum('endiaria', ['Si', 'No'])->default('No');
            $table->enum('entraspaso', ['Si', 'No'])->default('No');
            $table->enum('precio_por_metro', ['Si', 'No'])->default('No');
            $table->timestamp('fecha_ingreso')->useCurrent();
            $table->unsignedInteger('codigo_postal')->default(0)->zerofill();
            $table->string('operacion_hecha', 10);
            $table->dateTime('fecha_modificaciones');
            $table->mediumText('eventos')->nullable()->comment('JSON que describe la historia del inmueble');
            $table->integer('contador')->default(0);
            $table->date('fecha_expiracion');
            $table->string('i_calle_numero', 100);
            $table->string('i_num_ext', 10)->nullable();
            $table->string('i_num_int', 10)->nullable();
            $table->string('i_entre_calles', 100)->default('');
            $table->enum('publica_ubicacion', ['interno', 'bolsa', 'web'])->default('interno');
            $table->integer('i_propietario')->default(0);
            $table->mediumText('i_observaciones')->nullable();
            $table->mediumText('i_inventario')->nullable();
            $table->decimal('comision_ampi', 5, 2)->default(0.00);
            $table->integer('sucursal')->nullable();
            $table->decimal('comparto_comision', 5, 2)->default(0.00);
            $table->integer('asesor')->default(0);
            $table->decimal('i_porcentaje_comision', 5, 2)->default(0.00);
            $table->enum('tipo_promocion', ['DE PALABRA', 'CARTA AUTORIZACION', 'EN OPCION', 'EN EXCLUSIVA'])->nullable();
            $table->enum('i_requisito_renta', ['Fianza', 'Fiador', 'Negociable'])->default('Fiador');
            $table->integer('desarrollo')->default(0);
            $table->integer('aid')->default(0);
            $table->string('keywords_esp', 150);
            $table->string('keywords_ing', 150);
            $table->string('keywords_fra', 150);
            $table->enum('en_resumen', ['Si', 'No'])->default('No');
            $table->enum('presentar_con', ['tour', 'fotos'])->default('fotos');
            $table->enum('status_web', ['publicado', 'sin publicar'])->default('publicado');
            $table->enum('i_tipo_comision_rta', ['1 mes', 'cantidad fija'])->default('1 mes');
            $table->integer('i_comision_rta')->default(0);
            $table->enum('t_comision_ampi', ['sobre comision', 'sobre precio'])->default('sobre precio');
            $table->enum('t_comparto_comision', ['sobre comision', 'sobre precio'])->default('sobre precio');
            $table->decimal('rta_comparto_comision', 5, 2)->default(0.00);
            $table->decimal('rta_comision_ampi', 5, 2)->default(0.00);
            $table->date('fin_contrato');
            $table->enum('ampi_plus', ['Si', 'No', 'Ya'])->default('No');
            $table->date('conf_portal_ampi')->nullable();
            $table->date('msg_conf_portal_ampi')->nullable();
            $table->string('clave_externa', 100)->nullable();
            $table->string('map_lng', 30)->nullable();
            $table->string('map_lat', 30)->nullable();
            $table->mediumText('notas_mapa')->nullable();
            $table->string('key_str', 50)->nullable()->unique();
            
            // Índices
            $table->index('contrato');
            $table->index('tipo');
            $table->index('colonia');
            $table->index('asesor');
            $table->index('status_id');
            $table->index(['comparto_comision', 'enventa'], 'cmp_vta');
            $table->index(['rta_comparto_comision', 'enrenta'], 'cmp_rta');
            $table->index(['clave_sistema', 'operacion_hecha'], 'id_op');
            $table->index(['clave_sistema', 'contrato', 'status_id', 'fecha_expiracion'], 'idx_owner');
            $table->index(['enventa', 'precio_venta'], 'idx_venta');
            $table->index(['enrenta', 'precio_renta'], 'idx_renta');
            $table->index(['endiaria', 'precio_diaria'], 'idx_diaria');
            $table->index(['clave_sistema', 'contrato', 'status_id'], 'idx_partners');
            $table->index([
                'clave_sistema', 'contrato', 'operacion_hecha', 'comparto_comision', 
                'rta_comparto_comision', 'enventa', 'enrenta', 'endiaria', 'status_web', 
                'fecha_expiracion', 'precio_venta', 'precio_renta', 'precio_diaria'
            ], 'propiedades_index');
            
            // Claves foráneas
            $table->foreign('contrato')->references('contrato')->on('config')
                 ->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('id_colonia')->references('id')->on('new_colonias')
                 ->onDelete('set null')->onUpdate('cascade');
            $table->foreign('status_id')->references('id')->on('prop_status')
                 ->onDelete('set null')->onUpdate('cascade');
            $table->foreign('sucursal')->references('id')->on('sucursales')
                 ->onDelete('set null')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('sistemainmobiliario')->dropIfExists('propiedades');
    }
};
