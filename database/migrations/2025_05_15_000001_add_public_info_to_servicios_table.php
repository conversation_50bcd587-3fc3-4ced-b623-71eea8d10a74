<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->table('servicios', function (Blueprint $table) {
            // Se guardará data en JSON
            $table->mediumText('public_info')->nullable()->after('tipo');
            // Orden en que se presentan
            $table->integer('orden')->default(999)->after('public_info');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->table('servicios', function (Blueprint $table) {
            $table->dropColumn('public_info');
            $table->dropColumn('orden');
        });
    }
};
