<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('depositos', function (Blueprint $table) {
            $table->bigInteger('id', true)->unsigned();
            $table->bigInteger('id_usuario')->unsigned()->default(0);
            $table->string('banco', 20)->default('');
            $table->decimal('cantidad', 11, 2)->default(0.00);
            $table->dateTime('fecha_deposito');
            $table->dateTime('fecha_registro');
            $table->mediumText('observaciones')->nullable();
            $table->enum('verificado', ['Si', 'No'])->default('Si');
            $table->integer('item_paypal')->nullable();
            $table->string('movimiento', 25)->nullable();
            $table->string('user_admin', 25)->nullable();
            $table->integer('factura_mbi')->nullable();
            $table->text('api_response')->nullable()->comment('Respuesta de la API de la plataforma en donde se hace el cargo en línea');
            
            // Índices
            $table->foreign('id_usuario')->references('id')->on('clientes')
                ->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('depositos');
    }
};
