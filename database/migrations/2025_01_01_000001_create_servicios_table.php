<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('servicios', function (Blueprint $table) {
            $table->string('servicio', 15)->primary();
            $table->string('descripcion', 100)->nullable();
            $table->decimal('precio', 11, 2)->default(0.00);
            $table->longText('observaciones')->nullable();
            $table->integer('notifica_vencimiento')->default(0)->comment('Días de anticipación para comenzar a notificar al cliente antes del vencimiento del servicio');
            $table->integer('notifica_cada')->default(0)->comment('Intervalo en días entre notificaciones sucesivas para recordar al cliente sobre el vencimiento de un servicio');
            $table->enum('cobro_automatico', ['Si', 'No'])->default('Si');
            $table->enum('cobro_mensual', ['Si', 'No'])->default('Si');
            $table->decimal('desc_trimestral', 4, 2)->default(0.00);
            $table->decimal('desc_semestral', 4, 2)->default(0.00);
            $table->decimal('desc_anual', 4, 2)->default(0.00);
            $table->integer('renovacion_cada')->default(0);
            $table->integer('inmuebles')->default(0);
            $table->string('publicado', 15)->nullable();
            $table->enum('acepta_prorrogas', ['Si', 'No'])->default('No');
            $table->integer('NotificandoVencidos')->default(0);
            $table->integer('QueHacerVencido')->default(0);
            $table->string('tipo', 10)->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable()->useCurrentOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('servicios');
    }
}; 