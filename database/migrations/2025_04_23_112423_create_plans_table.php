<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('conekta_plan_id')->nullable()->unique(); // ID del plan en Conekta, nullable para planes que no van a Conekta
            $table->string('name'); // Nombre del plan
            $table->integer('amount'); // Monto en centavos
            $table->string('currency', 3)->default('MXN'); // Moneda
            $table->string('interval')->default('month'); // Intervalo de cobro
            $table->integer('frequency')->default(1); // Frecuencia del intervalo
            $table->foreignId('service_id')->nullable()->constrained('servicios')->onDelete('set null')->onUpdate('cascade'); // Relación con servicios
            $table->boolean('is_active')->default(true); // Estado del plan
            $table->text('description')->nullable(); // Descripción opcional del plan
            $table->timestamps();
            $table->softDeletes(); // Para mantener historial de planes eliminados
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
