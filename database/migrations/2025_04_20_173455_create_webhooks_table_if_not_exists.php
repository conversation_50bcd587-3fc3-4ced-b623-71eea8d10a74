<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('webhooks')) {
            Schema::create('webhooks', function (Blueprint $table) {
                $table->id();
                $table->string('cliente_ids', 50)->nullable();
                $table->unsignedBigInteger('cobro_id')->nullable();
                $table->string('origin', 150)->nullable()->comment('Desde donde se genera este WebHook');
                $table->string('type', 100)->nullable()->comment('Tipo de webhook, ej. order.created | charge.paid');
                $table->string('id_item', 250)->nullable();
                $table->mediumText('data')->nullable()->comment('Generalmente el POST con el cual es invocado');
                $table->timestamp('created_at')->useCurrent();

                // Índices
                $table->index('cliente_ids');
                $table->index('cobro_id');

                // Relaciones
                $table->foreign('cliente_ids')
                    ->references('usuario')
                    ->on('clientes')
                    ->onDelete('set null')
                    ->onUpdate('cascade');

                $table->foreign('cobro_id')
                    ->references('numero')
                    ->on('por_cobrar')
                    ->onDelete('set null')
                    ->onUpdate('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en down() porque no queremos eliminar la tabla si ya existía
        // Si la tabla fue creada por esta migración, se eliminará en un rollback completo
    }
};
