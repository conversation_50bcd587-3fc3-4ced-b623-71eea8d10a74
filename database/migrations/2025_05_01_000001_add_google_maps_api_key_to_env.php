<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\File;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Verificar si la variable GOOGLE_MAPS_API_KEY ya existe en el archivo .env
        if (!env('GOOGLE_MAPS_API_KEY')) {
            // Agregar la variable al archivo .env
            $envFile = base_path('.env');
            
            if (File::exists($envFile)) {
                $content = File::get($envFile);
                
                // Agregar la variable al final del archivo
                $content .= "\n# Google Maps API Key para Places Autocomplete\nGOOGLE_MAPS_API_KEY=\n";
                
                File::put($envFile, $content);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es necesario revertir este cambio
    }
};
