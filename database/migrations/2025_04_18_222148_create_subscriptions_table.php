<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('clientes')->onDelete('cascade');
            $table->string('conekta_id')->unique(); // ID de la suscripción de Conekta
            $table->string('conekta_plan'); // ID del plan de Conekta
            $table->string('conekta_customer'); // ID del cliente en Conekta
            $table->string('status'); // Estado de la suscripción: active, past_due, canceled, paused, etc.
            $table->timestamp('trial_ends_at')->nullable(); // Fin del periodo de prueba
            $table->timestamp('ends_at')->nullable(); // Fecha de finalización (si está cancelada)
            $table->string('billing_period')->nullable(); // Período de facturación: monthly, quarterly, biannual, annual
            $table->decimal('plan_price', 10, 2); // Precio mensual del plan
            $table->decimal('total_price', 10, 2); // Precio total a pagar según periodo
            $table->boolean('personalization')->default(false); // Si pidió personalización (opcional)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
