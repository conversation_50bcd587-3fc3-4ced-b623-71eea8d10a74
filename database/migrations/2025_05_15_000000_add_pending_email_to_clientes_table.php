<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->table('clientes', function (Blueprint $table) {
            $table->string('pending_email', 60)->nullable()->after('email');
            $table->timestamp('pending_email_at')->nullable()->after('pending_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->table('clientes', function (Blueprint $table) {
            $table->dropColumn('pending_email');
            $table->dropColumn('pending_email_at');
        });
    }
};
