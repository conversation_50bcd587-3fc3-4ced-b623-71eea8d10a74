<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('clientes', function (Blueprint $table) {
            $table->id();
            $table->string('usuario', 50)->unique()->nullable();
            $table->string('logmail', 75)->unique()->nullable();
            $table->string('password', 100)->default('');
            $table->string('remember_token', 100)->nullable();
            $table->string('google_id')->nullable();
            $table->string('google_token')->nullable();
            $table->string('google_refresh_token')->nullable();
            $table->string('facebook_id')->nullable();
            $table->string('facebook_token')->nullable();
            $table->integer('as_cnt')->nullable()->comment('Número de contrato de S.I. al que este usuario pertenece como asesor inmobiliario');
            $table->string('name')->nullable();
            $table->string('nombre', 100);
            $table->string('apellidos', 100)->default('');
            $table->string('ocupacion', 25)->default('');
            $table->string('empresa', 50)->default('');
            $table->string('calle_numero', 70)->default('');
            $table->string('colonia', 30)->default('');
            $table->integer('codigo_postal')->default(0);
            $table->string('ciudad', 30)->default('');
            $table->string('estado', 25)->default('');
            $table->string('pais', 25)->default('');
            $table->string('code_alpha2', 5)->default('es-MX')->comment('Código del país (es-MX)');
            $table->mediumText('como_llegar')->nullable();
            $table->string('telefono', 30)->default('');
            $table->string('phone_country_code', 5)->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->string('celular', 30)->default('');
            $table->string('nextel_tel', 10)->default('');
            $table->string('nextel_radio', 15)->default('');
            $table->string('fax', 15)->default('');
            $table->string('email', 60)->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('website_activated_at')->nullable();
            $table->string('avatar')->nullable();
            $table->string('email_sec', 60)->default('');
            $table->string('sitio_web', 60)->default('');
            $table->string('fact_nombre', 100)->default('');
            $table->string('fact_domicilio', 150)->default('');
            $table->string('fact_rfc', 15)->default('');
            $table->enum('activo', ['Si', 'No'])->default('Si');
            $table->mediumText('observaciones')->nullable();
            $table->string('quien_registro', 50)->default('');
            $table->string('conekta_id')->unique()->nullable()->comment('Customer ID en Conekta');
            $table->mediumText('pabusqueda')->default('');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('clientes');
    }
};
