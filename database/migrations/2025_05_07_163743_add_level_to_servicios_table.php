<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('servicios', function (Blueprint $table) {
            // Añadir campo level como entero con valor predeterminado 0
            $table->integer('level')->default(0)->after('tipo')
                  ->comment('Nivel jerárquico del plan (1=FREE, 2=MS, 3=PRO, 4=PLUS, etc.)');
        });

        // Asignar niveles predeterminados a los planes existentes
        DB::table('servicios')->where('servicio', 'SI-FREE')->update(['level' => 1]);
        DB::table('servicios')->where('servicio', 'SI-MS')->update(['level' => 2]);
        DB::table('servicios')->where('servicio', 'SI-PRO')->update(['level' => 3]);
        DB::table('servicios')->where('servicio', 'SI-PLUS')->update(['level' => 4]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('servicios', function (Blueprint $table) {
            $table->dropColumn('level');
        });
    }
};
