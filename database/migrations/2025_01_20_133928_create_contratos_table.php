<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('contratos', function (Blueprint $table) {
            $table->integer('numero', true);
            $table->string('usuario', 50);
            $table->tinyInteger('status')->default(1);
            $table->string('servicio', 15)->nullable();
            $table->string('dominio', 60);
            $table->string('s_usuario', 25);
            $table->string('s_password', 15);
            $table->mediumText('observaciones')->nullable();
            $table->integer('forma_pago')->default(1);
            $table->decimal('en_precio', 9, 2)->default(0.00);
            $table->enum('por_adelantado', ['Si', 'No'])->default('Si');
            $table->integer('dias_tregua')->default(0);
            $table->integer('cobro_dia')->default(0);
            $table->tinyInteger('cobro_siguiente')->default(0)
                ->comment(
                    'Número de días para realizar el siguiente cobro relacionado al contrato, 0 hace el cobro el día del vencimiento, 1 un día antes..., -1 dejará un cobro pendiente de pago con inicio después de now()'
                );
            $table->tinyInteger('pago_automatizado')->default(0);
            $table->date('cobranza_generada')->nullable()
                ->comment('Fecha en que se hace cobranza al contrato');
            $table->enum('tipo', ['empresarial', 'particular'])->default('particular');
            $table->timestamp('fecha')->nullable();
            $table->timestamp('pagado_hasta')->nullable();
            $table->timestamp('prorroga')->nullable();
            $table->text('history')->nullable();
            $table->mediumText('pabusqueda')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable()->useCurrentOnUpdate();

            // Índices y relaciones
            $table->foreign('usuario')->references('usuario')->on('clientes')
                ->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('servicio')->references('servicio')->on('servicios')
                ->onDelete('set null')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('contratos');
    }
};
