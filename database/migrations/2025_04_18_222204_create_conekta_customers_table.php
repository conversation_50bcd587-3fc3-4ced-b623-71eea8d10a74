<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conekta_customers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('clientes')->onDelete('cascade');
            $table->string('conekta_id')->unique(); // ID del cliente en Conekta
            $table->json('payment_sources')->nullable(); // Fuentes de pago guardadas (tarjetas)
            $table->string('default_payment_source_id')->nullable(); // ID del método de pago predeterminado
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conekta_customers');
    }
};
