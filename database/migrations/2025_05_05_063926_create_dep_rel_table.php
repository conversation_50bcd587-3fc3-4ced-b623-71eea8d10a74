<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('publiweb')->create('dep_rel', function (Blueprint $table) {
            $table->bigInteger('id', true)->unsigned();
            $table->bigInteger('id_deposito')->unsigned();
            $table->bigInteger('id_cobro')->unsigned();
            
            // Índices y claves foráneas
            $table->foreign('id_deposito', 'dep_rel_depositos_foreign')
                ->references('id')
                ->on('depositos')
                ->onDelete('cascade')
                ->onUpdate('cascade');
                
            $table->foreign('id_cobro', 'dep_rel_por_cobrar_foreign')
                ->references('numero')
                ->on('por_cobrar')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('publiweb')->dropIfExists('dep_rel');
    }
};
