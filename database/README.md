# Configuración de Múltiples Bases de Datos

Este proyecto utiliza dos bases de datos separadas:

1. **publiweb**: Base de datos principal para la mayoría de los modelos
2. **sistemainmobiliario**: Base de datos secundaria para modelos específicos, como el modelo `Propiedad`

## Configuración con Docker (Recomendado)

La forma más sencilla de configurar el entorno de desarrollo es usando Docker:

1. Asegúrate de tener Docker instalado en tu sistema.

2. Usa el script de ayuda para configurar automáticamente:

```bash
./setup-databases.sh
```

Este script detectará automáticamente si debes usar `docker compose` (macOS/versiones recientes) o `docker-compose` (versiones antiguas).

Alternativamente, puedes iniciar manualmente el contenedor:

```bash
# En macOS reciente o Docker Desktop (recomendado)
docker compose up -d

# En versiones antiguas de Docker
docker-compose up -d
```

El contenedor creará automáticamente las bases de datos al iniciar. El archivo `.env` ya está configurado para conectarse a este servidor de base de datos.

## Configuración Manual (Alternativa)

Si prefieres no usar Docker, puedes configurar manualmente:

1. Instala MariaDB 10.6.15 en tu sistema.

2. Crea las bases de datos manualmente:

```bash
mysql -u root -p < database/setup-local-databases.sql
```

3. Asegúrate de que las credenciales en `.env` coincidan con tu configuración local.

## Estructura de Modelos

Los modelos están organizados para usar la conexión correcta automáticamente:

-   Modelos de `publiweb`: Heredan de `PubliwebModel`
-   Modelos de `sistemainmobiliario`: Heredan de `SistemaInmobiliarioModel`

## Migraciones

Para ejecutar migraciones en bases de datos específicas:

```bash
# Migraciones para la base de datos predeterminada (publiweb)
php artisan migrate

# Migraciones para la base de datos sistemainmobiliario
php artisan migrate --database=sistemainmobiliario
```

## Crear Nuevos Modelos

Al crear nuevos modelos, asegúrate de extender la clase base correcta:

```php
// Para modelos en publiweb
class NuevoModelo extends PubliwebModel
{
    // ...
}

// Para modelos en sistemainmobiliario
class NuevoModeloSI extends SistemaInmobiliarioModel
{
    // ...
}
```
