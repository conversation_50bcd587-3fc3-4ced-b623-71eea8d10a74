-- Script para configurar las bases de datos locales para desarrollo
-- <PERSON>ste script se ejecuta automáticamente cuando se inicia el contenedor de Docker

-- Crear base de datos publiweb
CREATE DATABASE IF NOT EXISTS `publiweb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- <PERSON>rear base de datos sistemainmobiliario
CREATE DATABASE IF NOT EXISTS `sistemainmobiliario` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario para desarrollo (opcional, también puedes usar root)
-- CREATE USER IF NOT EXISTS 'sidev'@'%' IDENTIFIED BY 'sidev';
-- GRANT ALL PRIVILEGES ON publiweb.* TO 'sidev'@'%';
-- GRANT ALL PRIVILEGES ON sistemainmobiliario.* TO 'sidev'@'%';
-- FLUSH PRIVILEGES; 