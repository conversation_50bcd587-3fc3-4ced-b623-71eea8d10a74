<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Ejecutar los seeders
        $this->call([
            // ServiciosSeeder::class, // Primero crear los servicios
            // PlansSeeder::class,     // Luego crear los planes que dependen de los servicios
        ]);
    }
}
