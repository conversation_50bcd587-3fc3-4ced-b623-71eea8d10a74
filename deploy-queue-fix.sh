#!/bin/bash

# Script para desplegar la solución de colas en producción
# Uso: ./deploy-queue-fix.sh

set -e

echo "🚀 DESPLEGANDO SOLUCIÓN DE COLAS EN PRODUCCIÓN"
echo "============================================="

# Verificar que estamos en el directorio correcto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: No se encontró docker-compose.yml"
    echo "Ejecuta este script desde el directorio raíz del proyecto"
    exit 1
fi

# Mostrar cambios que se van a aplicar
echo ""
echo "📝 CAMBIOS QUE SE VAN A APLICAR:"
echo "• Configuración de supervisor para queue workers"
echo "• Variables de entorno para colas en docker-compose"
echo "• Logging específico para colas"
echo "• Comandos de diagnóstico"

# Confirmar despliegue
read -p "¿Continuar con el despliegue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Despliegue cancelado"
    exit 1
fi

# Reconstruir imagen de producción
echo ""
echo "🔨 RECONSTRUYENDO IMAGEN DE PRODUCCIÓN..."
if [ -f "buildPush.prod.sh" ]; then
    chmod +x buildPush.prod.sh
    ./buildPush.prod.sh
else
    echo "⚠️  buildPush.prod.sh no encontrado, reconstruyendo manualmente..."
    docker build -f Dockerfile.production -t publiweb/website2025-mulbin:latest .
fi

# Detener servicios
echo ""
echo "⏹️  DETENIENDO SERVICIOS..."
docker-compose down

# Iniciar servicios
echo ""
echo "▶️  INICIANDO SERVICIOS..."
docker-compose up -d

# Esperar a que los servicios estén listos
echo ""
echo "⏳ ESPERANDO A QUE LOS SERVICIOS ESTÉN LISTOS..."
sleep 30

# Verificar estado de supervisor
echo ""
echo "🔍 VERIFICANDO ESTADO DE SUPERVISOR..."
CONTAINER_NAME="website2025-website2025-1"
docker exec $CONTAINER_NAME supervisorctl status

# Verificar estado de las colas
echo ""
echo "🔍 VERIFICANDO ESTADO DE LAS COLAS..."
docker exec $CONTAINER_NAME php artisan queue:status

# Probar envío de email
echo ""
echo "📧 PROBANDO ENVÍO DE EMAIL..."
docker exec $CONTAINER_NAME php artisan tinker --execute="
\$user = App\Models\User::where('email', '!=', null)->first();
if (\$user) {
    echo 'Enviando email de prueba a: ' . \$user->email . PHP_EOL;
    \$user->sendEmailVerificationNotification();
    echo 'Email encolado exitosamente!' . PHP_EOL;
} else {
    echo 'No se encontró usuario para prueba' . PHP_EOL;
}
"

# Verificar que el email se procesó
echo ""
echo "🔍 VERIFICANDO PROCESAMIENTO DEL EMAIL..."
sleep 5
docker exec $CONTAINER_NAME php artisan queue:status

# Mostrar logs recientes
echo ""
echo "📋 LOGS RECIENTES DEL WORKER:"
docker exec $CONTAINER_NAME tail -n 10 /var/log/supervisor/laravel-worker.log

echo ""
echo "✅ DESPLIEGUE COMPLETADO!"
echo ""
echo "🛠️  COMANDOS ÚTILES PARA MONITOREO:"
echo "• Monitoreo completo: ./docker/monitor-queues.sh"
echo "• Estado de colas: docker exec $CONTAINER_NAME php artisan queue:status"
echo "• Logs en tiempo real: docker exec $CONTAINER_NAME tail -f /var/log/supervisor/laravel-worker.log"
echo "• Reiniciar workers: docker exec $CONTAINER_NAME supervisorctl restart laravel-worker:*"
echo "• Probar registro: docker exec $CONTAINER_NAME php artisan test:user-registration"
echo ""
echo "🎉 Los emails de verificación ahora se enviarán automáticamente!" 