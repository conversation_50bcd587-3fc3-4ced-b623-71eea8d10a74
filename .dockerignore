# Archivos de Git
.git
# .gitignore
# .gitattributes

# Documentación
README.md
README.docker.md
DESARROLLO.md
OAUTH_IMPROVEMENTS.md
docs/
*.md

# Archivos de configuración de IDE y editores
.vscode/
.idea/
.fleet/
.nova/
.zed/
.phpactor.json
# *.swp
# *.swo
# *~

# Archivos de sistema
.DS_Store
Thumbs.db

# Archivos de entorno y configuración local
.env
.env.*
!.env.example
.phpunit.result.cache
/.phpunit.cache

# Dependencias (se instalarán en el contenedor)
vendor/
node_modules/
# composer.lock
# package-lock.json

# Archivos de build y cache local
/public/build/
/public/hot/
/public/storage/
/storage/*.key
/storage/pail/
/storage/logs/
/storage/framework/cache/
/storage/framework/sessions/
/storage/framework/testing/
# /storage/framework/views/

# # Archivos de pruebas y desarrollo
# /tests/
# phpunit.xml
# Pest.php
# .phpunit.cache

# # Archivos de Homestead
# Homestead.json
# Homestead.yaml

# # Logs
# npm-debug.log*
# yarn-debug.log*
# yarn-error.log*

# # Archivos temporales
# *.log
# *.tmp
# *.temp

# Archivos de Docker (evitar recursión)
Dockerfile*
docker-compose*.yml
.dockerignore
.docker-volumes/

# Archivos de deployment específicos del proyecto
k8s/
buildPush.*.sh
deploy-*.sh
migrate.sh
setup-*.sh
vite-build.sh

# Directorios específicos del proyecto que no deben ir al contenedor
_repoToBuild/
photos-properties/
mariaDB/
inversionistas/
html/
examples/

# Archivos de configuración de herramientas de desarrollo
auth.json
.fleet
.nova
.phpactor.json

# GitHub Actions
.github/

# Archivos de configuración específicos del entorno local
docker-compose.override.yml 