# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Testing
- `php artisan test` - Run all tests (uses Pest testing framework)
- `php artisan test --testsuite=Unit` - Run unit tests only
- `php artisan test --testsuite=Feature` - Run feature tests only
- `php artisan test tests/Unit/Models/UserTest.php` - Run specific test file

### Code Quality
- `./vendor/bin/pint` - Laravel Pint code formatting (equivalent to running `php artisan pint`)

### Asset Building
- `npm run build` - Build assets for production using Vite
- `npm run dev` - Start Vite development server with hot reload
- `npm run watch` - Watch for file changes and rebuild assets

### Development Server
- `composer dev` - Start all development services concurrently (server, queue, logs, vite)
- `php artisan serve` - Start Laravel development server only
- `php artisan queue:listen --tries=1` - Start queue worker
- `php artisan pail --timeout=0` - Start log viewer

### Database
- `./migrate.sh` - Run database migrations
- `./setup-databases.sh` - Setup local databases

## Architecture Overview

### Framework & Stack
- **Laravel 12** with **Livewire** and **Volt** for reactive components
- **Tailwind CSS 4** for styling via Vite plugin
- **Alpine.js** for JavaScript interactivity
- **Pest** testing framework
- **MariaDB** databases (multiple connections)

### Database Architecture
- **Multi-database setup**: Uses separate connections for different data contexts
- **Primary connection**: `publiweb` - main application data including users (`clientes` table)
- **Models use custom table/connection mappings** - User model maps to `clientes` table on `publiweb` connection

### Authentication System
- **Custom authentication**: User model extends `Authenticatable` with `MustVerifyEmail`
- **Legacy password compatibility**: Supports both modern bcrypt and legacy MD5+salt format
- **OAuth integration**: Google and Facebook social login support
- **Custom login field**: Uses `logmail` instead of standard email field

### Key Models & Relationships
- **User** (maps to `clientes` table): Core user model with complex business logic
- **Contrato**: User contracts with services
- **Cobro**: Billing/charges system
- **ConektaCustomer/Subscription**: Payment processing integration
- **SIConfig**: User website configuration

### Business Logic Features
- **Multi-tenant website system**: Users get subdomains and custom domains
- **Payment integration**: Conekta payment processing with subscriptions
- **Search optimization**: `pabusqueda` field auto-updated for full-text search
- **Website activation**: Complex logic determining if user's website is active

### File Structure
- `app/Livewire/` - Livewire components for reactive UI
- `app/Services/` - Business logic services (payments, sync, etc.)
- `app/Traits/` - Reusable model traits
- `resources/views/` - Blade templates (minimal, mostly Livewire)
- `routes/web.php` - Web routes
- `routes/auth.php` - Authentication routes

### Development Patterns
- **Livewire-first approach**: Most UI interactions through Livewire components
- **Service layer pattern**: Complex business logic abstracted to service classes
- **Trait-based functionality**: Common model behaviors extracted to traits
- **Multi-database relationships**: Models reference different database connections

### Docker & Deployment
- **Multi-stage Dockerfiles**: Separate dev/prod configurations
- **Kubernetes manifests**: `/k8s/` and `/k8s-pre/` directories
- **Queue processing**: Background job handling with supervisord
- **Asset optimization**: Vite for modern asset pipeline