[PHP]
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 64M
post_max_size = 64M
expose_php = Off
display_errors = Off
display_startup_errors = Off
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
log_errors = On
error_log = /proc/self/fd/2
default_charset = "UTF-8"
date.timezone = UTC

; Optimizaciones
realpath_cache_size = 4096K
realpath_cache_ttl = 600

[opcache]
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.validate_timestamps=0
opcache.revalidate_freq=0
opcache.save_comments=1
opcache.enable_file_override=1
opcache.huge_code_pages=1

[session]
session.cookie_httponly = 1
session.use_strict_mode = 1 