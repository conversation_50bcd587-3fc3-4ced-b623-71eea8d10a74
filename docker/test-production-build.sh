#!/bin/bash

echo "🔨 Construyendo imagen de producción..."
echo "======================================="

# Construir la imagen
docker build -f Dockerfile.production -t website2025:test-prod .

if [ $? -ne 0 ]; then
    echo "❌ Error: Falló la construcción de la imagen"
    exit 1
fi

echo "✅ Imagen construida exitosamente"
echo ""

echo "🧪 Probando la imagen..."
echo "========================"

# Ejecutar un contenedor temporal para probar
CONTAINER_ID=$(docker run -d --name website2025-test -p 8888:80 website2025:test-prod)

if [ $? -ne 0 ]; then
    echo "❌ Error: No se pudo iniciar el contenedor"
    exit 1
fi

echo "✅ Contenedor iniciado (ID: $CONTAINER_ID)"
echo "🔗 Accede a: http://localhost:8888"
echo ""

# Esperar a que el contenedor esté listo
echo "⏳ Esperando a que el contenedor esté listo..."
sleep 10

# Verificar que el contenedor está ejecutándose
if ! docker ps | grep -q website2025-test; then
    echo "❌ Error: El contenedor no está ejecutándose"
    docker logs website2025-test
    docker rm -f website2025-test
    exit 1
fi

# Verificar extensiones PHP
echo "🔍 Verificando extensiones PHP..."
docker exec website2025-test php -m | grep -E "(curl|gd|mbstring|pdo_mysql|zip|opcache|intl|exif)"

# Verificar que Nginx responde
echo "🌐 Verificando que Nginx responde..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8888)

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "302" ]; then
    echo "✅ Nginx está respondiendo correctamente (HTTP $HTTP_CODE)"
else
    echo "❌ Error: Nginx no está respondiendo correctamente (HTTP $HTTP_CODE)"
    docker logs website2025-test
    docker rm -f website2025-test
    exit 1
fi

echo ""
echo "🎉 ¡Prueba exitosa! La imagen de producción funciona correctamente."
echo ""
echo "📋 Siguientes pasos:"
echo "   1. Prueba la aplicación en: http://localhost:8888"
echo "   2. Si todo está bien, detén el contenedor con:"
echo "      docker rm -f website2025-test"
echo "   3. Construye la imagen final para producción con:"
echo "      docker build -f Dockerfile.production -t website2025:prod ."
echo ""
echo "🧹 Para limpiar la imagen de prueba:"
echo "   docker rmi website2025:test-prod" 