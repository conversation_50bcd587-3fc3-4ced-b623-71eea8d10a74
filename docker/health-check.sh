#!/bin/bash

# Health Check Script para Sistema de Colas
# Uso: ./docker/health-check.sh [--alert] [--email <EMAIL>]

CONTAINER_NAME="website2025-website2025-1"
ALERT_MODE=false
EMAIL_RECIPIENT=""
EXIT_CODE=0

# Procesar argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        --alert)
            ALERT_MODE=true
            shift
            ;;
        --email)
            EMAIL_RECIPIENT="$2"
            shift 2
            ;;
        *)
            echo "Uso: $0 [--alert] [--email <EMAIL>]"
            exit 1
            ;;
    esac
done

# Función para logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Función para alertas
alert() {
    if [ "$ALERT_MODE" = true ]; then
        log "🚨 ALERTA: $1"
        if [ -n "$EMAIL_RECIPIENT" ]; then
            echo "ALERTA Sistema de Colas: $1" | mail -s "ALERTA: Sistema de Colas" "$EMAIL_RECIPIENT"
        fi
    fi
}

log "🔍 Iniciando Health Check del Sistema de Colas"

# 1. Verificar que el contenedor esté corriendo
log "Verificando contenedor..."
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    alert "Contenedor $CONTAINER_NAME no está corriendo"
    EXIT_CODE=1
else
    log "✅ Contenedor está corriendo"
fi

# 2. Verificar estado de supervisor
log "Verificando supervisor..."
SUPERVISOR_STATUS=$(docker exec $CONTAINER_NAME supervisorctl status 2>/dev/null)
if [ $? -ne 0 ]; then
    alert "No se puede conectar a supervisor"
    EXIT_CODE=1
else
    # Verificar workers específicos
    WORKER_STATUS=$(echo "$SUPERVISOR_STATUS" | grep "laravel-worker")
    if echo "$WORKER_STATUS" | grep -q "STOPPED\|FATAL"; then
        alert "Algunos workers están caídos: $WORKER_STATUS"
        EXIT_CODE=1
    else
        WORKER_COUNT=$(echo "$WORKER_STATUS" | grep -c "RUNNING")
        log "✅ $WORKER_COUNT workers corriendo"
    fi
fi

# 3. Verificar estado de las colas
log "Verificando estado de colas..."
QUEUE_STATUS=$(docker exec $CONTAINER_NAME php artisan queue:status 2>/dev/null)
if [ $? -ne 0 ]; then
    alert "No se puede obtener estado de colas"
    EXIT_CODE=1
else
    # Extraer números de jobs
    PENDING_JOBS=$(echo "$QUEUE_STATUS" | grep "Jobs pendientes" | grep -o '[0-9]\+' | head -1)
    FAILED_JOBS=$(echo "$QUEUE_STATUS" | grep "Jobs fallidos" | grep -o '[0-9]\+' | head -1)
    
    # Verificar límites
    if [ "$PENDING_JOBS" -gt 100 ]; then
        alert "Demasiados jobs pendientes: $PENDING_JOBS"
        EXIT_CODE=1
    elif [ "$PENDING_JOBS" -gt 50 ]; then
        log "⚠️  Advertencia: $PENDING_JOBS jobs pendientes"
    else
        log "✅ Jobs pendientes: $PENDING_JOBS"
    fi
    
    if [ "$FAILED_JOBS" -gt 10 ]; then
        alert "Demasiados jobs fallidos: $FAILED_JOBS"
        EXIT_CODE=1
    elif [ "$FAILED_JOBS" -gt 5 ]; then
        log "⚠️  Advertencia: $FAILED_JOBS jobs fallidos"
    else
        log "✅ Jobs fallidos: $FAILED_JOBS"
    fi
fi

# 4. Verificar jobs atascados
log "Verificando jobs atascados..."
STUCK_JOBS=$(docker exec $CONTAINER_NAME php -r "
    require '/var/www/html/bootstrap/app.php';
    \$app = \$app ?? new Illuminate\Foundation\Application(realpath('/var/www/html'));
    \$app->singleton(Illuminate\Contracts\Http\Kernel::class, App\Http\Kernel::class);
    \$app->singleton(Illuminate\Contracts\Console\Kernel::class, App\Console\Kernel::class);
    \$app->singleton(Illuminate\Contracts\Debug\ExceptionHandler::class, App\Exceptions\Handler::class);
    \$kernel = \$app->make(Illuminate\Contracts\Console\Kernel::class);
    \$kernel->bootstrap();
    
    \$count = DB::table('jobs')
        ->where('created_at', '<', now()->subMinutes(15))
        ->count();
    echo \$count;
" 2>/dev/null)

if [ $? -eq 0 ] && [ "$STUCK_JOBS" -gt 0 ]; then
    alert "$STUCK_JOBS jobs llevan más de 15 minutos en cola"
    EXIT_CODE=1
else
    log "✅ No hay jobs atascados"
fi

# 5. Verificar conectividad de base de datos
log "Verificando conectividad de base de datos..."
DB_CHECK=$(docker exec $CONTAINER_NAME php artisan tinker --execute="
try {
    DB::connection('publiweb')->getPdo();
    echo 'OK';
} catch (Exception \$e) {
    echo 'ERROR: ' . \$e->getMessage();
}
" 2>/dev/null)

if [[ "$DB_CHECK" == *"ERROR"* ]]; then
    alert "Error de conexión a base de datos: $DB_CHECK"
    EXIT_CODE=1
else
    log "✅ Conexión a base de datos OK"
fi

# 6. Verificar logs recientes por errores
log "Verificando logs recientes..."
RECENT_ERRORS=$(docker exec $CONTAINER_NAME tail -100 /var/log/supervisor/laravel-worker.log 2>/dev/null | grep -i "error\|exception\|fatal" | wc -l)
if [ "$RECENT_ERRORS" -gt 5 ]; then
    alert "$RECENT_ERRORS errores encontrados en logs recientes"
    EXIT_CODE=1
else
    log "✅ Logs sin errores críticos"
fi

# 7. Verificar uso de memoria (si es posible)
log "Verificando uso de memoria..."
MEMORY_USAGE=$(docker exec $CONTAINER_NAME ps aux | grep "artisan queue:work" | grep -v grep | awk '{sum += $6} END {print sum/1024}' 2>/dev/null)
if [ -n "$MEMORY_USAGE" ] && [ "$(echo "$MEMORY_USAGE > 800" | bc 2>/dev/null)" -eq 1 ]; then
    alert "Alto uso de memoria en workers: ${MEMORY_USAGE}MB"
    EXIT_CODE=1
else
    log "✅ Uso de memoria normal"
fi

# Resumen final
log "===================="
if [ $EXIT_CODE -eq 0 ]; then
    log "✅ Health Check EXITOSO - Sistema funcionando correctamente"
else
    log "❌ Health Check FALLIDO - Se detectaron problemas"
    if [ "$ALERT_MODE" = true ]; then
        alert "Health Check del sistema de colas falló - Revisar logs"
    fi
fi

log "Comandos útiles para diagnóstico:"
log "• Monitoreo completo: ./docker/monitor-queues.sh"
log "• Estado de colas: docker exec $CONTAINER_NAME php artisan queue:status"
log "• Logs en tiempo real: docker exec $CONTAINER_NAME tail -f /var/log/supervisor/laravel-worker.log"
log "• Reiniciar workers: docker exec $CONTAINER_NAME supervisorctl restart laravel-worker:*"

exit $EXIT_CODE 