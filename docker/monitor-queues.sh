#!/bin/bash

# Script de monitoreo de colas para producción
# Uso: ./docker/monitor-queues.sh

echo "=== MONITOREO DE COLAS - $(date) ==="

# Verificar si el contenedor está corriendo
CONTAINER_NAME="website2025-website2025-1"
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ ERROR: Contenedor $CONTAINER_NAME no está corriendo"
    exit 1
fi

echo "✅ Contenedor está corriendo"

# Verificar estado de supervisor
echo ""
echo "=== ESTADO DE SUPERVISOR ==="
docker exec $CONTAINER_NAME supervisorctl status

# Verificar estado de las colas
echo ""
echo "=== ESTADO DE LAS COLAS ==="
docker exec $CONTAINER_NAME php artisan queue:status

# Verificar logs recientes del worker
echo ""
echo "=== LOGS RECIENTES DEL WORKER ==="
docker exec $CONTAINER_NAME tail -n 20 /var/log/supervisor/laravel-worker.log

# Verificar si hay jobs atascados (más de 10 minutos)
echo ""
echo "=== VERIFICANDO JOBS ATASCADOS ==="
STUCK_JOBS=$(docker exec $CONTAINER_NAME php -r "
    require '/var/www/html/bootstrap/app.php';
    \$app = \$app ?? new Illuminate\Foundation\Application(realpath('/var/www/html'));
    \$app->singleton(Illuminate\Contracts\Http\Kernel::class, App\Http\Kernel::class);
    \$app->singleton(Illuminate\Contracts\Console\Kernel::class, App\Console\Kernel::class);
    \$app->singleton(Illuminate\Contracts\Debug\ExceptionHandler::class, App\Exceptions\Handler::class);
    \$kernel = \$app->make(Illuminate\Contracts\Console\Kernel::class);
    \$kernel->bootstrap();
    
    \$count = DB::table('jobs')
        ->where('created_at', '<', now()->subMinutes(10))
        ->count();
    echo \$count;
")

if [ "$STUCK_JOBS" -gt 0 ]; then
    echo "⚠️  ADVERTENCIA: $STUCK_JOBS jobs llevan más de 10 minutos en cola"
    echo "💡 Considera reiniciar el worker: docker exec $CONTAINER_NAME supervisorctl restart laravel-worker:*"
else
    echo "✅ No hay jobs atascados"
fi

echo ""
echo "=== COMANDOS ÚTILES ==="
echo "• Ver logs en tiempo real: docker exec $CONTAINER_NAME tail -f /var/log/supervisor/laravel-worker.log"
echo "• Reiniciar worker: docker exec $CONTAINER_NAME supervisorctl restart laravel-worker:*"
echo "• Estado detallado: docker exec $CONTAINER_NAME php artisan queue:status"
echo "• Procesar cola manualmente: docker exec $CONTAINER_NAME php artisan queue:work --once" 