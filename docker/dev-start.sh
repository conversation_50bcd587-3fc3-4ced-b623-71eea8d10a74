#!/bin/sh

# Instalar dependencias si no están instaladas
if [ ! -d "vendor" ]; then
    echo "Instalando dependencias de Composer..."
    composer install
fi

if [ ! -d "node_modules" ]; then
    echo "Instalando dependencias de Node..."
    npm install
fi

# Asegurar permisos correctos
chown -R www-data:www-data storage bootstrap/cache

# Generar clave de aplicación si no existe
if [ ! -f ".env" ]; then
    cp .env.example .env
    php artisan key:generate
fi

# Iniciar PHP-FPM y Nginx a través de Supervisor
/usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf &

# Iniciar el servidor Vite para desarrollo del frontend
echo "Iniciando Vite para hot-reload del frontend..."
npm run dev &

# Mantener el contenedor en ejecución
echo "Servidor en ejecución. Presiona Ctrl+C para detener."
tail -f /dev/null 