#!/bin/sh

# Optimizaciones Laravel para producción
cd /var/www/html

# Asegurar permisos correctos (por si acaso)
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Verificar que la APP_KEY existe, generarla si no
if [ -z "$APP_KEY" ] && ! grep -q "APP_KEY=" .env; then
    echo "Generando clave de aplicación..."
    php artisan key:generate --force
fi

# Crear enlace simbólico para storage si no existe
if [ ! -L "public/storage" ]; then
    echo "Creando enlace simbólico para storage..."
    php artisan storage:link
fi

# Optimizaciones para producción
echo "Aplicando optimizaciones de Laravel para producción..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Verificar que las extensiones necesarias están instaladas
echo "Verificando extensiones PHP..."
php -m | grep -E "(curl|gd|mbstring|pdo_mysql|zip|opcache|intl)" || echo "⚠️ Algunas extensiones podrían estar faltando"

echo "🚀 Iniciando servicios de producción..."
# Iniciar supervisord que manejará PHP-FPM y Nginx
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf 