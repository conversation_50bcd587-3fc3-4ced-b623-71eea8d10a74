#!/bin/sh

# Instalar dependencias si no están instaladas
if [ ! -d "vendor" ]; then
    echo "Instalando dependencias de Composer..."
    composer install
fi

# Asegurar permisos correctos
chown -R www-data:www-data storage bootstrap/cache

# Generar clave de aplicación si no existe
if [ ! -f ".env" ]; then
    cp .env.example .env
    php artisan key:generate
fi

# Ejecutar migraciones si es necesario
if [ "$RUN_MIGRATIONS" = "true" ]; then
    php artisan migrate --force
fi

# Limpiar caché de configuración
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Iniciar PHP-FPM y Nginx a través de Supervisor
/usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf &

# Mantener el contenedor en ejecución
echo "Servidor en ejecución. Presiona Ctrl+C para detener."
tail -f /dev/null 