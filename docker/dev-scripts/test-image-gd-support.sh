#!/bin/bash

echo "🧪 Probando soporte GD en la imagen construida..."

# Ejecutar el script de verificación GD en la imagen
docker run --rm publiweb/php82-nginx-dev:laravel-250628 php -r "
echo '=== Verificación de soporte GD para imágenes ===' . PHP_EOL . PHP_EOL;

// Verificar si GD está instalado
if (!extension_loaded('gd')) {
    echo '❌ La extensión GD NO está instalada' . PHP_EOL;
    exit(1);
}

echo '✅ La extensión GD está instalada' . PHP_EOL . PHP_EOL;

// Verificar soporte para diferentes formatos
\$formats = [
    'JPEG' => 'imagecreatefromjpeg',
    'PNG' => 'imagecreatefrompng', 
    'GIF' => 'imagecreatefromgif',
    'WebP' => 'imagecreatefromwebp'
];

foreach (\$formats as \$format => \$function) {
    if (function_exists(\$function)) {
        echo '✅ Soporte para ' . \$format . ' disponible' . PHP_EOL;
    } else {
        echo '❌ Soporte para ' . \$format . ' NO disponible' . PHP_EOL;
    }
}

echo PHP_EOL . '=== Información adicional ===' . PHP_EOL;
echo 'Versión GD: ' . gd_info()['GD Version'] . PHP_EOL;

\$gdInfo = gd_info();
echo 'JPEG Support: ' . (\$gdInfo['JPEG Support'] ? 'Sí' : 'No') . PHP_EOL;
echo 'PNG Support: ' . (\$gdInfo['PNG Support'] ? 'Sí' : 'No') . PHP_EOL;
echo 'GIF Read Support: ' . (\$gdInfo['GIF Read Support'] ? 'Sí' : 'No') . PHP_EOL;
echo 'GIF Create Support: ' . (\$gdInfo['GIF Create Support'] ? 'Sí' : 'No') . PHP_EOL;
echo 'WebP Support: ' . (\$gdInfo['WebP Support'] ? 'Sí' : 'No') . PHP_EOL;

echo PHP_EOL . '✅ Verificación completada' . PHP_EOL;
"

echo ""
echo "🎯 Si ves '✅ Soporte para JPEG disponible' y 'JPEG Support: Sí', entonces la imagen está correctamente configurada." 