<?php

echo "=== Verificación de soporte GD para imágenes ===\n\n";

// Verificar si GD está instalado
if (!extension_loaded('gd')) {
    echo "❌ La extensión GD NO está instalada\n";
    exit(1);
}

echo "✅ La extensión GD está instalada\n\n";

// Verificar soporte para diferentes formatos
$formats = [
    'JPEG' => 'imagecreatefromjpeg',
    'PNG' => 'imagecreatefrompng',
    'GIF' => 'imagecreatefromgif',
    'WebP' => 'imagecreatefromwebp'
];

foreach ($formats as $format => $function) {
    if (function_exists($function)) {
        echo "✅ Soporte para {$format} disponible\n";
    } else {
        echo "❌ Soporte para {$format} NO disponible\n";
    }
}

echo "\n=== Información adicional ===\n";
echo "Versión GD: " . gd_info()['GD Version'] . "\n";

$gdInfo = gd_info();
echo "JPEG Support: " . ($gdInfo['JPEG Support'] ? 'Sí' : 'No') . "\n";
echo "PNG Support: " . ($gdInfo['PNG Support'] ? 'Sí' : 'No') . "\n";
echo "GIF Read Support: " . ($gdInfo['GIF Read Support'] ? 'Sí' : 'No') . "\n";
echo "GIF Create Support: " . ($gdInfo['GIF Create Support'] ? 'Sí' : 'No') . "\n";
echo "WebP Support: " . ($gdInfo['WebP Support'] ? 'Sí' : 'No') . "\n";

echo "\n✅ Verificación completada\n";
