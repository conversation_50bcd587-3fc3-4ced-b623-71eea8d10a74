#!/bin/bash

echo "🔄 Reconstruyendo contenedores con soporte JPEG completo..."

# Detener contenedores actuales
echo "📦 Deteniendo contenedores actuales..."
docker-compose down

# Reconstruir imágenes
echo "🔨 Reconstruyendo imágenes Docker..."
docker-compose build --no-cache

# Iniciar contenedores
echo "🚀 Iniciando contenedores..."
docker-compose up -d

# Esperar un momento para que los servicios estén listos
echo "⏳ Esperando que los servicios estén listos..."
sleep 10

# Verificar soporte GD
echo "🔍 Verificando soporte GD..."
docker-compose exec app php docker/dev-scripts/check-gd-support.php

echo "✅ Reconstrucción completada!"
echo "💡 Si ves errores de soporte JPEG, asegúrate de que el contenedor se haya reconstruido correctamente." 