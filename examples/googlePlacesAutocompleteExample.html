<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="utf-8"/>
  <title>Google Places Autocomplete MX - Campos separados</title>

  <!-- 1️⃣  Estilos -->
  <style>
      :root {
          color-scheme: light dark;
      }

      /* Soporta modo oscuro */
      body {
          font-family: system-ui, -apple-system, Roboto, "Helvetica Neue", Arial;
          background: #f1f5f9;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
      }

      form {
          background: #fff;
          padding: 2rem 2.5rem;
          border-radius: 1rem;
          box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
          width: 100%;
          max-width: 520px;
      }

      .field {
          margin-bottom: 1.25rem;
      }

      label {
          display: block;
          font-weight: 600;
          margin-bottom: 0.4rem;
          color: #1e293b;
      }

      input {
          width: 100%;
          font-size: 1rem;
          padding: 0.7rem 1rem;
          border: 1px solid #cbd5e1;
          border-radius: 0.5rem;
          transition: border-color 0.2s;
      }

      input:focus {
          outline: none;
          border-color: #2563eb;
          box-shadow: 0 0 0 3px rgb(37 99 235 / 20%);
      }

      .note {
          font-size: 0.875rem;
          color: #475569;
          margin-top: 0rem;
          margin-bottom: 1.25rem;
      }
  </style>
</head>
<body>
<form autocomplete="off">
  <!-- Campo de búsqueda con Autocomplete -->
  <div class="field">
    <label for="search">Busca tu domicilio (México)</label>
    <input id="search" type="text" placeholder="Empieza a escribir…"/>
    <p class="note">Resultados restringidos a la República Mexicana.</p>
  </div>

  <!-- Campos separados que se rellenarán automáticamente -->
  <div class="field">
    <label for="street">Calle y número</label>
    <input
        id="street"
        name="street"
        type="text"
        placeholder="Ej. Av. Juárez 123"
    />
  </div>

  <div class="field">
    <label for="city">Ciudad</label>
    <input id="city" name="city" type="text" placeholder="Ej. Puebla"/>
  </div>

  <div class="field">
    <label for="state">Estado</label>
    <input id="state" name="state" type="text" placeholder="Ej. Puebla"/>
  </div>

  <div class="field">
    <label for="country">País</label>
    <input id="country" name="country" type="text" placeholder="México"/>
  </div>

  <div class="field">
    <label for="postal">Código postal</label>
    <input id="postal" name="postal" type="text" placeholder="Ej. 72000"/>
  </div>
</form>

<!-- 2️⃣  Carga de la API -->
<script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDHJ6wYwZCjQx5QaZRIZ_cjYD9NsLGcNX0&libraries=places&callback=initAutocomplete"
    async
    defer
></script>

<!-- 3️⃣  Lógica JavaScript -->
<script>
    function initAutocomplete() {
        const inputSearch = document.getElementById("search");

        const autocomplete = new google.maps.places.Autocomplete(inputSearch, {
            types: ["address"],
            componentRestrictions: {country: "mx"}, // 🇲🇽
            fields: ["address_components"], // Solo lo necesario
        });

        autocomplete.addListener("place_changed", () => {
            const place = autocomplete.getPlace();
            if (!place.address_components) return;

            // Vacía los campos antes de rellenar
            ["street", "city", "state", "country", "postal"].forEach(
                (id) => (document.getElementById(id).value = "")
            );

            /*  Mapeo de componentes → IDs del formulario  */
            const map = {
                street_number: {id: "street", fmt: "%v %s"}, // número + ruta
                route: {id: "street", fmt: "%v %s"},
                locality: {id: "city"},
                administrative_area_level_1: {id: "state"},
                country: {id: "country"},
                postal_code: {id: "postal"},
            };

            /*  Recorre cada componente de la respuesta  */
            let street = {number: "", route: ""};

            place.address_components.forEach((c) => {
                const type = c.types[0];
                if (!map[type]) return;

                // Calle: combinamos número y ruta después
                if (type === "street_number") street.number = c.short_name;
                if (type === "route") street.route = c.long_name;

                if (type !== "street_number" && type !== "route") {
                    document.getElementById(map[type].id).value = c.long_name;
                }
            });

            /* Combina calle + número */
            if (street.route || street.number) {
                document.getElementById("street").value = `${street.route || ""} ${
                    street.number || ""
                }`.trim();
            }
        });
    }
</script>
</body>
</html>
