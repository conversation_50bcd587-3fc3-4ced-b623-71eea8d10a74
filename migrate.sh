#!/bin/bash

# Colores para una salida más amigable
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Sistema Inmobiliario - Ejecución de migraciones${NC}\n"

# Limpiar la caché de la aplicación
echo -e "${GREEN}Limpiando caché...${NC}"
php artisan config:clear
php artisan cache:clear

# Verificar si hay conexión a la base de datos publiweb
echo -e "${GREEN}Verificando conexión a la base de datos publiweb...${NC}"
if php artisan db:monitor --connection=publiweb | grep -q "Connection OK"; then
    echo -e "${GREEN}✅ Conexión a publiweb establecida${NC}"
else
    echo -e "${RED}❌ No se pudo conectar a la base de datos publiweb. Verifica tu configuración en .env${NC}"
    exit 1
fi

# Verificar si hay conexión a la base de datos sistemainmobiliario
echo -e "${GREEN}Verificando conexión a la base de datos sistemainmobiliario...${NC}"
if php artisan db:monitor --connection=sistemainmobiliario | grep -q "Connection OK"; then
    echo -e "${GREEN}✅ Conexión a sistemainmobiliario establecida${NC}"
else
    echo -e "${RED}❌ No se pudo conectar a la base de datos sistemainmobiliario. Verifica tu configuración en .env${NC}"
    exit 1
fi

# Ejecutar las migraciones para publiweb
echo -e "${YELLOW}Ejecutando migraciones para publiweb...${NC}"
php artisan migrate --database=publiweb --path=database/migrations

# Ejecutar las migraciones para sistemainmobiliario
echo -e "${YELLOW}Ejecutando migraciones para sistemainmobiliario...${NC}"
php artisan migrate --database=sistemainmobiliario --path=database/migrations

echo -e "\n${GREEN}Proceso de migración completado.${NC}" 