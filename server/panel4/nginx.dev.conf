# /etc/nginx/nginx.conf

user nginx;

# Set number of worker processes automatically based on number of CPU cores.
worker_processes auto;

# Enables the use of JIT for regular expressions to speed-up their processing.
pcre_jit on;

# Configures default error logger.
error_log /var/log/nginx/error.log warn;

# Includes files with directives to load dynamic modules.
include /etc/nginx/modules/*.conf;


events {
    # The maximum number of simultaneous connections that can be opened by
    # a worker process.
    worker_connections 1024;
}

http {
    # Includes mapping of file name extensions to MIME types of responses
    # and defines the default type.
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Name servers used to resolve names of upstream servers into addresses.
    # It's also needed when using tcpsocket and udpsocket in Lua modules.
    #resolver ************** **************;

    # Don't tell nginx version to clients.
    server_tokens off;

    # Specifies the maximum accepted body size of a client request, as
    # indicated by the request header Content-Length. If the stated content
    # length is greater than this size, then the client receives the HTTP
    # error code 413. Set to 0 to disable.
    client_max_body_size 64M;

    # Timeout for keep-alive connections. Server will close connections after
    # this time.
    keepalive_timeout 65;

    # Sendfile copies data between one FD and other from within the kernel,
    # which is more efficient than read() + write().
    sendfile on;

    # Don't buffer data-sends (disable Nagle algorithm).
    # Good for sending frequent small bursts of data in real time.
    tcp_nodelay on;

    # Causes nginx to attempt to send its HTTP response head in one packet,
    # instead of using partial frames.
    tcp_nopush on;

    server_names_hash_bucket_size 2500;

    # Path of the file with Diffie-Hellman parameters for EDH ciphers.
    #ssl_dhparam /etc/ssl/nginx/dh2048.pem;
    # Specifies that our cipher suits should be preferred over client ciphers.
    ssl_prefer_server_ciphers on;

    # Enables a shared SSL cache with size that can hold around 8000 sessions.
    ssl_session_cache shared:SSL:2m;


    # Enable gzipping of responses.
    #gzip on;
    # Set the Vary HTTP header as defined in the RFC 2616.
    gzip_vary on;

    # Enable checking the existence of precompressed files.
    #gzip_static on;

    # Specifies the main log format.
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    # Sets the path, format, and configuration for a buffered log write.
    access_log /var/log/nginx/access.log main;

    # === CONFIGURACIÓN UPSTREAM PARA SERVICIOS ===
    # Upstream para el servicio de fotos
    upstream photos {
        server photos:80;
        keepalive 32;
    }

    # Upstream para MSI v5
    upstream msi-v5 {
        server msi-v5:80;
        keepalive 32;
    }

    # Includes virtual hosts configs.
    include /etc/nginx/conf.d/*.conf;
}