server {
    listen 80 default_server;
    listen [::]:80 default_server;

    server_name panel4;

    # auth_basic "Access with password!";
    # auth_basic_user_file /etc/nginx/.htpasswd;

    root /var/www/html;
    index index.php;

    location / {
        try_files $uri $uri/ /rewrites.php?$query_string;
        # rewrite ^/$ /si/;
    }

    # Configuración para uploads de archivos grandes
    client_max_body_size 64M;

    # Configuración para sesiones de larga duración como Facebook
    # Tiempo de vida extendido para keepalive
    keepalive_timeout 300;
    client_body_timeout 300;
    client_header_timeout 300;

    # add_header X-Frame-Options "SAMEORIGIN";
    # add_header X-Content-Type-Options "nosniff";

    charset utf-8;

    location /si {
        alias "/var/www/html";
        index index.php;
        location ~ \.php$ {
            fastcgi_pass php-fpm;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
        }
    }

    # photos - Usa el mismo auth-session que msi-v5
    location /photos {
        auth_request /auth-session;

        # HEADERS QUE PHOTOS NECESITA (estructura como msi-v5)
        auth_request_set $auth_cnt $upstream_http_x_auth_cnt;
        auth_request_set $auth_user $upstream_http_x_auth_user;
        auth_request_set $auth_llv $upstream_http_x_auth_llv;
        auth_request_set $auth_sessid $upstream_http_x_auth_sessid;

        # EXACTAMENTE IGUAL QUE MSI-V5 pero con headers de photos
        proxy_set_header X-Auth-Cnt $auth_cnt;
        proxy_set_header X-Auth-User $auth_user;
        proxy_set_header X-Auth-Llv $auth_llv;
        proxy_set_header X-Auth-Sessid $auth_sessid;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Host $http_host;

        # Configuración para preservar cookies de sesión de larga duración
        proxy_set_header Cookie $http_cookie;
        proxy_pass_header Set-Cookie;

        proxy_pass http://photos/;
    }

    # msi-v5 - Usa el mismo auth-session que photos
    location /msi-v5 {
        auth_request /auth-session;

        # ESTAS LAS DEFINE EL SCRIPT PHP
        auth_request_set $auth_bearer_token $upstream_http_x_auth_bearer_token;
        auth_request_set $auth_type $upstream_http_x_auth_type;
        auth_request_set $auth_contrato $upstream_http_x_auth_contrato;

        # ESTAS LAS DEFINE EL ENTORNO DEL SERVER
        proxy_set_header X-Auth-Bearer-Token $auth_bearer_token;
        proxy_set_header X-Auth-Type $auth_type;
        proxy_set_header X-Auth-Contrato $auth_contrato;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Host $http_host;

        # Configuración para preservar cookies de sesión de larga duración
        proxy_set_header Cookie $http_cookie;
        proxy_pass_header Set-Cookie;

        proxy_pass http://msi-v5/;
    }

    # auth-session - Autenticación de sesión de larga duración
    location ~ ^/auth-session$ {
        internal;
        fastcgi_pass php-fpm;
        fastcgi_pass_request_body off;
        fastcgi_param REQUEST_METHOD GET;
        fastcgi_param CONTENT_LENGTH 0;
        fastcgi_param CONTENT_TYPE '';
        # fastcgi_param QUERY_STRING "sessId=$cookie_";
        fastcgi_param SCRIPT_FILENAME "/var/www/auth-session/iSession.php";
    }

    # PHP files (EXCEPT auth-session)
    location ~ ^(?!/auth-session/).*\.php$ {
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # Configuración para sesiones de larga duración
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
    }

    error_log /var/log/nginx/error.log debug;
    access_log /var/log/nginx/access.log;

    # No permitir acceso a archivos ocultos
    location ~ /\.(?!well-known).* {
        deny all;
    }
}