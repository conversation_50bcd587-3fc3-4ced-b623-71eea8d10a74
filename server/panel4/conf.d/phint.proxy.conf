server {
    listen 80;
    server_name si4;

    root /var/www/html;

    location /phint {
        auth_request /secureInternal.nginx.php;

        # ESTAS LAS DEFINE EL SCRIPT PHP
        auth_request_set $auth_llvsi4 $upstream_http_x_auth_llvsi4;

        # ESTAS LAS DEFINE EL ENTORNO DEL SERVER
        proxy_set_header X-Auth-Llvsi4 $auth_llvsi4;

        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Host $http_host;
        proxy_set_header X-Request-Scheme $scheme;

        proxy_pass http://photos/;
    }

    # PHP
    location ~ \.php$ {
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
