server {
    listen 80;
    server_name pics-server;

    # Directorio raíz
    root /var/mulbin-photos/web;
    index index.php index.html;

    # === CONFIGURACIÓN DE LOGS PARA DESARROLLO ===
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log debug;

    # === CONFIGURACIÓN DE UPLOADS PARA IMÁGENES ===
    client_max_body_size 64M;
    client_body_timeout 300s;
    client_header_timeout 300s;

    # === CONFIGURACIÓN DE TIMEOUTS ===
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    fastcgi_connect_timeout 300s;
    fastcgi_send_timeout 300s;
    fastcgi_read_timeout 300s;

    # === CONFIGURACIÓN DE BUFFERS PARA IMÁGENES GRANDES ===
    fastcgi_buffer_size 128k;
    fastcgi_buffers 4 256k;
    fastcgi_busy_buffers_size 256k;

    # === HEADERS PARA DESARROLLO (NO CACHE) ===
    add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
    add_header Pragma "no-cache";
    add_header Expires "0";

    # === ENDPOINT PRINCIPAL PARA FOTOS ===
    location / {
        root "/var/mulbin-photos/web";
        rewrite ^/$ /index.php;
        # try_files $uri $uri/ /index.php?$query_string;
    }

    # === CONFIGURACIÓN PHP-FPM CON SOCKET UNIX ===
    location ~ ^/index\.php$ {
        root "/var/mulbin-photos/iFotos";
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        # Socket Unix para mejor rendimiento
        fastcgi_pass unix:/run/php-fpm.sock;
        fastcgi_index index.php;

        # Parámetros FastCGI optimizados
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # Headers para procesamiento de imágenes
        fastcgi_param HTTP_X_FORWARDED_FOR $remote_addr;
        fastcgi_param HTTP_X_REAL_IP $remote_addr;

        # Headers para desarrollo
        fastcgi_param ENVIRONMENT "development";
        fastcgi_param DEBUG_MODE "1";

        # Configuración específica para uploads
        fastcgi_request_buffering off;
        fastcgi_max_temp_file_size 0;

        # === HEADERS DE NO-CACHE PARA PHP ===
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # === DIRECTORIO DE FOTOS SUBIDAS (PRIORIDAD MÁXIMA) ===
    location ^~ /propiedades/ {
        alias /var/mulbin-photos/photos/;

        # Sin caché para desarrollo
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
        add_header Pragma "no-cache";

        # Logs habilitados para debugging
        access_log on;

        # Prevenir ejecución de scripts en directorio de uploads
        location ~ \.php$ {
            deny all;
        }
    }

    # === CONFIGURACIÓN PARA ARCHIVOS ESTÁTICOS EN DESARROLLO ===
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        # Sin caché para desarrollo
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
        add_header Pragma "no-cache";

        # Logs habilitados para debugging
        access_log on;
    }

    # === CONFIGURACIÓN DE SEGURIDAD ===
    # Denegar acceso a archivos sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Denegar acceso a archivos de configuración
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}