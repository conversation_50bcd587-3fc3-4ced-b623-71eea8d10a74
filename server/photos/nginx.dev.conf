# === CONFIGURACIÓN NGINX OPTIMIZADA PARA SERVICIO DE FOTOS ===

user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;

# PID file
pid /var/run/nginx.pid;

# Error log
error_log /var/log/nginx/error.log warn;

# Cargar módulos dinámicos
include /etc/nginx/modules/*.conf;

events {
    # Conexiones por worker optimizado para imágenes
    worker_connections 2048;

    # Método de eventos eficiente
    use epoll;

    # Aceptar múltiples conexiones
    multi_accept on;
}

http {
    # === CONFIGURACIÓN MIME ===
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # === CONFIGURACIÓN DE LOGS ===
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for" '
    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # === OPTIMIZACIONES DE RENDIMIENTO ===
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    # Timeouts optimizados para upload de imágenes
    keepalive_timeout 30;
    keepalive_requests 1000;
    client_header_timeout 10;
    client_body_timeout 60;
    send_timeout 10;

    # Buffers optimizados para imágenes grandes
    client_max_body_size 64M;
    client_body_buffer_size 128k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 16k;

    # === COMPRESIÓN GZIP ===
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;

    # === CONFIGURACIÓN DE CACHÉ ===
    # Caché de archivos abiertos
    open_file_cache max=10000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # === SEGURIDAD ===
    server_tokens off;

    # Headers de seguridad
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # === CONFIGURACIÓN FASTCGI ===
    fastcgi_buffers 16 16k;
    fastcgi_buffer_size 32k;
    fastcgi_read_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_connect_timeout 60;

    # === CONFIGURACIÓN UPSTREAM ===
    upstream php-fpm {
        server unix:/run/php-fpm.sock;
        keepalive 32;
    }

    # === LÍMITES DE RATE ===
    # Prevenir ataques de fuerza bruta en uploads
    limit_req_zone $binary_remote_addr zone=upload:10m rate=10r/m;

    # Incluir configuraciones de sitios
    include /etc/nginx/conf.d/*.conf;
}