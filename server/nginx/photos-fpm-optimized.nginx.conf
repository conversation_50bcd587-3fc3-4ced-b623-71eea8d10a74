server {
    listen 80;
    server_name localhost;

    root /var/www/web;
    index index.html;

    # === CONFIGURACIÓN DE LOGS ===
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log warn;

    # === CONFIGURACIÓN DE UPLOADS PARA IMÁGENES ===
    client_max_body_size 64M;
    client_body_timeout 300s;
    client_header_timeout 300s;

    # === CONFIGURACIÓN DE TIMEOUTS ===
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    fastcgi_connect_timeout 300s;
    fastcgi_send_timeout 300s;
    fastcgi_read_timeout 300s;

    # === CONFIGURACIÓN DE BUFFERS PARA IMÁGENES GRANDES ===
    fastcgi_buffer_size 128k;
    fastcgi_buffers 4 256k;
    fastcgi_busy_buffers_size 256k;

    # === ENDPOINT PRINCIPAL PARA FOTOS ===
    location / {
        root "/var/www/web";
        index index.html;
        rewrite ^/$ /index.php;
    }

    # === CONFIGURACIÓN PHP-FPM CON SOCKET UNIX ===
    location ~ ^/index\.php$ {
        root "/var/www/iFotos";
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        # Socket Unix para mejor rendimiento
        fastcgi_pass unix:/run/php-fpm.sock;

        # Parámetros FastCGI optimizados
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # Headers para procesamiento de imágenes
        fastcgi_param HTTP_X_FORWARDED_FOR $remote_addr;
        fastcgi_param HTTP_X_REAL_IP $remote_addr;

        # Configuración específica para uploads
        fastcgi_request_buffering off;
        fastcgi_max_temp_file_size 0;
    }

    # === CONFIGURACIÓN PARA ARCHIVOS ESTÁTICOS ===
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;

        # Compresión para archivos estáticos
        gzip_static on;
        # Logs opcionales para archivos estáticos
        access_log off;
    }

    # === DIRECTORIO DE FOTOS SUBIDAS ===
    location /pics/ {
        alias /var/www/pics/;

        # Configuración de caché para imágenes
        expires 30d;
        add_header Cache-Control "public";
        add_header X-Content-Type-Options nosniff;

        # Prevenir ejecución de scripts en directorio de uploads
        location ~ \.php$ {
            deny all;
        }
    }

    # === CONFIGURACIÓN DE SEGURIDAD ===
    # Denegar acceso a archivos sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Denegar acceso a archivos de configuración
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

#     # === ENDPOINT DE HEALTH CHECK ===
#     location /health {
#         access_log off;
#         return 200 "OK\n";
#         add_header Content-Type text/plain;
#     }

    # === CONFIGURACIÓN DE COMPRESIÓN ===
    location ~* \.(css|js|json|xml)$ {
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml+rss;
    }
}