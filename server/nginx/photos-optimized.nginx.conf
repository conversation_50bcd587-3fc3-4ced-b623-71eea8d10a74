server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/web;
    index index.html;

    # === CONFIGURACIÓN DE LOGS ===
    access_log /var/log/nginx/photos_access.log main;
    error_log /var/log/nginx/photos_error.log warn;

	location / {
		# Redirigir todas las solicitudes a index.php
		rewrite ^/$ /index.php;
	}
# 	location /propiedades {
# 		alias /mnt/photos001/photos;
# 	}

# 	# Para ejecutar scripts PHP
# 	location ~ ^/index\.php$ {
# 		root           "/var/www/iFotos";
# 		fastcgi_pass   php-fpm;
# 		fastcgi_param  SCRIPT_FILENAME   $document_root/index.php;
# 		include        fastcgi_params;
# 	}

    # === CONFIGURACIÓN PARA FOTOS DE PROPIEDADES ===
    location /propiedades {
        alias /var/www/pics;

        # Caché agresivo para imágenes
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;

        # Servir archivos directamente
        try_files $uri $uri/ =404;

        # Configuración para diferentes tipos de imagen
        location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";

            # Compresión para SVG
            location ~* \.svg$ {
                gzip_static on;
                expires 1y;
            }
        }
    }

    # === PROCESAMIENTO PHP ===
#     location ~ \.php$ {
	location ~ ^/index\.php$ {
        # Protección contra archivos PHP no existentes
        try_files $uri =404;

        # Pasar a PHP-FPM via socket Unix
        fastcgi_pass php-fpm;
#         fastcgi_index index.php;

        # Parámetros FastCGI optimizados
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
		fastcgi_param SCRIPT_FILENAME /var/www/iFotos/index.php;
        fastcgi_param QUERY_STRING $query_string;
        fastcgi_param REQUEST_METHOD $request_method;
        fastcgi_param CONTENT_TYPE $content_type;
        fastcgi_param CONTENT_LENGTH $content_length;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param DOCUMENT_URI $document_uri;
        fastcgi_param DOCUMENT_ROOT $document_root;
        fastcgi_param SERVER_PROTOCOL $server_protocol;
        fastcgi_param REMOTE_ADDR $remote_addr;
        fastcgi_param REMOTE_PORT $remote_port;
        fastcgi_param SERVER_ADDR $server_addr;
        fastcgi_param SERVER_PORT $server_port;
        fastcgi_param SERVER_NAME $server_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param HTTPS $https if_not_empty;

        # Timeouts para procesamiento de imágenes
        fastcgi_read_timeout 300s;
        fastcgi_send_timeout 300s;

        # Buffers optimizados
        fastcgi_buffer_size 32k;
        fastcgi_buffers 16 16k;
        fastcgi_busy_buffers_size 32k;
    }

    # === UPLOAD DE IMÁGENES ===
    location /upload {
        # Aplicar rate limiting
        limit_req zone=upload burst=3 nodelay;

        # Solo permitir POST
        limit_except POST {
            deny all;
        }

        # Pasar a PHP
        try_files $uri $uri/ @php;
    }

    # === MANEJADOR PHP PARA UPLOADS ===
    location @php {
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        include fastcgi_params;
    }

    # === ARCHIVOS ESTÁTICOS ===
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;

        # Compresión gzip para archivos texto
        location ~* \.(css|js|svg)$ {
            gzip_static on;
        }
    }

    # === MONITOREO Y STATUS ===
    location /nginx-status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }

    location ~ ^/(status|ping)$ {
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }

    # === SEGURIDAD ===
    # Negar acceso a archivos ocultos
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Negar acceso a archivos de configuración
    location ~* \.(conf|htaccess|htpasswd|ini|log|sh|sql|bak)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # === MANEJO DE ERRORES ===
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /var/www/web;
    }
}
