/* Base */

body,
body *:not(html):not(style):not(br):not(tr):not(code) {
    box-sizing: border-box;
    font-family: "Instrument Sans", -apple-system, BlinkMacSystemFont,
        "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji",
        "Segoe UI Emoji", "Segoe UI Symbol";
    position: relative;
}

body {
    -webkit-text-size-adjust: none;
    background-color: #f2f4f7;
    color: #4b5563;
    height: 100%;
    line-height: 1.4;
    margin: 0;
    padding: 0;
    width: 100% !important;
}

p,
ul,
ol,
blockquote {
    line-height: 1.4;
    text-align: left;
}

a {
    color: #4f46e5; /* Indigo */
}

a img {
    border: none;
}

/* Typography */

h1 {
    color: #3730a3;
    font-size: 18px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h2 {
    font-size: 16px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h3 {
    font-size: 14px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

p {
    font-size: 16px;
    line-height: 1.5em;
    margin-top: 0;
    text-align: left;
}

p.sub {
    font-size: 12px;
}

img {
    max-width: 100%;
}

/* Layout */

.wrapper {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f2f4f7;
    margin: 0;
    padding: 0;
    width: 100%;
}

.content {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Header */

.header {
    padding: 25px 0;
    text-align: center;
}

.header a {
    color: #3d4852;
    font-size: 19px;
    font-weight: bold;
    text-decoration: none;
}

/* Logo */

.logo {
    height: 75px;
    max-height: 75px;
    width: auto;
}

/* Body */

.body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f2f4f7;
    border-bottom: 1px solid #f2f4f7;
    border-top: 1px solid #f2f4f7;
    margin: 0;
    padding: 0;
    width: 100%;
}

.inner-body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    background-color: #ffffff;
    border-color: #e8e5ef;
    border-radius: 12px;
    border-width: 1px;
    box-shadow: 0 4px 8px rgba(0, 0, 150, 0.1);
    margin: 0 auto;
    padding: 0;
    width: 570px;
}

.inner-body a {
    word-break: break-all;
}

/* Subcopy */

.subcopy {
    border-top: 1px solid #e8e5ef;
    margin-top: 25px;
    padding-top: 25px;
}

.subcopy p {
    font-size: 14px;
    color: #718096;
}

/* Footer */

.footer {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 570px;
}

.footer p {
    color: #a0aec0;
    font-size: 12px;
    text-align: center;
}

.footer a {
    color: #a0aec0;
    text-decoration: underline;
}

/* Tables */

.table table {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    width: 100%;
}

.table th {
    border-bottom: 1px solid #edeff2;
    margin: 0;
    padding-bottom: 8px;
}

.table td {
    color: #74787e;
    font-size: 15px;
    line-height: 18px;
    margin: 0;
    padding: 10px 0;
}

.content-cell {
    max-width: 100vw;
    padding: 32px;
}

/* Buttons */

.action {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    padding: 0;
    text-align: center;
    width: 100%;
}

.button {
    -webkit-text-size-adjust: none;
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    overflow: hidden;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 0.01em;
    box-shadow: 0 3px 6px rgba(79, 70, 229, 0.2);
}

.button-blue,
.button-primary {
    background-color: #4338ca;
    border-bottom: 12px solid #4338ca;
    border-left: 24px solid #4338ca;
    border-right: 24px solid #4338ca;
    border-top: 12px solid #4338ca;
}

.button-green,
.button-success {
    background-color: #059669;
    border-bottom: 12px solid #059669;
    border-left: 24px solid #059669;
    border-right: 24px solid #059669;
    border-top: 12px solid #059669;
}

.button-red,
.button-error {
    background-color: #dc2626;
    border-bottom: 12px solid #dc2626;
    border-left: 24px solid #dc2626;
    border-right: 24px solid #dc2626;
    border-top: 12px solid #dc2626;
}

/* Panels */

.panel {
    border-left: #4338ca solid 4px;
    margin: 21px 0;
}

.panel-content {
    background-color: #f0f4ff;
    color: #4b5563;
    padding: 16px;
}

.panel-content p {
    color: #4b5563;
}

.panel-item {
    padding: 0;
}

.panel-item p:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Utilities */

.break-all {
    word-break: break-all;
}
