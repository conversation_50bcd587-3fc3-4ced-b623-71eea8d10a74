<!-- <PERSON>ript para Google Places Autocomplete -->
<script>
    document.addEventListener('livewire:initialized', () => {
        // Escuchar el evento cuando cambia el paso del formulario
        @this.on('formStepReady', () => {
            // Cargar el script de Google Maps solo cuando estemos en el paso 2
            if (@this.currentStep === 2) {
                // Verificar si el script ya ha sido cargado
                if (!window.googleMapsLoaded) {
                    window.googleMapsLoaded = true;

                    // Cargar el script de Google Maps dinámicamente
                    const script = document.createElement('script');
                    script.src = `https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&libraries=places&callback=initAutocomplete`;
                    script.async = true;
                    script.defer = true;
                    document.head.appendChild(script);
                } else {
                    // Si ya está cargado, inicializar autocomplete manualmente
                    if (typeof initAutocomplete === 'function') {
                        initAutocomplete();
                    }
                }
            }
        });
    });

    // Función para inicializar Google Places Autocomplete
    function initAutocomplete() {
        // Verificar que el elemento existe antes de intentar inicializar
        const inputSearch = document.getElementById('autocomplete');
        if (!inputSearch) return;

        // Crear el objeto autocomplete con restricciones para México
        const autocomplete = new google.maps.places.Autocomplete(inputSearch, {
            types: ["address"],
            componentRestrictions: {country: "mx"}, // Restringir a México
            fields: ["address_components"], // Solo obtener los componentes de dirección
        });

        // Cuando el usuario selecciona una dirección del dropdown, rellenar los campos
        autocomplete.addListener("place_changed", () => {
            const place = autocomplete.getPlace();
            if (!place.address_components) return;

            // Vacía los campos antes de rellenar
            document.getElementById('calle_numero').value = '';
            document.getElementById('colonia').value = '';
            document.getElementById('codigo_postal').value = '';
            document.getElementById('ciudad').value = '';
            document.getElementById('estado').value = ''; // Select se limpia igual
            document.getElementById('pais').value = '';

            // También limpiar los valores en Livewire
            @this.set('calle_numero', '');
            @this.set('colonia', '');
            @this.set('codigo_postal', '');
            @this.set('ciudad', '');
            @this.set('estado', '');
            @this.set('pais', '');

            /*  Mapeo de estados de Google Maps API a nombres exactos del select  */
            const estadosMap = {
                'Aguascalientes': 'Aguascalientes',
                'Baja California': 'Baja California',
                'Baja California Sur': 'Baja California Sur',
                'Campeche': 'Campeche',
                'Chiapas': 'Chiapas',
                'Chihuahua': 'Chihuahua',
                'Ciudad de México': 'Ciudad de México',
                'CDMX': 'Ciudad de México',
                'Distrito Federal': 'Ciudad de México',
                'Mexico City': 'Ciudad de México',
                'Coahuila': 'Coahuila',
                'Coahuila de Zaragoza': 'Coahuila',
                'Colima': 'Colima',
                'Durango': 'Durango',
                'Estado de México': 'Estado de México',
                'Mexico State': 'Estado de México',
                'Guanajuato': 'Guanajuato',
                'Guerrero': 'Guerrero',
                'Hidalgo': 'Hidalgo',
                'Jalisco': 'Jalisco',
                'Michoacán': 'Michoacán',
                'Michoacán de Ocampo': 'Michoacán',
                'Morelos': 'Morelos',
                'Nayarit': 'Nayarit',
                'Nuevo León': 'Nuevo León',
                'Oaxaca': 'Oaxaca',
                'Puebla': 'Puebla',
                'Querétaro': 'Querétaro',
                'Quintana Roo': 'Quintana Roo',
                'San Luis Potosí': 'San Luis Potosí',
                'Sinaloa': 'Sinaloa',
                'Sonora': 'Sonora',
                'Tabasco': 'Tabasco',
                'Tamaulipas': 'Tamaulipas',
                'Tlaxcala': 'Tlaxcala',
                'Veracruz': 'Veracruz',
                'Veracruz de Ignacio de la Llave': 'Veracruz',
                'Yucatán': 'Yucatán',
                'Zacatecas': 'Zacatecas'
            };

            /*  Mapeo de países de Google Maps API a nombres exactos del select  */
            const paisesMap = {
                // Códigos ISO y nombres completos de México
                'MX': 'México',
                'México': 'México',
                'Mexico': 'México',
                // Códigos ISO y nombres completos de otros países hispanos
                'AR': 'Argentina',
                'Argentina': 'Argentina',
                'BO': 'Bolivia',
                'Bolivia': 'Bolivia',
                'CL': 'Chile',
                'Chile': 'Chile',
                'CO': 'Colombia',
                'Colombia': 'Colombia',
                'CR': 'Costa Rica',
                'Costa Rica': 'Costa Rica',
                'CU': 'Cuba',
                'Cuba': 'Cuba',
                'EC': 'Ecuador',
                'Ecuador': 'Ecuador',
                'SV': 'El Salvador',
                'El Salvador': 'El Salvador',
                'ES': 'España',
                'España': 'España',
                'Spain': 'España',
                'GT': 'Guatemala',
                'Guatemala': 'Guatemala',
                'GQ': 'Guinea Ecuatorial',
                'Guinea Ecuatorial': 'Guinea Ecuatorial',
                'Equatorial Guinea': 'Guinea Ecuatorial',
                'HN': 'Honduras',
                'Honduras': 'Honduras',
                'NI': 'Nicaragua',
                'Nicaragua': 'Nicaragua',
                'PA': 'Panamá',
                'Panamá': 'Panamá',
                'Panama': 'Panamá',
                'PY': 'Paraguay',
                'Paraguay': 'Paraguay',
                'PE': 'Perú',
                'Perú': 'Perú',
                'Peru': 'Perú',
                'PR': 'Puerto Rico',
                'Puerto Rico': 'Puerto Rico',
                'DO': 'República Dominicana',
                'República Dominicana': 'República Dominicana',
                'Dominican Republic': 'República Dominicana',
                'UY': 'Uruguay',
                'Uruguay': 'Uruguay',
                'VE': 'Venezuela',
                'Venezuela': 'Venezuela'
            };

            /*  Mapeo de componentes → IDs del formulario  */
            const map = {
                street_number: {id: "calle_numero"},
                route: {id: "calle_numero"},
                neighborhood: {id: "calle_numero"}, // Agregar neighborhood para la calle
                sublocality_level_1: {id: "colonia"},
                locality: {id: "ciudad"},
                administrative_area_level_1: {id: "estado"},
                country: {id: "pais"},
                postal_code: {id: "codigo_postal"},
            };

            /*  Recorre cada componente de la respuesta  */
            let street = {number: "", route: "", neighborhood: ""};

            place.address_components.forEach((c) => {
                // Buscar en todos los tipos, no solo el primero
                let componentType = null;
                let isNeighborhood = false;
                
                // Identificar el tipo correcto
                if (c.types.includes("street_number")) {
                    componentType = "street_number";
                } else if (c.types.includes("route")) {
                    componentType = "route";
                } else if (c.types.includes("neighborhood")) {
                    isNeighborhood = true;
                } else if (c.types.includes("sublocality_level_1")) {
                    componentType = "sublocality_level_1";
                } else if (c.types.includes("locality")) {
                    componentType = "locality";
                } else if (c.types.includes("administrative_area_level_1")) {
                    componentType = "administrative_area_level_1";
                } else if (c.types.includes("country")) {
                    componentType = "country";
                } else if (c.types.includes("postal_code")) {
                    componentType = "postal_code";
                }

                // Manejar elementos de la calle
                if (componentType === "street_number") {
                    street.number = c.short_name;
                } else if (componentType === "route") {
                    street.route = c.long_name;
                } else if (isNeighborhood) {
                    street.neighborhood = c.long_name;
                } else if (componentType && map[componentType]) {
                    let valueToSet = c.long_name;
                    
                    // Si es el estado, usar el mapeo para obtener el nombre exacto del select
                    if (componentType === "administrative_area_level_1") {
                        valueToSet = estadosMap[c.long_name] || c.long_name;
                    }
                    
                    // Si es el país, usar short_name y mapeo para obtener el nombre exacto del select
                    if (componentType === "country") {
                        valueToSet = paisesMap[c.short_name] || paisesMap[c.long_name] || c.long_name;
                    }
                    
                    // Actualizar el campo en el DOM
                    document.getElementById(map[componentType].id).value = valueToSet;
                    // Actualizar el valor en Livewire
                    @this.set(map[componentType].id, valueToSet);
                }
            });

            /* Combina calle + número + neighborhood */
            if (street.route || street.number || street.neighborhood) {
                /*  Combina calle + número + neighborhood. "Calle 123, Colonia"  */
                const fullStreet = `${street.route || ""} ${street.number || ""}, ${street.neighborhood || ""}`.trim();

                // Actualizar el campo en el DOM
                document.getElementById("calle_numero").value = fullStreet;
                // Actualizar el valor en Livewire
                @this.set('calle_numero', fullStreet);
            }

            // Si no se encontró colonia (sublocality_level_1), intentar usar sublocality
            if (!document.getElementById('colonia').value) {
                place.address_components.forEach((c) => {
                    if (c.types.includes("sublocality") && !c.types.includes("sublocality_level_1")) {
                        // Actualizar el campo en el DOM
                        document.getElementById('colonia').value = c.long_name;
                        // Actualizar el valor en Livewire
                        @this.set('colonia', c.long_name);
                    }
                });
            }
        });

        // Prevenir el envío del formulario al presionar Enter en el campo de dirección
        inputSearch.addEventListener('keydown', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
    }
</script>
