<script type="text/javascript" src="https://cdn.conekta.io/js/latest/conekta.js"></script>
<script>
    document.addEventListener('livewire:initialized', () => {
        // Configurar Conekta con la llave pública
        Conekta.setPublicKey('{{ env("CONEKTA_PUBLIC_KEY") }}');

        // Función para crear token de tarjeta
        window.createConektaToken = function () {
            // Limpiar mensajes de error previos
            document.querySelectorAll('.conekta-error').forEach(el => el.textContent = '');

            console.log('Creando token de tarjeta...');

            // Obtener los valores del formulario
            const cardForm = {
                name: document.getElementById('cardName').value,
                number: document.getElementById('cardNumber').value.replace(/\s+/g, ''),
                exp_month: document.getElementById('cardExpiry').value.split('/')[0],
                exp_year: document.getElementById('cardExpiry').value.split('/')[1],
                cvc: document.getElementById('cardCvc').value
            };

            console.log('Valores del formulario:', cardForm);

            // Validar campos requeridos
            let hasErrors = false;
            if (!cardForm.name) {
                document.getElementById('errorCardName').textContent = 'El nombre en la tarjeta es requerido';
                hasErrors = true;
            }
            if (!cardForm.number) {
                document.getElementById('errorCardNumber').textContent = 'El número de tarjeta es requerido';
                hasErrors = true;
            }
            if (!cardForm.exp_month || !cardForm.exp_year) {
                document.getElementById('errorCardExpiry').textContent = 'La fecha de expiración es requerida';
                hasErrors = true;
            }
            if (!cardForm.cvc) {
                document.getElementById('errorCardCvc').textContent = 'El código de seguridad es requerido';
                hasErrors = true;
            }

            if (hasErrors) return;

            // Mostrar estado de carga en el botón de validación
            const validateButton = document.getElementById('validateCardButton');
            const validateButtonText = document.getElementById('validateCardButtonText');
            const validateButtonLoading = document.getElementById('validateCardButtonLoading');

            if (validateButton) validateButton.disabled = true;
            if (validateButtonText) validateButtonText.classList.add('hidden');
            if (validateButtonLoading) validateButtonLoading.classList.remove('hidden');

            // Deshabilitar el botón de envío mientras se procesa
            const submitButton = document.getElementById('submitButton');
            if (submitButton) submitButton.disabled = true;

            // Crear token con Conekta
            Conekta.Token.create(
                {card: cardForm},
                function (token) {
                    // Token creado exitosamente
                    // console.log('Token creado:', token.id);
                    // Enviar el token al componente Livewire
                    @this.call('handleTokenCreated', token.id);
                    // Mostrar mensaje de éxito
                    document.getElementById('errorConekta').innerHTML = '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">Tarjeta validada correctamente</div>';

                    // Restaurar el estado del botón de validación
                    const validateButton = document.getElementById('validateCardButton');
                    const validateButtonText = document.getElementById('validateCardButtonText');
                    const validateButtonLoading = document.getElementById('validateCardButtonLoading');

                    if (validateButton) validateButton.disabled = false;
                    if (validateButtonText) validateButtonText.classList.remove('hidden');
                    if (validateButtonLoading) validateButtonLoading.classList.add('hidden');

                    // No habilitamos manualmente el botón de envío, dejamos que Livewire lo controle
                },
                function (error) {
                    // Error al crear el token
                    console.error('Error al crear token:', error.message);
                    document.getElementById('errorConekta').textContent = error.message;

                    // Restaurar el estado del botón de validación
                    const validateButton = document.getElementById('validateCardButton');
                    const validateButtonText = document.getElementById('validateCardButtonText');
                    const validateButtonLoading = document.getElementById('validateCardButtonLoading');

                    if (validateButton) validateButton.disabled = false;
                    if (validateButtonText) validateButtonText.classList.remove('hidden');
                    if (validateButtonLoading) validateButtonLoading.classList.add('hidden');

                    // No habilitamos manualmente el botón de envío, dejamos que Livewire lo controle
                }
            );
        };
    });
</script>
