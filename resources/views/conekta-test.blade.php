<x-layouts.app title="Prueba de Conekta">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-2xl font-bold mb-6">Prueba de Integración con Conekta</h2>

                    @if (session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4"><PERSON><PERSON><PERSON> de Tarjeta</h3>

                        <div id="errorConekta" class="text-red-500 text-sm mb-4"></div>

                        <div class="mb-4">
                            <label for="cardNumber" class="block text-sm font-medium text-gray-700 mb-1">Número de tarjeta</label>
                            <input type="text" id="cardNumber" placeholder="4242 4242 4242 4242"
                                   x-data="{ mask: function(value) { return value.replace(/\D/g, '').replace(/(\d{4})(?=\d)/g, '$1 ').trim(); } }"
                                   x-on:input="$el.value = mask($el.value)"
                                   maxlength="19"
                                   class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            <span id="errorCardNumber" class="text-red-500 text-xs conekta-error"></span>
                            <p class="text-xs text-gray-500 mt-1">Para pruebas: 4242 4242 4242 4242</p>
                        </div>

                        <div class="mb-4">
                            <label for="cardName" class="block text-sm font-medium text-gray-700 mb-1">Nombre del titular</label>
                            <input type="text" id="cardName" placeholder="Como aparece en la tarjeta"
                                   class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            <span id="errorCardName" class="text-red-500 text-xs conekta-error"></span>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div class="mb-4">
                                <label for="cardExpiry" class="block text-sm font-medium text-gray-700 mb-1">Fecha de expiración</label>
                                <input type="text" id="cardExpiry" placeholder="MM/AA"
                                       x-data="{ mask: function(value) { return value.replace(/\D/g, '').replace(/(\d{2})(?=\d)/g, '$1/').substr(0, 5); } }"
                                       x-on:input="$el.value = mask($el.value)"
                                       maxlength="5"
                                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                                <span id="errorCardExpiry" class="text-red-500 text-xs conekta-error"></span>
                                <p class="text-xs text-gray-500 mt-1">Para pruebas: cualquier fecha futura</p>
                            </div>

                            <div class="mb-4">
                                <label for="cardCvc" class="block text-sm font-medium text-gray-700 mb-1">Código de seguridad</label>
                                <input type="text" id="cardCvc" placeholder="CVC"
                                       x-data="{ mask: function(value) { return value.replace(/\D/g, '').substr(0, 4); } }"
                                       x-on:input="$el.value = mask($el.value)"
                                       maxlength="4"
                                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                                <span id="errorCardCvc" class="text-red-500 text-xs conekta-error"></span>
                                <p class="text-xs text-gray-500 mt-1">Para pruebas: cualquier 3 dígitos</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="button" onclick="createConektaToken()"
                                    class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Crear Token
                            </button>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h3 class="text-lg font-medium mb-4">Crear Cliente en Conekta</h3>

                        <form action="{{ route('conekta.test.create-customer') }}" method="POST" class="space-y-4">
                            @csrf
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nombre</label>
                                <input type="text" name="name" id="name" required
                                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <input type="email" name="email" id="email" required
                                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="token_id" class="block text-sm font-medium text-gray-700 mb-1">Token ID</label>
                                <input type="text" name="token_id" id="token_id" required readonly
                                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md bg-gray-50">
                                <p class="text-xs text-gray-500 mt-1">Este campo se llenará automáticamente al crear un token</p>
                            </div>

                            <div>
                                <button type="submit" id="submitButton" disabled
                                        class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                    Crear Cliente
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script type="text/javascript" src="https://cdn.conekta.io/js/latest/conekta.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar Conekta con la llave pública
            Conekta.setPublicKey('{{ env("CONEKTA_PUBLIC_KEY") }}');

            // Función para crear token de tarjeta
            window.createConektaToken = function() {
                // Limpiar mensajes de error previos
                document.querySelectorAll('.conekta-error').forEach(el => el.textContent = '');
                document.getElementById('errorConekta').textContent = '';

                // Obtener los valores del formulario
                const cardForm = {
                    name: document.getElementById('cardName').value,
                    number: document.getElementById('cardNumber').value.replace(/\s+/g, ''),
                    exp_month: document.getElementById('cardExpiry').value.split('/')[0].trim(),
                    exp_year: document.getElementById('cardExpiry').value.split('/')[1].trim(),
                    cvc: document.getElementById('cardCvc').value
                };

                // Validar campos requeridos
                let hasErrors = false;
                if (!cardForm.name) {
                    document.getElementById('errorCardName').textContent = 'El nombre en la tarjeta es requerido';
                    hasErrors = true;
                }
                if (!cardForm.number) {
                    document.getElementById('errorCardNumber').textContent = 'El número de tarjeta es requerido';
                    hasErrors = true;
                }
                if (!cardForm.exp_month || !cardForm.exp_year) {
                    document.getElementById('errorCardExpiry').textContent = 'La fecha de expiración es requerida';
                    hasErrors = true;
                }
                if (!cardForm.cvc) {
                    document.getElementById('errorCardCvc').textContent = 'El código de seguridad es requerido';
                    hasErrors = true;
                }

                if (hasErrors) return;

                // Deshabilitar el botón de envío mientras se procesa
                const submitButton = document.getElementById('submitButton');
                if (submitButton) submitButton.disabled = true;

                // Crear token con Conekta
                Conekta.Token.create(
                    { card: cardForm },
                    function(token) {
                        // Token creado exitosamente
                        console.log('Token creado:', token.id);
                        // Llenar el campo de token_id
                        document.getElementById('token_id').value = token.id;
                        // Mostrar mensaje de éxito
                        document.getElementById('errorConekta').innerHTML = '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">Token creado exitosamente: ' + token.id + '</div>';
                        // Habilitar el botón de envío
                        if (submitButton) submitButton.disabled = false;
                    },
                    function(error) {
                        // Error al crear el token
                        console.error('Error al crear token:', error.message);
                        document.getElementById('errorConekta').textContent = error.message;
                        // Habilitar el botón de envío
                        if (submitButton) submitButton.disabled = false;
                    }
                );
            };
        });
    </script>
    @endpush
</x-layouts.app>
