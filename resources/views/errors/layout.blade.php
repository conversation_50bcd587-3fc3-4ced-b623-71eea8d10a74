<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Error') | {{ config('app.name') }}</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            padding: 0 20px;
        }
        .error-container {
            text-align: center;
            max-width: 500px;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
        h1 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 16px;
        }
        .error-code {
            font-size: 20px;
            color: #3498db;
            margin-bottom: 20px;
            font-weight: 600;
        }
        p {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #3498db;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="icon">@yield('icon', '❌')</div>
        <div class="error-code">Error @yield('code', '')</div>
        <h1>@yield('message', 'Ha ocurrido un error')</h1>
        <p>@yield('description', 'Lo sentimos, ha ocurrido un error inesperado. Por favor intenta de nuevo más tarde.')</p>
        <a href="{{ route('home') }}" class="btn">Volver al Inicio</a>
    </div>
</body>
</html> 