<x-layouts.app :title="__('Dashboard')"
               :metaDescription="'Panel de control de su sistema inmobiliario. Gestione propiedades, consultas y conecte con otros profesionales del sector.'"
               :metaKeywords="'dashboard inmobiliario, panel de control, sistema inmobiliario, gestión de propiedades'"
               :metaRobots="'noindex, nofollow'" :ogType="'website'">
    <div class="container mx-auto">
        <div class="mb-4 hidden">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('Dashboard') }}</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ __('Welcome to your dashboard') }}</p>
        </div>

        <!-- Cards de Acceso Rápido -->
        <livewire:dashboard-and-website-access/>

        <div class="grid auto-rows-min gap-6 md:grid-cols-3 mb-6">
            <div
                    class="relative aspect-video overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
                <h2 class="font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Properties') }}</h2>
                <div class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20">
                    <x-placeholder-pattern
                            class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20"/>
                </div>
            </div>
            <div
                    class="relative aspect-video overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
                <h2 class="font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Queries') }}</h2>
                <div class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20">
                    <x-placeholder-pattern
                            class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20"/>
                </div>
            </div>
            <div
                    class="relative aspect-video overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
                <h2 class="font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Statistics') }}</h2>
                <div class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20">
                    <x-placeholder-pattern
                            class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20"/>
                </div>
            </div>
        </div>

        <div
                class="relative h-64 md:h-96 overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
            <h2 class="font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Recent Activity') }}
            </h2>
            <div class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20">
                <x-placeholder-pattern
                        class="absolute inset-0 size-full stroke-gray-900/20 dark:stroke-neutral-100/20"/>
            </div>
        </div>
    </div>
</x-layouts.app>
