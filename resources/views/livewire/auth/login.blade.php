<div class="flex flex-col gap-6">
    <div class="text-center">
        <flux:heading size="lg" class="font-semibold text-indigo-600 dark:text-indigo-400">
            {{ __('Multibolsa Inmobiliaria') }}</flux:heading>
        <flux:heading size="xl" class="font-bold">{{ __('Acceso a tu cuenta') }}
        </flux:heading>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')"/>

    <!-- Error de Session -->
    @if (session('error'))
        <div class="p-4 text-red-700 bg-red-50 rounded-lg border border-red-200 dark:bg-red-900/30 dark:border-red-800 dark:text-red-300">
            {{ session('error') }}
        </div>
    @endif

    <!-- Error de encriptación -->
    @if ($error)
        <div class="p-4 text-red-700 bg-red-50 rounded-lg border border-red-200 dark:bg-red-900/30 dark:border-red-800 dark:text-red-300">
            {{ $error }}
        </div>
    @endif

    <div
            class="p-6 bg-white rounded-lg border shadow-md dark:bg-neutral-800 border-zinc-200 dark:border-zinc-700">
        <form wire:submit="login" class="flex flex-col gap-6">
            <!-- Email Address -->
            <flux:input wire:model="email" :label="__('Correo electrónico o usuario')" type="text" required
                        autofocus autocomplete="email" placeholder="<EMAIL> o usuario"
                        class="bg-zinc-50 dark:bg-neutral-700"/>

            <!-- Password -->
            <div class="relative">
                <flux:input wire:model="password" :label="__('Contraseña')" type="password" required
                            autocomplete="current-password" :placeholder="__('Contraseña')"
                            class="bg-zinc-50 dark:bg-neutral-700"/>

                @if (Route::has('password.request'))
                    <flux:link
                            class="absolute top-0 text-sm text-indigo-600 end-0 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300"
                            :href="route('password.request')">
                        {{ __('¿Olvidaste tu contraseña?') }}
                    </flux:link>
                @endif
            </div>

            <!-- Remember Me -->
            {{--            <flux:checkbox wire:model="remember" :label="__('Recordar mis datos')"/>--}}

            <div class="flex justify-end items-center">
                <flux:button variant="primary" type="submit"
                             class="w-full bg-indigo-600 cursor-pointer hover:bg-indigo-700">
                    {{ __('Iniciar sesión') }}
                </flux:button>
            </div>

            <div class="relative">
                <div class="flex absolute inset-0 items-center">
                    <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div class="flex relative justify-center text-sm">
                    <span class="px-2 text-gray-500 bg-white dark:bg-neutral-800 dark:text-gray-400">
                        {{ __('O continúa con') }}
                    </span>
                </div>
            </div>

            <div class="flex flex-col space-y-3">
                <a href="{{ route('auth.google') }}"
                   class="flex gap-3 justify-center items-center px-3 py-2 w-full text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-300 shadow-sm dark:bg-neutral-700 dark:text-white dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-neutral-600 focus-visible:ring-transparent">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                              fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                              fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                              fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                              fill="#EA4335"/>
                    </svg>
                    {{ __('Continuar con Google') }}
                </a>

                @if (false)
                {{-- Temporalmente desactivado hasta que configuremos la integración con Facebook --}}
                <a href="{{ route('auth.facebook') }}"
                   class="flex w-full items-center justify-center gap-3 rounded-md bg-[#1877F2] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#0C63D4] focus-visible:ring-transparent">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    {{ __('Continuar con Facebook') }}
                </a>
                @endif
            </div>
        </form>
    </div>

    <div
            class="p-4 bg-indigo-50 rounded-lg border border-indigo-100 dark:bg-indigo-900/30 dark:border-indigo-800">
        <p class="mb-2 text-sm font-medium text-indigo-700 dark:text-indigo-300">
            {{ __('Tu propia página web profesional') }}</p>
        <p class="text-xs text-indigo-600 dark:text-indigo-400">
            {{ __('Al iniciar sesión, accederás a tu portal inmobiliario donde podrás gestionar propiedades, conectar con otros agentes y expandir tu red profesional.') }}
        </p>
    </div>

    @if (Route::has('register'))
        <div class="space-x-1 text-sm text-center rtl:space-x-reverse text-zinc-600 dark:text-zinc-400">
            {{ __('¿Aún no tienes una cuenta?') }}
            <flux:link :href="route('register')"
                       class="font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300">
                {{ __('Regístrate ahora') }}</flux:link>
        </div>
    @endif
</div>
