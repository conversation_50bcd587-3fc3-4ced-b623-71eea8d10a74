<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth-meta')] class extends Component {
    public string $password = '';

    /**
     * Confirm the user's password.
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function confirmPassword()
    {
        $this->validate([
            'password' => ['required', 'string'],
        ]);

        if (!Hash::check($this->password, Auth::user()->password)) {
            throw ValidationException::withMessages([
                'password' => __('auth.password'),
            ]);
        }

        session(['auth.password_confirmed_at' => time()]);

        return redirect()->intended(route('dashboard'));
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Confirm password')" :description="__(
        'This is a secure area of the application. Please confirm your password before continuing.',
    )" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form wire:submit="confirmPassword" class="flex flex-col gap-6">
        <!-- Password -->
        <flux:input wire:model="password" :label="__('Password')" type="password" required
            autocomplete="new-password" :placeholder="__('Password')" />

        <flux:button variant="primary" type="submit" class="w-full">{{ __('Confirm') }}</flux:button>
    </form>
</div>
