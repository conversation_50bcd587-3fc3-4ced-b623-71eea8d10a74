<?php

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth-meta')] class extends Component
{
    #[Locked]
    public string $token = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public ?string $error = null;

    /**
     * Mount the component.
     */
    public function mount(string $token): void
    {
        $this->token = $token;
        $this->email = request()->string('email');
    }

    /**
     * Reset the password for the given user.
     */
    public function resetPassword()
    {
        $this->validate([
            'token' => ['required'],
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
        ]);

        try {
            // Buscar usuario por email (que en realidad es logmail en el formulario)
            $user = \App\Models\User::where('logmail', $this->email)->first();
            
            if ($user) {
                // Si encontramos el usuario, usamos su email real para el reset
                $resetData = [
                    'logmail' => $user->logmail,
                    'password' => $this->password,
                    'password_confirmation' => $this->password_confirmation,
                    'token' => $this->token
                ];

                $status = Password::reset($resetData, function ($user) {
                    $user->forceFill([
                        'password' => Hash::make($this->password),
                        'remember_token' => Str::random(60),
                    ])->save();

                    event(new PasswordReset($user));
                });
            // } else {
            //     // Si no encontramos el usuario, intentamos con el email tal cual
            //     $status = Password::reset(
            //         $this->only('email', 'password', 'password_confirmation', 'token'), 
            //         function ($user) {
            //             $user->forceFill([
            //                 'password' => Hash::make($this->password),
            //                 'remember_token' => Str::random(60),
            //             ])->save();

            //             event(new PasswordReset($user));
            //         }
            //     );
            }

            // Check status and redirect accordingly
            if ($status != Password::PASSWORD_RESET) {
                $this->addError('email', __($status));
                return;
            }

            Session::flash('status', __($status));
            return redirect()->route('login');
            
        } catch (\Exception $e) {
            $this->error = 'Ha ocurrido un error al intentar restablecer la contraseña: ' . $e->getMessage();
        }
    }
};
?>

<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Reset password')" :description="__('Please enter your new password below')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />
    
    <!-- Error Message -->
    @if ($error)
    <div class="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300">
        {{ $error }}
    </div>
    @endif

    <form wire:submit="resetPassword" class="flex flex-col gap-6">
        <!-- Email Address -->
        <flux:input wire:model="email" :label="__('Email')" type="email" required autocomplete="email" />

        <!-- Password -->
        <flux:input wire:model="password" :label="__('Password')" type="password" required
            autocomplete="new-password" :placeholder="__('Password')" />

        <!-- Confirm Password -->
        <flux:input wire:model="password_confirmation" :label="__('Confirm password')" type="password"
            required autocomplete="new-password" :placeholder="__('Confirm password')" />

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full">
                {{ __('Reset password') }}
            </flux:button>
        </div>
    </form>
</div>
