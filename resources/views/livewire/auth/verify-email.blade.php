<?php

use App\Livewire\Actions\Logout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;
use Carbon\Carbon;

new #[Layout('components.layouts.auth-meta')] class extends Component {
    // Propiedad para almacenar el mensaje de error del temporizador
    public $timerMessage = '';

    // Propiedad para controlar si el botón está deshabilitado
    public $buttonDisabled = false;

    // Propiedad para almacenar el tiempo restante en segundos
    public $timeRemaining = 0;

    /**
     * Mount the component and redirect if email is already verified.
     */
    public function mount()
    {
        // Si el correo ya está verificado, redirigir al dashboard
        if (Auth::user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard'));
        }

        // Verificar si hay un temporizador activo
        $this->checkTimer();
    }

    /**
     * Verificar si hay un temporizador activo y actualizar el estado del botón
     */
    public function checkTimer()
    {
        $lastVerificationSent = Session::get('last_verification_sent');

        if ($lastVerificationSent) {
            $now = Carbon::now();
            $lastSent = Carbon::parse($lastVerificationSent);

            // Calcular el tiempo transcurrido en segundos desde el último envío
            $elapsedSeconds = $now->timestamp - $lastSent->timestamp;

            // Calcular el tiempo restante (120 segundos - tiempo transcurrido)
            $remainingSeconds = 120 - $elapsedSeconds;

            // Si aún queda tiempo de espera
            if ($remainingSeconds > 0) {
                $this->buttonDisabled = true;
                $this->timeRemaining = (int)$remainingSeconds;
                $this->timerMessage = "Espere " . $this->timeRemaining . " segundos antes de solicitar otro correo.";
            } else {
                $this->buttonDisabled = false;
                $this->timerMessage = '';
                $this->timeRemaining = 0;
            }
        }
    }

    /**
     * Send an email verification notification to the user.
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function sendVerification()
    {
        if (Auth::user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard'));
        }

        // Verificar si han pasado al menos 2 minutos desde el último envío
        $lastVerificationSent = Session::get('last_verification_sent');

        if ($lastVerificationSent) {
            $now = Carbon::now();
            $lastSent = Carbon::parse($lastVerificationSent);

            // Calcular el tiempo transcurrido en segundos desde el último envío
            $elapsedSeconds = $now->timestamp - $lastSent->timestamp;

            // Calcular el tiempo restante (120 segundos - tiempo transcurrido)
            $remainingSeconds = 120 - $elapsedSeconds;

            // Si aún queda tiempo de espera
            if ($remainingSeconds > 0) {
                $this->buttonDisabled = true;
                $this->timeRemaining = (int)$remainingSeconds;
                $this->timerMessage = "Espere " . $this->timeRemaining . " segundos antes de solicitar otro correo.";
                return;
            }
        }

        // Enviar el correo de verificación
        Auth::user()->sendEmailVerificationNotification();

        // Guardar la marca de tiempo actual como referencia para el temporizador
        Session::put('last_verification_sent', Carbon::now());

        // Deshabilitar el botón y mostrar el mensaje del temporizador
        $this->buttonDisabled = true;
        $this->timeRemaining = 120;
        $this->timerMessage = "Por favor espere 120 segundos antes de solicitar otro correo.";

        Session::flash('status', 'verification-link-sent');
    }

    /**
     * Log the current user out of the application.
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function logout(Logout $logout)
    {
        $logout();

        return redirect('/');
    }
}; ?>

<div class="mt-4 flex flex-col gap-6" x-data="{ timeLeft: @entangle('timeRemaining'), timerInterval: null }" x-init="
    // Asegurarse de que timeLeft sea un entero
    timeLeft = parseInt(timeLeft);
    if (timeLeft > 0) {
        timerInterval = setInterval(() => {
            if (timeLeft > 0) {
                timeLeft--;
                // Actualizar el valor en Livewire como entero
                $wire.set('timeRemaining', parseInt(timeLeft));
                if (timeLeft > 0) {
                    $wire.set('timerMessage', 'Espere ' + parseInt(timeLeft) + ' segundos antes de solicitar otro correo.');
                } else {
                    $wire.set('timerMessage', '');
                    $wire.set('buttonDisabled', false);
                    clearInterval(timerInterval);
                }
            }
        }, 1000);
    }
    $watch('timeLeft', (value) => {
        // Asegurarse de que value sea un entero
        value = parseInt(value);
        if (value <= 0 && timerInterval) {
            clearInterval(timerInterval);
            $wire.set('buttonDisabled', false);
            $wire.set('timerMessage', '');
        }
    });
">
    <flux:text class="text-center">
        {{ __('Por favor, verifique su dirección de correo electrónico haciendo clic en el enlace que acabamos de enviarle.') }}
    </flux:text>

    <flux:text class="text-center">
        {{ __('Esta verificación es necesaria para activar su perfil en nuestra red profesional inmobiliaria.') }}
    </flux:text>

    @if (session('status') == 'verification-link-sent')
        <flux:text class="text-center font-medium !dark:text-green-400 !text-green-600">
            {{ __('Se ha enviado un nuevo enlace de verificación a la dirección de correo electrónico que proporcionó durante el registro.') }}
        </flux:text>
    @endif

    <div class="flex flex-col items-center justify-between space-y-3">
        @if ($timerMessage)
            <flux:text class="text-center text-sm text-amber-600 dark:text-amber-400 mb-2">
                {{ $timerMessage }}
            </flux:text>
        @endif

        <flux:button wire:click="sendVerification" variant="primary" class="w-full" :disabled="$buttonDisabled">
            {{ __('Reenviar correo de verificación') }}
        </flux:button>

        <flux:link class="text-sm cursor-pointer" wire:click="logout">
            {{ __('Cerrar sesión') }}
        </flux:link>
    </div>
</div>
