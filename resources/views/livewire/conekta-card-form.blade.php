<div
        x-data="{}"
        x-init="
            window.conektaCardForm('{{ env("CONEKTA_PUBLIC_KEY") }}', @this);
"
        class="mb-6 max-w-2xl mx-auto bg-white p-6 rounded-lg border border-gray-200 shadow-sm">

    <!-- Men<PERSON><PERSON> de error de Conekta -->
    <div id="errorConekta" class="text-red-500 text-sm mb-4"></div>

    @if (!$conektaTokenId)
        <div class="mb-4">
            <label for="cardNumber" class="block text-sm font-medium text-gray-700 mb-1">Número
                de
                tarjeta</label>
            <input type="text" id="cardNumber" placeholder="1234 5678 9012 3456"
                   x-mask="9999 9999 9999 9999"
                   maxlength="19"
                   class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md p-2"
            >
            <span id="errorCardNumber" class="text-red-500 text-xs conekta-error"></span>
        </div>

        <div class="mb-4">
            <label for="cardName" class="block text-sm font-medium text-gray-700 mb-1">Nombre
                del
                titular</label>
            <input type="text" id="cardName" placeholder="Como aparece en la tarjeta"
                   class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md p-2"
            >
            <span id="errorCardName" class="text-red-500 text-xs conekta-error"></span>
        </div>

        <div class="grid grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="cardExpiry" class="block text-sm font-medium text-gray-700 mb-1">Fecha
                    de expiración</label>
                <input type="text" id="cardExpiry" placeholder="MM/AA"
                       x-mask="99/99"
                       maxlength="5"
                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md p-2"
                >
                <span id="errorCardExpiry" class="text-red-500 text-xs conekta-error"></span>
            </div>

            <div class="mb-4">
                <label for="cardCvc" class="block text-sm font-medium text-gray-700 mb-1">Código
                    de
                    seguridad</label>
                <input type="text" id="cardCvc" placeholder="CVC"
                       x-mask="9999"
                       maxlength="4"
                       class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md p-2"
                >
                <span id="errorCardCvc" class="text-red-500 text-xs conekta-error"></span>
            </div>
        </div>

        <div class="mt-4">
            <button type="button" onclick="createConektaToken()" id="validateCardButton"
                    class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-indigo-600"
            >
                <span id="validateCardButtonText">Validar tarjeta</span>
                <span id="validateCardButtonLoading"
                      class="hidden flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Validando...
            </span>
            </button>
        </div>

    @endif

    <div class="mt-4 text-xs text-gray-500 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
             viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
        </svg>
        <span>Su información de pago está segura y encriptada</span>
    </div>

    @if ($conektaTokenId)
        <div class="mt-4 p-3 bg-green-50 text-green-700 border border-green-200 rounded-md">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"/>
                </svg>
                <span class="font-medium">Tarjeta validada correctamente</span>
            </div>
            <p class="mt-1 text-sm">Ya puedes continuar con la activación de tu portal web.</p>
        </div>
    @endif
</div>