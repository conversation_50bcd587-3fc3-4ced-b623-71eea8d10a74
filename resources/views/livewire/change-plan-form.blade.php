<div>
    @push('scripts')
        <!-- ConektaCardForm component manages its own scripts -->
    @endpush

    <div class="space-y-6">
        <!-- Paso 1: Selección de plan -->
        @if ($currentStep == 1)
            @livewire('plan-selector', ['isChangingPlan' => true])
        @endif

        <!-- Paso 2: Formulario de datos de pago (solo si es necesario) -->
        @if ($currentStep == 2)
            <form wire:submit.prevent="processChangePlan" class="space-y-6" id="datos-formulario">
                <!-- Resumen del plan seleccionado -->
                <div class="bg-indigo-50 p-6 rounded-lg border border-indigo-100 mb-8 max-w-2xl mx-auto">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 text-center">Plan seleccionado</h3>
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="mb-4 md:mb-0 text-center md:text-left">
                            <p class="text-base font-medium text-gray-900">
                                @if ($selectedPlan == 'SI-FREE')
                                    Plan Freemium
                                    @if ($addMsComplement)
                                        + {{ $msComplementName }}
                                    @endif
                                @elseif ($selectedPlan == 'SI-PRO')
                                    Plan PRO
                                @elseif ($selectedPlan == 'SI-PLUS')
                                    Plan PLUS
                                @endif
                                -
                                @if ($billingPeriod == 'monthly')
                                    Mensual
                                @elseif ($billingPeriod == 'quarterly')
                                    Trimestral
                                @elseif ($billingPeriod == 'biannual')
                                    Semestral
                                @elseif ($billingPeriod == 'annual')
                                    Anual
                                @endif
                            </p>
                            <p class="text-sm text-gray-600 mt-1">
                                @if ($selectedPlan == 'SI-FREE' && $addMsComplement)
                                    ${{ number_format($msComplementPrice, 0) }} MXN/mes
                                    @if ($billingPeriod != 'monthly')
                                        (${{ number_format($totalPrice, 0) }} MXN total)
                                    @endif
                                @else
                                    ${{ number_format($planPrice, 0) }} MXN/mes
                                    @if ($billingPeriod != 'monthly')
                                        (${{ number_format($totalPrice, 0) }} MXN total)
                                    @endif
                                @endif
                            </p>
                            @if ($selectedPlan == 'SI-FREE' && $addMsComplement)
                                <p class="text-xs text-indigo-600 mt-1">
                                <span class="inline-flex items-center">
                                    <svg class="h-3 w-3 text-indigo-500 mr-1" xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                    Se facturará el complemento a su tarjeta
                                </span>
                                </p>
                            @endif
                        </div>
                        <a href="#" wire:click.prevent="$set('currentStep', 1)"
                           class="text-sm text-indigo-600 hover:text-indigo-800">
                            Cambiar plan
                        </a>
                    </div>
                </div>

                <!-- Mensajes de error/éxito -->
                @if ($errorMessage)
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {{ $errorMessage }}
                    </div>
                @endif

                @if ($successMessage)
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        {{ $successMessage }}
                    </div>
                @endif

                <!-- Formulario de tarjeta -->
                @if ($hasValidatedCard)
                    <div class="bg-green-50 p-4 rounded-md border border-green-200 mb-6 max-w-2xl mx-auto">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg"
                                 viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                      clip-rule="evenodd"/>
                            </svg>
                            <span class="font-medium">Ya tiene una tarjeta registrada</span>
                        </div>
                        <p class="mt-1 text-sm ml-7">No es necesario introducir nuevamente sus datos de pago.</p>

                        @if ($selectedPlan == 'SI-FREE' && $addMsComplement)
                            <div class="mt-3 ml-7 p-3 bg-indigo-50 border border-indigo-100 rounded">
                                <p class="text-sm text-indigo-800">
                                    <span class="font-medium">Información del complemento:</span> Se añadirá el
                                    complemento {{ $msComplementName }} a su plan Freemium.
                                    <br>
                                    <span class="font-medium">Importe a facturar:</span>
                                    ${{ number_format($msComplementPrice, 0) }} MXN/mes
                                    @if ($billingPeriod != 'monthly')
                                        (${{ number_format($totalPrice, 0) }} MXN total
                                        por {{ $billingPeriod == 'quarterly' ? '3 meses' : ($billingPeriod == 'biannual' ? '6 meses' : '12 meses') }}
                                        )
                                    @endif
                                </p>
                            </div>
                        @endif

                        <div class="mt-4 text-center">
                            <button type="submit"
                                    class="py-2 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105 animate-pulse cursor-pointer"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="opacity-75 cursor-wait"
                                    wire:target="processChangePlan">
                                <span wire:loading.class="hidden"
                                      wire:target="processChangePlan">¡Cambiar plan ahora!</span>
                                <span wire:loading.class.remove="hidden" wire:target="processChangePlan"
                                      class="hidden flex items-center">
                                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor"
                                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Procesando...
                                </span>
                            </button>
                        </div>
                    </div>
                @else
                    <div class="max-w-2xl mx-auto">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Información de pago</h3>
                        <p class="text-sm text-gray-600 mb-4">
                            @if ($selectedPlan == 'SI-FREE' && $addMsComplement)
                                Para añadir el complemento {{ $msComplementName }} a su plan Freemium, necesitamos
                                validar su tarjeta de crédito o débito.
                                <br><br>
                                <span class="text-indigo-600 font-medium">Importe a facturar: ${{ number_format($msComplementPrice, 0) }} MXN/mes
                            @if ($billingPeriod != 'monthly')
                                        (${{ number_format($totalPrice, 0) }} MXN total
                                        por {{ $billingPeriod == 'quarterly' ? '3 meses' : ($billingPeriod == 'biannual' ? '6 meses' : '12 meses') }}
                                        )
                                    @endif
                            </span>
                            @else
                                Para cambiar a este plan, necesitamos validar su tarjeta de crédito o débito.
                            @endif
                        </p>

                        <!-- Formulario de tarjeta con Conekta -->
                        @livewire('conekta-card-form', ['conektaTokenId' => $conektaTokenId], key('conekta-card-form'))

                        <!-- Botón para procesar el cambio de plan -->
                        <div class="mt-6 flex justify-center">
                            <button type="submit"
                                    class="w-full md:w-auto flex justify-center py-3 px-8 border border-transparent rounded-md shadow-lg text-base font-medium text-white {{ ($conektaTokenId) ? 'bg-green-600 hover:bg-green-700 animate-pulse' : 'bg-indigo-600 hover:bg-indigo-700' }} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 max-w-md disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-indigo-600 disabled:hover:scale-100 disabled:animate-none cursor-pointer"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="opacity-75 cursor-wait"
                                    wire:target="processChangePlan"
                                    {{ (!$conektaTokenId) ? 'disabled' : '' }}>
                                <span wire:loading.class="hidden"
                                      wire:target="processChangePlan">{{ ($conektaTokenId) ? '¡Cambiar plan ahora!' : 'Cambiar plan' }}</span>
                                <span wire:loading.class.remove="hidden" wire:target="processChangePlan"
                                      class="hidden flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                            stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Procesando...
                            </span>
                            </button>
                        </div>
                    </div>
                @endif
            </form>
        @endif
    </div>

    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('refreshPage', () => {
                // Esperar 2 segundos y luego redirigir al dashboard
                setTimeout(() => {
                    window.location.href = '{{ route('dashboard') }}';
                }, 2000);
            });

            // Manejar el evento planActivationStep para hacer scroll al formulario
            Livewire.on('planActivationStep', () => {
                // Esperar a que el DOM se actualice
                setTimeout(() => {
                    // Hacer scroll suave al formulario
                    const formElement = document.getElementById('datos-formulario');
                    if (formElement) {
                        formElement.scrollIntoView({behavior: 'smooth', block: 'start'});
                    }
                }, 100);
            });

            // Manejar el evento formStepReady para hacer scroll al formulario
            Livewire.on('formStepReady', () => {
                // Esperar a que el DOM se actualice
                setTimeout(() => {
                    // Hacer scroll suave al formulario
                    const formElement = document.getElementById('datos-formulario');
                    if (formElement) {
                        formElement.scrollIntoView({behavior: 'smooth', block: 'start'});
                    }
                }, 100);
            });
        });
    </script>
</div>
