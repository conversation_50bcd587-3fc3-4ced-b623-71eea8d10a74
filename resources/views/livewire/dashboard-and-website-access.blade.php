<div class="grid gap-6 md:grid-cols-2 mb-6" x-data="{
    accessPanel(dashboardUrl, usuario, authToken) {
        // Crear un formulario dinámico para el POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = dashboardUrl;
        form.target = '_blank'; // Abrir en nueva ventana

        // Campo usuario
        const usuarioInput = document.createElement('input');
        usuarioInput.type = 'hidden';
        usuarioInput.name = 'usuario';
        usuarioInput.value = usuario;
        form.appendChild(usuarioInput);

        // Campo token
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = 'token';
        tokenInput.value = authToken;
        form.appendChild(tokenInput);

        // Agregar el formulario al DOM y enviarlo
        document.body.appendChild(form);
        form.submit();

        // Limpiar el formulario del DOM
        document.body.removeChild(form);
    }
}">
    <!-- Panel de Control Card -->
    <div class="relative overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
        <h2 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-600 dark:text-indigo-400"
                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            Ir a mi Panel de Control
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{!! __('AccessToYourDashboard') !!}</p>
        <button type="button" @click="accessPanel('{{ $dashboardUrl }}', '{{ $usuario }}', '{{ $authToken }}')"
                class="flex items-center justify-between w-full py-4 px-6 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg text-center transition-colors duration-300 group-hover:shadow-lg cursor-pointer">
            <span class="text-lg">{{ __('Go to my dashboard') }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"/>
            </svg>
        </button>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
            <a href="{{ $dashboardUrl }}" target="_blank" class="text-indigo-600 hover:text-indigo-700">
                {{ $dashboardUrl }}
            </a>
        </p>
    </div>

    <!-- Sitio Web Card -->
    <div class="relative overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-900 p-6 shadow-sm hover:shadow-md transition-all duration-300 group">
        <h2 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-emerald-600 dark:text-emerald-400"
                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
            </svg>
            Ir a mi Sitio Web
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">Visita tu sitio web público donde se muestran tus
            propiedades a los clientes potenciales.</p>
        <a href="{{ $websiteUrl }}" target="_blank"
           class="flex items-center justify-between w-full py-4 px-6 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg text-center transition-colors duration-300 group-hover:shadow-lg">
            <span class="text-lg">{{ __('Go to my website') }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"/>
            </svg>
        </a>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
            <a href="{{ $websiteUrl }}" target="_blank" class="text-emerald-600 hover:text-emerald-700">
                {{ $websiteUrl }}
            </a>
        </p>
    </div>
</div>

