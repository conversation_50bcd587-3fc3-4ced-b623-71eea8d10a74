@php
    use App\Models\Contrato;use Illuminate\Support\Facades\Auth;
    use Carbon\Carbon;
    // Establecer locale para las fechas
    Carbon::setLocale('es');
@endphp
<div class="space-y-8">
    <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900">{{ $isChangingPlan ? 'Cambiar su plan actual' : 'Seleccione su plan' }}</h2>
        <p class="mt-2 text-sm text-gray-600 max-w-2xl mx-auto">
            @if ($isChangingPlan)
                Seleccione el nuevo plan al que desea cambiar. El cambio se aplicará inmediatamente.
            @else
                Elija el plan que mejor se adapte a sus necesidades. Puede cambiar de plan en cualquier momento.
            @endif
        </p>
    </div>

    @php
        // Obtener el contrato del usuario actual (si no se ha hecho antes)
        if (!isset($contrato) || !isset($vigencia)) {
            $user = Auth::user();
            $contrato = $user->contratos()
                ->select('contratos.numero as contrato', 'por_cobrar.servicio', 'por_cobrar.hasta')
                ->where('contratos.status', 1)
                ->join('por_cobrar', 'contratos.numero', '=', 'por_cobrar.contrato')
                ->join('servicios', 'por_cobrar.servicio', '=', 'servicios.servicio')
                ->where('servicios.tipo', 'PLAN-SI')
                ->orderBy('por_cobrar.numero', 'desc')
                ->first();
            $vigencia = null;
            if ($contrato && $contrato->servicio !== 'SI-FREE') {
                $vigencia = new Carbon($contrato->hasta);
            }
        }
    @endphp

            <!-- Selector de planes -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        @foreach ($plans as $planId => $plan)
            <div
                    wire:click="selectPlan('{{ $planId }}')"
                    class="relative border rounded-lg shadow-sm p-6 transition-all duration-200 transform hover:scale-105 mb-4
                        {{ $selectedPlan === $planId ? 'border-2 border-indigo-500 bg-indigo-50' : 'border-gray-200 bg-white cursor-pointer' }}
                        {{ ($isChangingPlan && $currentPlanId === $planId) ? 'ring-2 ring-green-500' : '' }}"
            >
                @if ($isChangingPlan
                    && (
                    ($currentPlanId === $planId && $currentBillingPeriod === $billingPeriod) ||
                    ($planId === 'SI-FREE' && $addMsComplement && $currentPlanId === 'SI-MS' && $currentBillingPeriod === $billingPeriod)
                    )
                )
                    <div class="absolute top-0 right-0 mt-2 mr-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-300">
                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3"/>
                            </svg>
                            Plan actual
                            @if (($planId === 'SI-FREE' && $addMsComplement) || ($contrato->servicio === 'SI-MS'))
                                + Micrositio
                            @endif
                        </span>
                    </div>
                @endif

                <!-- Mostrar la vigencia del contrato si el plan seleccionado es el actual -->
                @if ($vigencia && ($contrato->servicio === $planId || ($planId === 'SI-FREE' && $contrato->servicio === 'SI-MS')))
                    <div class="absolute bottom-[-12px] right-4 flex justify-center">
                            <span class="inline-flex rounded-full bg-yellow-50 px-3 py-1 text-xs font-medium text-blue-800 shadow-sm border-2 border-indigo-500 dark:border-gray-200 dark:border-gray-700">
                                Vigente hasta {{ $vigencia->translatedFormat('d M Y') }}
                            </span>
                    </div>
                @endif

                @if ($plan['popular'])
                    <div class="absolute top-0 inset-x-0 -mt-3 flex justify-center">
                        <span class="inline-flex rounded-full bg-indigo-600 px-4 py-1 text-xs font-semibold tracking-wider uppercase text-white shadow-sm">
                            MÁS POPULAR
                        </span>
                    </div>
                @endif

                <div class="flex flex-col h-full">
                    <div>
                        <h3 class="text-xl font-medium text-gray-900 {{ $plan['popular'] ? 'mt-3' : '' }}">{{ $plan['name'] }}</h3>
                        <p class="mt-2 text-sm text-gray-500">{{ $plan['description'] }}</p>

                        <div class="mt-4">
                            <span class="text-3xl font-extrabold text-gray-900">
                                ${{ $planId === 'freemium' ? '0' : number_format($plan['price'], 0) }}
                            </span>
                            <span class="text-base font-medium text-gray-500">MXN/mes</span>
                        </div>
                    </div>

                    <div class="mt-6 flex-grow" wire:ignore>
                        <details class="bg-gray-50 rounded-lg p-2 cursor-pointer">
                            <summary class="text-sm font-medium text-gray-700 hover:text-gray-900">
                                Ver características del plan
                            </summary>
                            <ul class="space-y-3 mt-3">
                                <li class="flex items-start">
                                    <span class="ml-2 text-sm text-gray-500 italic">...</span>
                                </li>

                                @php
                                    // Determinar cuántas características mostrar (máximo 4)
                                    $featuresToShow = [];
                                    $featuresCount = 0;
                                    $maxFeatures = 4;

                                    foreach ($plan['features'] as $feature) {
                                        if (($feature['active'] ?? true) === false) {
                                            continue;
                                        }
                                        if ($featuresCount < $maxFeatures) {
                                            $featuresToShow[] = $feature;
                                            $featuresCount++;
                                        }
                                    }
                                @endphp

                                @foreach ($featuresToShow as $feature)
                                    @php
                                        // Determinar si la característica tiene detalles o explicación
                                        $featureTitle = is_array($feature) ? ($feature['title'] ?? $feature) : $feature;
                                        $isDisabled = strpos($featureTitle, 'line-through') !== false || strpos($featureTitle, 'no ') === 0;
                                        $isNewIntegration = is_array($feature) && isset($feature['active']) && $feature['active'] === true;
                                    @endphp
                                    <li class="flex items-start">
                                        @if($isDisabled)
                                            <svg class="h-5 w-5 text-red-400 flex-shrink-0"
                                                 xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                        @else
                                            <svg class="h-5 w-5 text-green-500 flex-shrink-0"
                                                 xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                        @endif

                                        <span class="ml-2 {{ $isDisabled ? 'text-sm line-through text-gray-400' : 'text-sm text-gray-600' }}">
                                            {!! $featureTitle !!}
                                            @if($isNewIntegration)
                                                <span class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                    Nuevo
                                                </span>
                                            @endif
                                        </span>
                                    </li>
                                @endforeach

                                <li class="flex items-start">
                                    <a href="{{ route('home') }}#precios" target="_blank"
                                       class="ml-2 text-sm text-gray-500 italic">(ver
                                        plan)...</a>
                                </li>
                            </ul>
                        </details>
                    </div>

                    <a class="mt-6" href="#continuar-plan">
                        <button
                                type="button"
                                wire:click="selectPlan('{{ $planId }}')"
                                class="w-full py-2 px-4 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                            {{ $selectedPlan === $planId
                                ? 'bg-indigo-600 text-white border-transparent hover:bg-indigo-700 cursor-default'
                                : ($planId === 'freemium'
                                    ? 'bg-gray-200 text-gray-800 border-transparent hover:bg-gray-300 cursor-pointer'
                                    : 'bg-white text-indigo-600 border-indigo-600 hover:bg-indigo-50 cursor-pointer')
                            }}"
                        >
                            {{ $selectedPlan === $planId ? 'Plan seleccionado' : 'Seleccionar plan' }}
                        </button>
                    </a>
                </div>
            </div>
        @endforeach
    </div>

    <div class="mt-10 bg-white p-6 border border-gray-200 rounded-lg shadow-sm" id="continuar-plan">
    @if ($selectedPlan && $selectedPlan !== 'SI-FREE')
            <h3 class="text-lg font-medium text-gray-900 text-center">Período de facturación</h3>
            <p class="mt-1 text-sm text-gray-600 text-center max-w-2xl mx-auto">Elija el período de facturación que
                prefiera. A mayor período, mayor descuento.</p>

            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
                @foreach ($billingOptions as $periodId => $period)
                    <div
                            wire:click="selectBillingPeriod('{{ $periodId }}')"
                            class="relative border rounded-lg p-4 cursor-pointer transition-all duration-200 mb-4
                                {{ $billingPeriod === $periodId ? 'border-2 border-indigo-500 bg-indigo-50' : 'border-gray-200 bg-white' }}
                                {{ ($isChangingPlan && (
                                    ($currentPlanId === $selectedPlan && $currentBillingPeriod === $periodId) || 
                                    ($selectedPlan === 'SI-FREE' && $addMsComplement && $currentPlanId === 'SI-MS' && $currentBillingPeriod === $periodId)
                                )) ? 'ring-2 ring-green-500' : '' }}"
                    >
                        @if ($isChangingPlan && (
                            ($currentPlanId === $selectedPlan && $currentBillingPeriod === $periodId) || 
                            ($selectedPlan === 'SI-FREE' && $addMsComplement && $currentPlanId === 'SI-MS' && $currentBillingPeriod === $periodId)
                        ))
                            <div class="absolute top-0 right-0 mt-1 mr-1">
                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="-ml-0.5 mr-1 h-2 w-2 text-green-400" fill="currentColor"
                                         viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3"/>
                                    </svg>
                                    Actual
                                </span>
                            </div>

                            @php
                                // Obtener el contrato del usuario actual (si no se ha hecho antes)
                                if (!isset($contrato) || !isset($vigencia)) {
                                    $user = Auth::user();
                                    $contrato = Contrato::where('usuario', $user->usuario)
                                        ->where('status', 1)
                                        ->where('servicio', $currentPlanId === 'SI-MS' ? 'SI-FREE' : $currentPlanId)
                                        ->first();
                                    $vigencia = null;
                                    if ($contrato) {
                                        $vigencia = $contrato->recuperarVigencia();
                                    }
                                }
                            @endphp
                        @endif
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ $period['name'] }}</h4>
                                @if ($period['discount'] > 0)
                                    <p class="text-sm text-green-600">{{ $period['discount'] }}% descuento</p>
                                @else
                                    <p class="text-sm text-gray-400">Sin descuento</p>
                                @endif
                            </div>
                            <div class="h-5 w-5 rounded-full border-2 {{ $billingPeriod === $periodId ? 'border-indigo-500 bg-indigo-500' : 'border-gray-300' }}">
                                @if ($billingPeriod === $periodId)
                                    <svg class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Resumen de precios -->
            <div class="mt-8 bg-gray-50 p-6 rounded-lg max-w-2xl mx-auto">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-4 md:mb-0 text-center md:text-left">
                        <h4 class="font-medium text-gray-900 text-lg">Resumen</h4>
                        <p class="text-sm text-gray-600">
                            {{ $plans[$selectedPlan]['name'] }} - {{ $billingOptions[$billingPeriod]['name'] }}
                        </p>
                    </div>
                    <div class="text-center md:text-right">
                        <div class="text-2xl font-bold text-gray-900">${{ number_format($totalPrice, 0) }} MXN</div>
                        @if ($savings > 0)
                            <div class="text-sm text-green-600">Ahorro: ${{ number_format($savings, 0) }} MXN</div>
                        @endif
                    </div>
                </div>
            </div>
    @elseif ($selectedPlan === 'SI-FREE')
        <!-- Complemento para Plan FREE -->
            <h3 class="text-lg font-medium text-gray-900 text-center">Complementos disponibles</h3>
            <p class="mt-1 text-sm text-gray-600 text-center max-w-2xl mx-auto">Mejore su experiencia con el Plan FREE
                añadiendo complementos opcionales</p>

            <div class="mt-6 w-full">
                @if ($msComplement)
                    <div class="border rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ $msComplement['name'] }}</h4>
                                <p class="mt-1 text-sm text-gray-600">{{ $msComplement['description'] }}</p>

                                <ul class="mt-3 space-y-2">
                                    @if (isset($msComplement['features']) && is_array($msComplement['features']))
                                        @foreach ($msComplement['features'] as $feature)
                                            @php
                                                // Determinar si la característica tiene detalles o explicación
                                                $featureTitle = is_array($feature) ? ($feature['title'] ?? $feature) : $feature;
                                                $isDisabled = str_contains($featureTitle, 'line-through') || str_starts_with($featureTitle, 'no ');
                                                $isNewIntegration = is_array($feature) && isset($feature['active']) && $feature['active'] === true;
                                            @endphp
                                            <li class="flex items-start">
                                                @if($isDisabled)
                                                    <svg class="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5"
                                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                         fill="currentColor">
                                                        <path fill-rule="evenodd"
                                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                              clip-rule="evenodd"/>
                                                    </svg>
                                                @else
                                                    <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                         fill="currentColor">
                                                        <path fill-rule="evenodd"
                                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                              clip-rule="evenodd"/>
                                                    </svg>
                                                @endif
                                                <span class="ml-2 {{ $isDisabled ? 'text-sm line-through text-gray-400' : 'text-sm text-gray-600' }}">
                                                {!! $featureTitle !!}
                                                    @if($isNewIntegration)
                                                        <span class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">Nuevo</span>
                                                    @endif
                                            </span>
                                            </li>
                                        @endforeach
                                    @else
                                        <li class="flex items-start">
                                            <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                                 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span class="ml-2 text-sm text-gray-600">Publicación automática en redes sociales</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                                 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span class="ml-2 text-sm text-gray-600">Estadísticas de interacción</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                                 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span class="ml-2 text-sm text-gray-600">Plantillas optimizadas para marketing</span>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                            <div class="mt-4 sm:mt-0 sm:ml-6 sm:text-right">
                                <div class="text-xl font-bold text-gray-900">
                                    ${{ number_format($msComplement['price'], 0) }} MXN/mes
                                </div>
                                <div class="mt-2">
                                    <label for="toggle-ms" class="inline-flex items-center cursor-pointer">
                                        <span class="mr-3 text-sm font-medium text-gray-900">Añadir</span>
                                        <div class="relative">
                                            <input type="checkbox" id="toggle-ms" wire:model.live="addMsComplement"
                                                   class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="border rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">Complemento Micrositio</h4>
                                <p class="mt-1 text-sm text-gray-600">Publica hasta 50 de tus inmuebles en tu propia
                                    página web.</p>

                                <ul class="mt-3 space-y-2">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                             xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span class="ml-2 text-sm text-gray-600">Publicación automática en redes sociales</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                             xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span class="ml-2 text-sm text-gray-600">Estadísticas de interacción</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5"
                                             xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span class="ml-2 text-sm text-gray-600">Plantillas optimizadas para marketing</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="mt-4 sm:mt-0 sm:ml-6 sm:text-right">
                                <div class="text-xl font-bold text-gray-900">$199 MXN/mes</div>
                                <div class="mt-2">
                                    <label for="toggle-ms" class="inline-flex items-center cursor-pointer">
                                        <span class="mr-3 text-sm font-medium text-gray-900">Añadir</span>
                                        <div class="relative">
                                            <input type="checkbox" id="toggle-ms" wire:model.live="addMsComplement"
                                                   class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Resumen de precios si se selecciona el complemento -->
            @if ($addMsComplement)
                <div class="mt-8 bg-gray-50 p-6 rounded-lg max-w-2xl mx-auto">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="mb-4 md:mb-0 text-center md:text-left">
                            <h4 class="font-medium text-gray-900 text-lg">Resumen</h4>
                            <p class="text-sm text-gray-600">
                                Plan FREE + {{ $msComplement ? $msComplement['name'] : 'Complemento Micrositio' }}
                            </p>
                        </div>
                        <div class="text-center md:text-right">
                            <div class="text-2xl font-bold text-gray-900">
                                ${{ $msComplement ? number_format($msComplement['price'], 0) : '199' }} MXN/mes
                            </div>
                        </div>
                    </div>
                </div>
            @endif
    @endif
    </div>

    <!-- Botón para continuar -->
    <div class="mt-8 flex justify-center">
        <button
                type="button"
                wire:click="continueToPlanActivation"
                @if ($isChangingPlan && !$isPlanDifferent) disabled @endif
                class="py-3 px-8 border border-transparent rounded-md shadow-lg text-base font-medium text-white
                    {{ ($isChangingPlan && !$isPlanDifferent)
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 cursor-pointer' }}"
        >
            @if ($isChangingPlan)
                @if (!$isPlanDifferent)
                    Este es su plan actual
                @elseif ($hasValidatedCard)
                    Cambiar a este plan
                @else
                    Continuar con el cambio de plan
                @endif
            @else
                Continuar con este plan
            @endif
        </button>
    </div>

    @if ($isChangingPlan)
        @if (!$isPlanDifferent)
            <div class="mt-4 text-center text-sm text-gray-600">
                <p>Ya tiene este plan activo. Seleccione un plan diferente para cambiar.</p>
            </div>
        @elseif ($hasValidatedCard)
            <div class="mt-4 text-center text-sm text-gray-600">
                <p>Ya tiene una tarjeta registrada, no necesitará volver a introducir sus datos de pago.</p>
            </div>
        @endif
    @endif
</div>

<script>
    document.addEventListener('livewire:initialized', () => {
        // Escuchar ambos eventos: cuando se selecciona el plan y cuando el formulario está listo
        Livewire.on('planActivationStep', handleFormScroll);
        Livewire.on('formStepReady', handleFormScroll);

        function handleFormScroll() {
            // Intentar hacer scroll inmediatamente
            tryScrollToForm();

            // Y también intentar después de un retraso para asegurar que el DOM esté actualizado
            setTimeout(tryScrollToForm, 300);
            setTimeout(tryScrollToForm, 600); // Tercer intento por si acaso
        }

        function tryScrollToForm() {
            const formElement = document.getElementById('datos-formulario');
            if (formElement) {
                formElement.scrollIntoView({behavior: 'smooth'});
            }
        }
    });
</script>
