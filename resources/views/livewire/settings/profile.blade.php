<?php

use App\Models\User;
use App\Notifications\EmailChangeNotification;
use App\Notifications\EmailChangeRequestNotification;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Livewire\Volt\Component;
use Livewire\WithFileUploads;
use App\Livewire\Traits\SyncWithMeteorTrait;

new class extends Component {
    use WithFileUploads;
    use SyncWithMeteorTrait;

    public string $name = '';
    public string $email = '';
    public string $new_email = '';
    public string $current_password = '';
    public bool $showEmailChangeForm = false;
    public $avatar;
    public string $empresa = '';
    public string $telefono = '';

    // Campos de domicilio
    public string $calle_numero = '';
    public string $colonia = '';
    public int $codigo_postal = 0;
    public string $ciudad = '';
    public string $estado = '';
    public string $pais = 'México';
    public ?string $como_llegar = null;

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $user = Auth::user();

        $this->name = $user->name;
        $this->email = $user->email;
        $this->empresa = $user->empresa ?? '';
        $this->telefono = $user->telefono ?? '';

        // Inicializar campos de domicilio
        $this->calle_numero = $user->calle_numero ?? '';
        $this->colonia = $user->colonia ?? '';
        $this->codigo_postal = $user->codigo_postal ?? 0;
        $this->ciudad = $user->ciudad ?? '';
        $this->estado = $user->estado ?? '';
        $this->pais = $user->pais ?? 'México';
        $this->como_llegar = $user->como_llegar;

        // Si hay un correo pendiente de verificación, mostrarlo
        if ($user->pending_email) {
            $this->new_email = $user->pending_email;
            Session::flash('status', 'verification-link-sent-to-new-email');
        } else {
            $this->new_email = $this->email;
        }
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function updateProfileInformation(): void
    {
        $user = Auth::user();

        // Validar los campos básicos del perfil
        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'empresa' => ['nullable', 'string', 'max:50'],
            'telefono' => ['nullable', 'string', 'max:30'],
            'calle_numero' => ['nullable', 'string', 'max:70'],
            'colonia' => ['nullable', 'string', 'max:30'],
            'codigo_postal' => ['nullable', 'integer'],
            'ciudad' => ['nullable', 'string', 'max:30'],
            'estado' => ['nullable', 'string', 'max:25'],
            'pais' => ['nullable', 'string', 'max:25'],
            'como_llegar' => ['nullable', 'string'],
            'avatar' => ['nullable', 'image', 'max:1024'], // 1MB Max
        ]);

        // Verificar si el correo electrónico ha cambiado
        $emailChanged = $this->new_email !== $user->email;

        // Si el correo ha cambiado, mostrar el formulario para confirmar con contraseña
        if ($emailChanged && !$this->showEmailChangeForm) {
            $this->validate(
                [
                    'new_email' => [
                        'required',
                        'string',
                        'lowercase',
                        'email',
                        'max:255',
                        Rule::unique(User::class, 'email')->ignore($user->id),
                        Rule::unique(User::class, 'logmail')->ignore($user->id)
                    ],
                ],
                [
                    'new_email.unique' => __('El correo electrónico ya está relacionado a otra cuenta.'),
                    'new_email.required' => __('El correo electrónico es obligatorio.'),
                    'new_email.email' => __('El formato del correo electrónico no es válido.'),
                ]
            );

            $this->showEmailChangeForm = true;
            return;
        }

        // Si hay cambio de correo y el formulario está visible, procesar el cambio
        if ($emailChanged && $this->showEmailChangeForm) {
            try {
                // Verificar la contraseña actual
                if (!Hash::check($this->current_password, $user->password)) {
                    throw ValidationException::withMessages([
                        'current_password' => [__('La contraseña actual es incorrecta.')],
                    ]);
                }

                // Enviar correo de verificación a la nueva dirección
                $user->pending_email = $this->new_email;
                $user->pending_email_at = now();
                $user->save();

                // Enviar notificación al correo actual informando sobre el cambio
                $user->notify(new EmailChangeRequestNotification($this->new_email));

                // Enviar notificación al nuevo correo para confirmar
                $user->notifyNow(new EmailChangeNotification($this->new_email));

                // Guardar el nuevo correo en la sesión para referencia
                Session::put('pending_email_change', $this->new_email);

                // Separar el nombre completo en nombre y apellidos para Meteor
                $nameParts = explode(' ', trim($validated['name']), 2);
                $nombre = $nameParts[0] ?? '';
                $apellidos = $nameParts[1] ?? '';

                // Actualizar solo el nombre, empresa, teléfono, domicilio y avatar, no el correo todavía
                $user->fill([
                    'name' => $validated['name'],
                    'nombre' => $nombre,
                    'apellidos' => $apellidos,
                    'empresa' => $validated['empresa'],
                    'telefono' => $validated['telefono'],
                    'calle_numero' => $validated['calle_numero'],
                    'colonia' => $validated['colonia'],
                    'codigo_postal' => $validated['codigo_postal'],
                    'ciudad' => $validated['ciudad'],
                    'estado' => $validated['estado'],
                    'pais' => $validated['pais'],
                    'como_llegar' => $validated['como_llegar'],
                ]);

                if ($this->avatar) {
                    // Procesar y recortar la imagen a cuadrado perfecto
                    $avatarPath = $this->processAndStoreAvatar($this->avatar);
                    $user->avatar = Storage::url($avatarPath);
                }

                $user->save();

                // Sincronizar todos los campos actualizados con Meteor
                $camposActualizados = ['nombre', 'apellidos', 'empresa', 'telefono', 'ciudad', 'estado'];
                if ($this->avatar) {
                    $camposActualizados[] = 'avatar';
                }
                $this->sincronizarConMeteor($user, $camposActualizados);

                $this->reset('current_password');
                $this->showEmailChangeForm = false;

                Session::flash('status', 'verification-link-sent-to-new-email');
                $this->dispatch('profile-updated', name: $user->name);

                // Limpiar el avatar temporal después de guardarlo exitosamente
                $this->reset('avatar');

                return;
            } catch (ValidationException $e) {
                $this->reset('current_password');
                throw $e;
            }
        }

        // Si no hay cambio de correo, actualizar normalmente
        // Separar el nombre completo en nombre y apellidos para Meteor
        $nameParts = explode(' ', trim($validated['name']), 2);
        $nombre = $nameParts[0] ?? '';
        $apellidos = $nameParts[1] ?? '';

        $user->fill([
            'name' => $validated['name'],
            'nombre' => $nombre,
            'apellidos' => $apellidos,
            'empresa' => $validated['empresa'],
            'telefono' => $validated['telefono'],
            'calle_numero' => $validated['calle_numero'],
            'colonia' => $validated['colonia'],
            'codigo_postal' => $validated['codigo_postal'],
            'ciudad' => $validated['ciudad'],
            'estado' => $validated['estado'],
            'pais' => $validated['pais'],
            'como_llegar' => $validated['como_llegar'],
        ]);

        if ($this->avatar) {
            // Procesar y recortar la imagen a cuadrado perfecto
            $avatarPath = $this->processAndStoreAvatar($this->avatar);
            $user->avatar = Storage::url($avatarPath);
        }

        $user->save();

        // Sincronizar todos los campos actualizados con Meteor
        $camposActualizados = ['nombre', 'apellidos', 'empresa', 'telefono', 'ciudad', 'estado'];
        if ($this->avatar) {
            $camposActualizados[] = 'avatar';
        }
        $this->sincronizarConMeteor($user, $camposActualizados);

        // Limpiar el avatar temporal después de guardarlo exitosamente
        $this->reset('avatar');

        $this->dispatch('profile-updated', name: $user->name);
    }

    /**
     * Cancelar el proceso de cambio de correo electrónico.
     */
    public function cancelEmailChange(): void
    {
        $user = Auth::user();

        // Limpiar el correo pendiente en la base de datos
        if ($user->pending_email) {
            $user->pending_email = null;
            $user->pending_email_at = null;
            $user->save();
        }

        $this->new_email = $user->email;
        $this->reset('current_password');
        $this->showEmailChangeForm = false;
        Session::forget('pending_email_change');
    }

    // Propiedad para almacenar el mensaje de error del temporizador
    public $verificationTimerMessage = '';

    /**
     * Reenviar el correo de verificación para el cambio de correo electrónico.
     */
    public function resendEmailChangeVerification(): void
    {
        $user = Auth::user();

        if (!$user->pending_email) {
            return;
        }

        // Verificar si han pasado al menos 2 minutos desde el último envío
        $lastVerificationSent = Session::get('last_email_change_verification_sent');

        if ($lastVerificationSent) {
            $now = now();
            $lastSent = Carbon::parse($lastVerificationSent);

            // Calcular el tiempo transcurrido en segundos desde el último envío
            $elapsedSeconds = $now->timestamp - $lastSent->timestamp;

            // Calcular el tiempo restante (120 segundos - tiempo transcurrido)
            $remainingSeconds = 120 - $elapsedSeconds;

            // Si aún queda tiempo de espera
            if ($remainingSeconds > 0) {
                // Asegurarse de que timeRemaining sea un entero
                $timeRemaining = (int)$remainingSeconds;
                $this->verificationTimerMessage = "Espere {$timeRemaining} segundos antes de solicitar otro correo de verificación.";
                Session::flash('verification-timer', $this->verificationTimerMessage);
                return;
            }
        }

        // Enviar correo de verificación a la nueva dirección
        $user->notifyNow(new EmailChangeNotification($user->pending_email));

        // Guardar la marca de tiempo actual como referencia para el temporizador
        Session::put('last_email_change_verification_sent', Carbon::now());

        Session::flash('status', 'verification-link-sent-to-new-email');
    }

    /**
     * Procesa y recorta la imagen del avatar a un cuadrado perfecto
     */
    private function processAndStoreAvatar($uploadedFile): string
    {
        // Verificar que la extensión GD esté disponible
        if (!extension_loaded('gd')) {
            throw new \Exception('La extensión GD no está disponible para procesar imágenes');
        }

        // Eliminar avatar anterior si existe
        $this->deleteOldAvatar();
        
        // Obtener información de la imagen
        $imageInfo = getimagesize($uploadedFile->getRealPath());
        if (!$imageInfo) {
            throw new \Exception('No se pudo obtener información de la imagen. Asegúrate de subir una imagen válida.');
        }
        
        $mimeType = $imageInfo['mime'];
        $image = null;
        
        // Crear imagen según el tipo detectado
        switch ($mimeType) {
            case 'image/jpeg':
            case 'image/jpg':
                if (!function_exists('imagecreatefromjpeg')) {
                    throw new \Exception('Soporte JPEG no disponible en esta instalación de PHP. Por favor, convierte la imagen a PNG antes de subirla.');
                }
                $image = imagecreatefromjpeg($uploadedFile->getRealPath());
                break;
                
            case 'image/png':
                if (!function_exists('imagecreatefrompng')) {
                    throw new \Exception('Soporte PNG no disponible en esta instalación de PHP.');
                }
                $image = imagecreatefrompng($uploadedFile->getRealPath());
                break;
                
            case 'image/gif':
                if (!function_exists('imagecreatefromgif')) {
                    throw new \Exception('Soporte GIF no disponible en esta instalación de PHP.');
                }
                $image = imagecreatefromgif($uploadedFile->getRealPath());
                break;
                
            default:
                throw new \Exception("Formato de imagen no soportado: {$mimeType}. Solo se permiten JPG, PNG y GIF.");
        }
        
        if (!$image) {
            throw new \Exception("No se pudo procesar la imagen. Formato detectado: {$mimeType}. Asegúrate de subir una imagen válida (JPG, PNG, GIF).");
        }
        
        // Obtener dimensiones originales
        $originalWidth = imagesx($image);
        $originalHeight = imagesy($image);
        
        // Calcular el tamaño del cuadrado (el menor de los dos lados)
        $squareSize = min($originalWidth, $originalHeight);
        
        // Calcular las coordenadas para centrar el recorte
        $x = ($originalWidth - $squareSize) / 2;
        $y = ($originalHeight - $squareSize) / 2;
        
        // Crear una nueva imagen cuadrada
        $squareImage = imagecreatetruecolor($squareSize, $squareSize);
        
        // Preservar transparencia para PNG
        imagealphablending($squareImage, false);
        imagesavealpha($squareImage, true);
        
        // Crear color transparente para el fondo
        $transparent = imagecolorallocatealpha($squareImage, 0, 0, 0, 127);
        imagefill($squareImage, 0, 0, $transparent);
        
        // Recortar la imagen desde el centro
        imagecopy($squareImage, $image, 0, 0, $x, $y, $squareSize, $squareSize);
        
        // Generar nombre único para el archivo
        $filename = 'avatar_' . auth()->id() . '_' . time() . '.png';
        $path = 'avatars/' . $filename;
        $fullPath = storage_path('app/public/' . $path);
        
        // Crear directorio si no existe
        if (!file_exists(dirname($fullPath))) {
            mkdir(dirname($fullPath), 0755, true);
        }
        
        // Guardar como PNG
        imagepng($squareImage, $fullPath, 8); // Compresión nivel 8 (0-9)
        
        // Liberar memoria
        imagedestroy($image);
        imagedestroy($squareImage);
        
        return $path;
    }

    /**
     * Elimina el avatar anterior del usuario
     */
    private function deleteOldAvatar(): void
    {
        $user = auth()->user();
        
        if ($user->avatar) {
            // Extraer el path relativo del avatar actual
            $oldAvatarPath = str_replace('/storage/', '', $user->avatar);
            $fullOldPath = storage_path('app/public/' . $oldAvatarPath);
            
            // Eliminar el archivo si existe
            if (file_exists($fullOldPath)) {
                unlink($fullOldPath);
            }
        }
    }

    /**
     * Send an email verification notification to the current user.
     */
    public function resendVerificationNotification(): void
    {
        $user = Auth::user();

        if ($user->hasVerifiedEmail()) {
            $this->redirectIntended(default: route('dashboard', absolute: false));
            return;
        }

        // Verificar si han pasado al menos 2 minutos desde el último envío
        $lastVerificationSent = Session::get('last_verification_sent');

        if ($lastVerificationSent) {
            $now = now();
            $lastSent = Carbon::parse($lastVerificationSent);

            // Calcular el tiempo transcurrido en segundos desde el último envío
            $elapsedSeconds = $now->timestamp - $lastSent->timestamp;

            // Calcular el tiempo restante (120 segundos - tiempo transcurrido)
            $remainingSeconds = 120 - $elapsedSeconds;

            // Si aún queda tiempo de espera
            if ($remainingSeconds > 0) {
                // Asegurarse de que timeRemaining sea un entero
                $timeRemaining = (int)$remainingSeconds;
                $this->verificationTimerMessage = "Espere {$timeRemaining} segundos antes de solicitar otro correo de verificación.";
                Session::flash('verification-timer', $this->verificationTimerMessage);
                return;
            }
        }

        $user->sendEmailVerificationNotification();

        // Guardar la marca de tiempo actual como referencia para el temporizador
        Session::put('last_verification_sent', Carbon::now());

        Session::flash('status', 'verification-link-sent');
    }
};

?>

<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Profile')"
                       :subheading="__('Actualiza tu información personal, nombre de agencia, teléfono y correo electrónico')">
        {{--    Colocar el usuario centrado en gris    --}}
        <div class="flex justify-center items-center p-2 w-full bg-gray-100 rounded-lg">
            <span class="text-sm font-bold text-gray-700 dark:text-gray-300">{{ auth()->user()->usuario }}</span>
        </div>
        <form wire:submit="updateProfileInformation" class="my-6 w-full space-y-6">
            <div class="mb-6">
                <label class="block text-sm font-medium mb-2">{{ __('Foto de perfil') }}</label>
                <div class="flex flex-col md:flex-row md:items-center gap-6">
                    <div class="flex flex-col items-center gap-3">
                        <div class="relative group">
                            @if ($avatar)
                                <img class="h-24 w-24 object-cover rounded-full border-2 border-indigo-100 dark:border-indigo-900 shadow-sm"
                                     src="{{ $avatar->temporaryUrl() }}"
                                     alt="{{ __('Profile picture preview') }}">
                            @elseif (auth()->user()->getAvatarUrl())
                                <img class="h-24 w-24 object-cover rounded-full border-2 border-indigo-100 dark:border-indigo-900 shadow-sm"
                                     src="{{ auth()->user()->getAvatarUrl() }}"
                                     alt="{{ auth()->user()->name }}">
                            @else
                                <div class="h-24 w-24 rounded-full flex items-center justify-center bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300 border-2 border-indigo-100 dark:border-indigo-900 shadow-sm">
                                    <span class="text-xl font-semibold">{{ auth()->user()->initials() }}</span>
                                </div>
                            @endif
                        </div>

                        <div class="flex flex-col items-center">
                            <label class="inline-flex items-center px-4 py-2 bg-indigo-50 text-indigo-700 rounded-md border border-indigo-100 cursor-pointer hover:bg-indigo-100 transition-colors dark:bg-indigo-900 dark:text-indigo-300 dark:border-indigo-800 dark:hover:bg-indigo-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24"
                                     fill="none" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                                </svg>
                                <span class="text-sm font-medium">{{ __('Elegir foto') }}</span>
                                <input type="file" wire:model="avatar" accept="image/*" class="hidden">
                            </label>
                            @error('avatar')
                            <span class="text-sm text-red-600 dark:text-red-400 mt-2">{{ $message }}</span>
                            @enderror
                            <p class="text-xs text-zinc-500 dark:text-zinc-400 mt-2">{{ __('JPG, PNG o GIF. Máximo 1MB.') }}</p>
                        </div>
                    </div>

                    <div class="flex-1 bg-zinc-50 dark:bg-zinc-900/50 p-4 rounded-lg border border-zinc-200 dark:border-zinc-700">
                        <h4 class="font-medium text-sm mb-2">{{ __('Acerca de tu foto de perfil') }}</h4>
                        <p class="text-sm text-zinc-600 dark:text-zinc-400">
                            {{ __('Tu foto de perfil se mostrará en tu perfil y en toda la plataforma. Una foto clara y profesional ayuda a que otros te reconozcan.') }}
                        </p>
                    </div>
                </div>
            </div>

            <flux:input wire:model="name" :label="__('Name')" type="text" required autofocus autocomplete="name"
                        class="bg-white dark:bg-zinc-800"/>

            <flux:input wire:model="empresa" :label="__('Nombre de la agencia')" type="text" autocomplete="organization"
                        class="bg-white dark:bg-zinc-800"/>

            <flux:input wire:model="telefono" :label="__('Teléfono')" type="tel" autocomplete="tel"
                        class="bg-white dark:bg-zinc-800"
                        x-mask="+99 ************"
                        maxlength="16"
            />

            <!-- Sección de domicilio -->
            <div class="mt-6 mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Domicilio') }}</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="col-span-2">
                        <flux:input wire:model="calle_numero" :label="__('Calle y número')" type="text"
                                    autocomplete="street-address"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div>
                        <flux:input wire:model="colonia" :label="__('Colonia')" type="text"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div>
                        <flux:input wire:model="codigo_postal" :label="__('Código postal')" type="number"
                                    autocomplete="postal-code"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div>
                        <flux:input wire:model="ciudad" :label="__('Ciudad')" type="text" autocomplete="address-level2"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div>
                        <flux:input wire:model="estado" :label="__('Estado')" type="text" autocomplete="address-level1"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div class="col-span-2">
                        <flux:input wire:model="pais" :label="__('País')" type="text" autocomplete="country-name"
                                    class="bg-white dark:bg-zinc-800"/>
                    </div>

                    <div class="col-span-2">
                        <flux:textarea wire:model="como_llegar" :label="__('Cómo llegar (opcional)')" rows="3"
                                       class="bg-white dark:bg-zinc-800"/>
                    </div>
                </div>
            </div>

            <div>
                <div class="mb-4">
                    <flux:input wire:model="new_email" :label="__('Email')" type="email" required autocomplete="email"
                                class="bg-white dark:bg-zinc-800"/>
                    <p class="text-xs text-zinc-500 dark:text-zinc-400 mt-2">{{ __('Para cambiar tu correo electrónico, se te pedirá verificar tu contraseña y confirmar el nuevo correo.') }}</p>
                </div>

                @if ($showEmailChangeForm)
                    <div class="mt-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg">
                        <h4 class="font-medium text-sm mb-3">{{ __('Confirmar cambio de correo electrónico') }}</h4>
                        <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
                            {{ __('Para proteger tu cuenta, por favor ingresa tu contraseña actual para confirmar este cambio.') }}
                        </p>

                        <flux:input
                                wire:model="current_password"
                                :label="__('Contraseña actual')"
                                type="password"
                                required
                                autocomplete="current-password"
                                class="bg-white dark:bg-zinc-800 mb-4"
                        />

                        <div class="flex items-center gap-3">
                            <flux:button variant="primary" type="submit"
                                         class="">{{ __('Confirmar cambio') }}</flux:button>
                            <flux:button type="button" wire:click="cancelEmailChange"
                                         class="">{{ __('Cancelar') }}</flux:button>
                        </div>
                    </div>
                @endif

                @if (session('status') === 'verification-link-sent-to-new-email' || Auth::user()->pending_email)
                    <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <flux:text class="text-green-800 dark:text-green-200">
                            {{ __('Hemos enviado un enlace de verificación a tu nueva dirección de correo electrónico:') }}
                            <strong>{{ Auth::user()->pending_email }}</strong>
                            <p class="mt-2">{{ __('Por favor revisa tu bandeja de entrada y haz clic en el enlace para completar el cambio.') }}</p>

                            @if (session('verification-timer'))
                                <span class="text-sm text-amber-600 dark:text-amber-400 block mt-2">
                                    {{ session('verification-timer') }}
                                </span>
                            @else
                                <p class="mt-2">
                                    <flux:link class="text-sm cursor-pointer font-medium"
                                               wire:click.prevent="resendEmailChangeVerification">
                                        {{ __('Haz clic aquí para reenviar el correo de verificación.') }}
                                    </flux:link>
                                </p>
                            @endif
                        </flux:text>
                        <div class="mt-3">
                            <flux:button type="button" wire:click="cancelEmailChange"
                                         class="text-sm">{{ __('Cancelar cambio de correo') }}</flux:button>
                        </div>
                    </div>
                @endif

                @if (session('status') === 'email-changed')
                    <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <flux:text class="text-green-800 dark:text-green-200">
                            {{ __('Tu dirección de correo electrónico ha sido actualizada y verificada correctamente.') }}
                        </flux:text>
                    </div>
                @endif

                @if (session('error'))
                    <div class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <flux:text class="text-red-800 dark:text-red-200">
                            {{ session('error') }}
                        </flux:text>
                    </div>
                @endif

                @if (auth()->user() instanceof MustVerifyEmail &&! auth()->user()->hasVerifiedEmail())
                    <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <flux:text class="text-yellow-800 dark:text-yellow-200">
                            {{ __('Your email address is unverified.') }}

                            @if (session('verification-timer'))
                                <span class="text-sm text-amber-600 dark:text-amber-400 block mt-1">
                                    {{ session('verification-timer') }}
                                </span>
                            @else
                                <flux:link class="text-sm cursor-pointer font-medium"
                                           wire:click.prevent="resendVerificationNotification">
                                    {{ __('Click here to re-send the verification email.') }}
                                </flux:link>
                            @endif
                        </flux:text>

                        @if (session('status') === 'verification-link-sent')
                            <flux:text class="mt-2 font-medium !text-green-600 !dark:text-green-400">
                                {{ __('A new verification link has been sent to your email address.') }}
                            </flux:text>
                        @endif
                    </div>
                @endif
            </div>

            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end">
                    <flux:button variant="primary" type="submit" class="w-full">{{ __('Save') }}</flux:button>
                </div>

                <x-action-message class="me-3" on="profile-updated">
                    {{ __('Saved.') }}
                </x-action-message>
            </div>
        </form>

        <livewire:settings.delete-user-form/>
    </x-settings.layout>
</section>
