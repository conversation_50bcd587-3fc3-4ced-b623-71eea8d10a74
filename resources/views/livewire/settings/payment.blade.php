<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Livewire\Volt\Component;

new class extends Component {
};

?>

<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Pagos y facturación')" :subheading="__('Gestiona tu suscripción, métodos de pago e información de facturación')">
        <!-- Mensajes de estado -->
        @if ($successMessage)
            <div class="mb-4 p-4 bg-green-50 border border-green-200 text-green-700 rounded-md">
                {{ $successMessage }}
            </div>
        @endif

        @if ($errorMessage)
            <div class="mb-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
                {{ $errorMessage }}
            </div>
        @endif

        <!-- Navegación por pestañas -->
        <div class="mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button wire:click="setActiveTab('subscription')" type="button"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'subscription' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }}">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            {{ __('Suscripción') }}
                        </div>
                    </button>
                    <button wire:click="setActiveTab('payment')" type="button"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'payment' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }}">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {{ __('Métodos de pago') }}
                        </div>
                    </button>
                    <button wire:click="setActiveTab('billing')" type="button"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'billing' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }}">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            {{ __('Facturación') }}
                        </div>
                    </button>
                    <button wire:click="setActiveTab('history')" type="button"
                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'history' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }}">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {{ __('Historial') }}
                        </div>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Contenido de las pestañas -->
        <div class="mt-6 space-y-6 max-w-full">
            <!-- Pestaña de Suscripción -->
            @if ($activeTab === 'subscription')
                <div class="max-w-full">
                    @if ($subscription)
                        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ __('Detalles de tu suscripción') }}</h3>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Plan actual') }}</p>
                                    <p class="font-medium">{{ $subscription->plan_name ?? 'Plan Estándar' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Estado') }}</p>
                                    <p class="font-medium">
                                        @if($subscription->status === 'active')
                                            <span class="text-green-600 dark:text-green-400">{{ __('Activa') }}</span>
                                        @elseif($subscription->status === 'canceled')
                                            <span class="text-red-600 dark:text-red-400">{{ __('Cancelada') }}</span>
                                        @elseif($subscription->status === 'paused')
                                            <span class="text-yellow-600 dark:text-yellow-400">{{ __('Pausada') }}</span>
                                        @else
                                            <span>{{ $subscription->status }}</span>
                                        @endif
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Fecha de inicio') }}</p>
                                    <p class="font-medium">{{ $subscription->created_at ? $subscription->created_at->format('d/m/Y') : 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Próximo cobro') }}</p>
                                    <p class="font-medium">{{ $subscription->current_period_end ? date('d/m/Y', strtotime($subscription->current_period_end)) : 'N/A' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Monto') }}</p>
                                    <p class="font-medium">${{ number_format($subscription->amount / 100, 2) }} MXN</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Periodo de facturación') }}</p>
                                    <p class="font-medium">{{ $subscription->billing_period === 'month' ? 'Mensual' : 'Anual' }}</p>
                                </div>
                            </div>

                            @if($subscription->status === 'active')
                                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <button type="button" wire:click="showCancelSubscriptionConfirmation" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 w-full sm:w-auto">
                                        {{ __('Cancelar suscripción') }}
                                    </button>
                                </div>
                            @elseif($subscription->status === 'canceled' && $subscription->ends_at && $subscription->ends_at->isFuture())
                                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                        {{ __('Tu suscripción ha sido cancelada pero seguirá activa hasta') }} {{ $subscription->ends_at->format('d/m/Y') }}.
                                    </p>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700 text-yellow-700 dark:text-yellow-400">
                            <p>{{ __('No tienes una suscripción activa actualmente.') }}</p>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Pestaña de Métodos de Pago -->
            @if ($activeTab === 'payment')
                <div>
                    <!-- Tarjetas guardadas -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Métodos de pago guardados') }}</h3>

                        @if (count($paymentSources) > 0)
                            <div class="space-y-3">
                                @foreach ($paymentSources as $source)
                                    <div class="flex items-center justify-between p-4 bg-white dark:bg-zinc-800 rounded-lg border {{ $defaultSourceId === $source['id'] ? 'border-indigo-300 dark:border-indigo-700 bg-indigo-50 dark:bg-indigo-900/20' : 'border-gray-200 dark:border-gray-700' }}">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                                                @if (strtolower($source['brand']) === 'visa')
                                                    <span class="text-blue-600 text-xl">Visa</span>
                                                @elseif (strtolower($source['brand']) === 'mastercard')
                                                    <span class="text-red-600 text-xl">MC</span>
                                                @elseif (strtolower($source['brand']) === 'american express')
                                                    <span class="text-blue-800 text-xl">Amex</span>
                                                @else
                                                    <span class="text-gray-600 dark:text-gray-400 text-xl">{{ $source['brand'] }}</span>
                                                @endif
                                            </div>
                                            <div>
                                                <p class="font-medium">•••• {{ $source['last4'] }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ __('Expira') }}: {{ $source['exp_month'] }}/{{ $source['exp_year'] }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            @if ($defaultSourceId !== $source['id'])
                                                <button type="button" wire:click="setDefaultCard('{{ $source['id'] }}')" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                    {{ __('Predeterminada') }}
                                                </button>
                                            @else
                                                <span class="text-xs bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-300 py-1 px-2 rounded-full">
                                                    {{ __('Predeterminada') }}
                                                </span>
                                            @endif
                                            <button type="button" wire:click="deleteCard('{{ $source['id'] }}')" class="inline-flex items-center px-2.5 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                {{ __('Eliminar') }}
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="bg-gray-50 dark:bg-zinc-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center">
                                <p class="text-gray-500 dark:text-gray-400">{{ __('No tienes métodos de pago guardados.') }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Botón para agregar tarjeta -->
                    @if (!$showAddCardForm)
                        <div class="mt-4">
                            <button type="button" wire:click="toggleAddCardForm" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Agregar método de pago') }}
                            </button>
                        </div>
                    @endif

                    <!-- Formulario para agregar tarjeta -->
                    @if ($showAddCardForm)
                        <div class="mt-6 p-4 bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Agregar nueva tarjeta') }}</h3>

                            <div id="errorConekta" class="mb-4"></div>

                            <div class="space-y-4">
                                <div>
                                    <label for="cardName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('Nombre en la tarjeta') }}</label>
                                    <input type="text" id="cardName" wire:model="cardName" placeholder="Nombre como aparece en la tarjeta"
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    <span id="errorCardName" class="text-red-500 text-xs conekta-error"></span>
                                </div>

                                <div>
                                    <label for="cardNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('Número de tarjeta') }}</label>
                                    <input type="text" id="cardNumber" wire:model="cardNumber" placeholder="1234 5678 9012 3456" x-mask="9999 9999 9999 9999"
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    <span id="errorCardNumber" class="text-red-500 text-xs conekta-error"></span>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="cardExpiry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('Fecha de expiración') }}</label>
                                        <input type="text" id="cardExpiry" wire:model="cardExpiry" placeholder="MM/AA" x-mask="99/99"
                                            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                        <span id="errorCardExpiry" class="text-red-500 text-xs conekta-error"></span>
                                    </div>
                                    <div>
                                        <label for="cardCvc" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('CVC/CVV') }}</label>
                                        <input type="password" id="cardCvc" wire:model="cardCvc" placeholder="123" x-mask="999"
                                            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                        <span id="errorCardCvc" class="text-red-500 text-xs conekta-error"></span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-end space-x-3 mt-4">
                                    <button type="button" wire:click="toggleAddCardForm" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        {{ __('Cancelar') }}
                                    </button>

                                    <button type="button" id="validateCardButton" onclick="createConektaToken()" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <span id="validateCardButtonText">{{ __('Validar tarjeta') }}</span>
                                        <span id="validateCardButtonLoading" class="hidden">
                                            <svg class="animate-spin h-5 w-5 text-gray-700 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </span>
                                    </button>

                                    <button type="button" wire:click="saveCard" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" {{ !$conektaTokenId ? 'disabled' : '' }}>
                                        {{ __('Guardar tarjeta') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Pestaña de Facturación -->
            @if ($activeTab === 'billing')
                <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Información de facturación') }}</h3>

                    <form wire:submit.prevent="updateBillingInfo" class="space-y-4">
                        <div>
                            <label for="fact_nombre" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('Nombre o razón social') }}</label>
                            <input type="text" id="fact_nombre" wire:model="fact_nombre" placeholder="{{ __('Nombre completo o razón social') }}"
                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('fact_nombre') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="fact_domicilio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('Domicilio fiscal') }}</label>
                            <input type="text" id="fact_domicilio" wire:model="fact_domicilio" placeholder="{{ __('Dirección fiscal completa') }}"
                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('fact_domicilio') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="fact_rfc" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('RFC') }}</label>
                            <input type="text" id="fact_rfc" wire:model="fact_rfc" placeholder="{{ __('RFC con homoclave') }}"
                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            @error('fact_rfc') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>

                        <div class="pt-4">
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Actualizar información') }}
                            </button>
                        </div>
                    </form>
                </div>
            @endif

            <!-- Pestaña de Historial -->
            @if ($activeTab === 'history')
                <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Historial de facturas') }}</h3>

                    @if (count($invoices) > 0)
                        <div class="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-zinc-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Fecha') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Descripción') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Monto') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Estado') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Factura') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach ($invoices as $invoice)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ $invoice->fecha->format('d/m/Y') }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ $invoice->descripcion }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${{ number_format($invoice->precio, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                @if ($invoice->pagado)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">{{ __('Pagado') }}</span>
                                                @else
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300">{{ __('Pendiente') }}</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                @if ($invoice->factura)
                                                    <a href="#" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">{{ __('Descargar') }}</a>
                                                @else
                                                    <span class="text-gray-500 dark:text-gray-400">{{ __('No disponible') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="bg-gray-50 dark:bg-zinc-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center">
                            <p class="text-gray-500 dark:text-gray-400">{{ __('No hay facturas disponibles.') }}</p>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        <!-- Modal de confirmación para cancelar suscripción -->
        @if ($showCancelSubscriptionModal)
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full mx-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('¿Estás seguro de que deseas cancelar tu suscripción?') }}</h3>

                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        {{ __('Al cancelar tu suscripción, perderás acceso a todas las funciones premium al final del periodo de facturación actual.') }}
                    </p>

                    <div class="mb-4">
                        <div>
                            <label for="cancelReason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ __('¿Por qué cancelas tu suscripción? (opcional)') }}</label>
                            <textarea id="cancelReason" wire:model="cancelReason" rows="3" placeholder="{{ __('Ayúdanos a mejorar nuestro servicio') }}" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-zinc-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" wire:click="hideCancelSubscriptionModal" class="inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {{ __('Volver') }}
                        </button>
                        <button type="button" wire:click="cancelSubscription" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            {{ __('Confirmar cancelación') }}
                        </button>
                    </div>
                </div>
            </div>
        @endif
    </x-settings.layout>
</section>

@push('scripts')
<script type="text/javascript" src="https://cdn.conekta.io/js/latest/conekta.js"></script>
<script>
    document.addEventListener('livewire:initialized', () => {
        // Configurar Conekta con la llave pública
        Conekta.setPublicKey('{{ env("CONEKTA_PUBLIC_KEY") }}');

        // Función para crear token de tarjeta
        window.createConektaToken = function() {
            // Limpiar mensajes de error previos
            document.querySelectorAll('.conekta-error').forEach(el => el.textContent = '');
            document.getElementById('errorConekta').innerHTML = '';

            // Mostrar indicador de carga
            const validateButton = document.getElementById('validateCardButton');
            const validateButtonText = document.getElementById('validateCardButtonText');
            const validateButtonLoading = document.getElementById('validateCardButtonLoading');

            validateButtonText.classList.add('hidden');
            validateButtonLoading.classList.remove('hidden');
            validateButton.disabled = true;

            // Obtener los valores del formulario
            const cardForm = {
                name: document.getElementById('cardName').value,
                number: document.getElementById('cardNumber').value.replace(/\s+/g, ''),
                exp_month: document.getElementById('cardExpiry').value.split('/')[0],
                exp_year: document.getElementById('cardExpiry').value.split('/')[1],
                cvc: document.getElementById('cardCvc').value
            };

            // Validar campos
            let hasErrors = false;

            if (!cardForm.name) {
                document.getElementById('errorCardName').textContent = 'El nombre es obligatorio';
                hasErrors = true;
            }

            if (!cardForm.number || cardForm.number.length < 15) {
                document.getElementById('errorCardNumber').textContent = 'Número de tarjeta inválido';
                hasErrors = true;
            }

            if (!cardForm.exp_month || !cardForm.exp_year) {
                document.getElementById('errorCardExpiry').textContent = 'Fecha de expiración inválida';
                hasErrors = true;
            }

            if (!cardForm.cvc || cardForm.cvc.length < 3) {
                document.getElementById('errorCardCvc').textContent = 'CVC inválido';
                hasErrors = true;
            }

            if (hasErrors) {
                // Restaurar el estado del botón
                validateButtonText.classList.remove('hidden');
                validateButtonLoading.classList.add('hidden');
                validateButton.disabled = false;
                return;
            }

            // Crear token con Conekta
            Conekta.Token.create(
                { card: cardForm },
                function(token) {
                    // Token creado exitosamente
                    @this.call('handleTokenCreated', token.id);

                    // Mostrar mensaje de éxito
                    document.getElementById('errorConekta').innerHTML = '<div class="p-4 bg-green-50 border border-green-200 text-green-700 rounded-md mb-4">Tarjeta validada correctamente</div>';

                    // Restaurar el estado del botón
                    validateButtonText.classList.remove('hidden');
                    validateButtonLoading.classList.add('hidden');
                    validateButton.disabled = false;
                },
                function(error) {
                    // Error al crear el token
                    document.getElementById('errorConekta').innerHTML = '<div class="p-4 bg-red-50 border border-red-200 text-red-700 rounded-md mb-4">Error: ' + error.message_to_purchaser + '</div>';

                    // Restaurar el estado del botón
                    validateButtonText.classList.remove('hidden');
                    validateButtonLoading.classList.add('hidden');
                    validateButton.disabled = false;
                }
            );
        };
    });
</script>
@endpush
