<div id="precios" class="bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-indigo-600 font-semibold tracking-wide uppercase">Precios</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Planes adaptados a sus necesidades
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                Ofrecemos precios transparentes y sin costos ocultos para que pueda planificar su
                inversión.
            </p>
        </div>

        <div class="mt-16 bg-white rounded-lg shadow overflow-hidden lg:grid lg:grid-cols-{{ count($plans) }} lg:gap-0">
            @foreach($plans as $planId => $plan)
                <div class="p-6 border-b border-gray-200 lg:border-b-0 {{ !$loop->last ? 'lg:border-r' : '' }} 
                    {{ $plan['popular'] ? 'bg-' . $plan['color'] . '-50 relative' : '' }}">

                    @if($plan['popular'])
                        <div class="absolute top-4 inset-x-0 -mt-3 flex justify-center">
                            <span class="inline-flex rounded-full bg-{{ $plan['color'] }}-600 px-4 py-1 text-sm font-semibold tracking-wider uppercase text-white shadow-sm">
                                MÁS POPULAR
                            </span>
                        </div>
                    @endif

                    <div>
                        <h3 class="text-2xl font-medium text-gray-900 {{ $plan['popular'] ? 'mt-3' : '' }}">{{ $plan['name'] }}</h3>
                        <p class="mt-4 text-sm text-gray-500">{{ $plan['description'] }}</p>
                        <p class="mt-8">
                            <span class="text-4xl font-extrabold text-gray-900">${{ number_format($plan['price'], 0) }}</span>
                            <span class="text-base font-medium text-gray-500">MXN/mes</span>
                        </p>

                        @php
                            $buttonClasses = $planId === 'SI-FREE' 
                                ? 'mt-8 block w-full bg-gray-200 border border-transparent rounded-md py-3 text-center font-medium text-gray-800 hover:bg-gray-300' 
                                : 'mt-8 block w-full bg-' . $plan['color'] . '-600 border border-transparent rounded-md py-3 text-center font-medium text-white hover:bg-' . $plan['color'] . '-700' . ($plan['popular'] ? ' shadow-lg' : '');
                                
                            $buttonText = $planId === 'SI-FREE' 
                                ? 'Comenzar gratis' 
                                : ($planId === 'SI-PRO' ? 'Solicitar plan ' . explode(' ', $plan['name'])[1] : 'Comenzar ahora');
                        @endphp

                        <a href="{{ route('activar') }}" class="{{ $buttonClasses }}">
                            {{ $buttonText }}
                        </a>
                    </div>

                    <div class="mt-8">
                        <div class="flex items-center">
                            <h4 class="flex-shrink-0 pr-4 text-sm font-semibold text-{{ $plan['color'] }}-600 tracking-wider uppercase">
                                Incluye
                            </h4>
                            <div class="flex-1 border-t-2 border-gray-200"></div>
                        </div>

                        <ul role="list"
                            class="mt-8 space-y-5 lg:space-y-0 lg:grid lg:grid-cols-1 lg:gap-x-8 lg:gap-y-5">
                            @foreach($plan['features'] as $feature)
                                @php
                                    if (($feature['active'] ?? true) === false) {
                                        continue;
                                    }
                                    // Determinar si la característica tiene detalles o explicación
                                    $featureTitle = is_array($feature) ? ($feature['title'] ?? '') : $feature;
                                    $hasDetails = is_array($feature) && isset($feature['details']) && $feature['details'] !== null;
                                    $hasExplanation = is_array($feature) && isset($feature['explanation']) && $feature['explanation'] !== null;
                                    $isDisabled = strpos($featureTitle, 'line-through') !== false || strpos($featureTitle, 'no ') === 0;
                                    $isNewIntegration = is_array($feature) && isset($feature['active']) && $feature['active'] === true;
                                @endphp
                                <li class="flex items-start lg:col-span-1">
                                    <div class="flex-shrink-0">
                                        @if($isDisabled)
                                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                        @else
                                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                        @endif
                                    </div>

                                    @php
                                        // Aplicar line-through si es necesario
                                        $textClasses = $isDisabled ? 'ml-3 text-sm line-through text-gray-400' : 'ml-3 text-sm text-gray-700';
                                        
                                        // Eliminar la indicación de line-through del texto
                                        $featureTitle = str_replace('line-through', '', $featureTitle);
                                    @endphp

                                    <div class="ml-3 w-full" @if($hasExplanation) x-data="{ open: false }" @endif>
                                        <div class="flex items-center">
                                            <p class="{{ $textClasses }}">
                                                {!! $featureTitle !!}
                                                @if($isNewIntegration)
                                                    <span class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                        Nuevo
                                                    </span>
                                                @endif
                                            </p>

                                            @if($hasDetails)
                                                <div class="relative ml-1" x-data="{ open: false }">
                                                    <button type="button" @click="open = !open"
                                                            @click.away="open = false"
                                                            class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                             fill="currentColor" class="w-4 h-4">
                                                            <path fill-rule="evenodd"
                                                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
                                                                  clip-rule="evenodd"/>
                                                        </svg>
                                                    </button>
                                                    <!-- Popover/Tooltip -->
                                                    <div x-show="open"
                                                         x-transition:enter="transition ease-out duration-200"
                                                         x-transition:enter-start="opacity-0 translate-y-1"
                                                         x-transition:enter-end="opacity-100 translate-y-0"
                                                         x-transition:leave="transition ease-in duration-150"
                                                         x-transition:leave-start="opacity-100 translate-y-0"
                                                         x-transition:leave-end="opacity-0 translate-y-1"
                                                         class="absolute z-50 mt-2 w-72 -left-60 bottom-8 rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-700 focus:outline-none"
                                                         style="display: none;">
                                                        <div class="p-2">
                                                            <div class="text-xs text-gray-600 dark:text-gray-400">
                                                                {!! $feature['details'] !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            @if($hasExplanation)
                                                <div class="ml-2">
                                                    <button @click="open = !open"
                                                            class="text-gray-400 hover:text-gray-600 focus:outline-none transition-transform duration-200"
                                                            :class="{ 'transform rotate-180': open }">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                             class="h-5 w-5 border border-gray-300 rounded-md cursor-pointer text-gray-400 hover:text-gray-600"
                                                             viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd"
                                                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                                  clip-rule="evenodd"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            @endif
                                        </div>

                                        @if($hasExplanation)
                                            <div x-show="open"
                                                 x-transition:enter="transition ease-out duration-200"
                                                 x-transition:enter-start="opacity-0"
                                                 x-transition:enter-end="opacity-100"
                                                 x-transition:leave="transition ease-in duration-150"
                                                 x-transition:leave-start="opacity-100"
                                                 x-transition:leave-end="opacity-0"
                                                 class="text-xs text-gray-500 bg-gray-50 p-2 rounded-md mt-2"
                                                 style="display: none;">
                                                {!! $feature['explanation'] !!}
                                            </div>
                                        @endif
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Add-ons Section -->
        <div class="mt-12 bg-white rounded-lg shadow overflow-hidden">
            <div class="p-6">
                <div>
                    <h3 class="text-2xl font-medium text-gray-900">Complementos Opcionales</h3>
                    <p class="mt-4 text-sm text-gray-500">Personalice su experiencia con estos
                        complementos adicionales para potenciar su presencia inmobiliaria.</p>
                </div>
                <div class="mt-8">
                    <div class="flex items-center">
                        <h4
                                class="flex-shrink-0 pr-4 text-sm font-semibold text-indigo-600 tracking-wider uppercase">
                            Complementos
                        </h4>
                        <div class="flex-1 border-t-2 border-gray-200"></div>
                    </div>
                    <ul role="list"
                        class="mt-8 space-y-5 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-x-8 lg:gap-y-5">
                        @foreach($complementos as $key => $complemento)
                            <li class="flex items-start lg:col-span-1">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-indigo-400" xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3 w-full" x-data="{ open: false }">
                                    <div class="flex items-center">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $complemento['title'] }}</p>
                                            <p class="text-sm text-gray-500">{{ $complemento['price'] }}</p>
                                        </div>
                                        <div class="ml-2">
                                            <button @click="open = !open"
                                                    class="text-gray-400 hover:text-gray-600 focus:outline-none transition-transform duration-200"
                                                    :class="{ 'transform rotate-180': open }">
                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                     class="h-5 w-5 border border-gray-300 rounded-md cursor-pointer text-gray-400 hover:text-gray-600"
                                                     viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                          clip-rule="evenodd"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <div x-show="open"
                                         x-transition:enter="transition ease-out duration-200"
                                         x-transition:enter-start="opacity-0"
                                         x-transition:enter-end="opacity-100"
                                         x-transition:leave="transition ease-in duration-150"
                                         x-transition:leave-start="opacity-100"
                                         x-transition:leave-end="opacity-0"
                                         class="text-xs text-gray-500 bg-gray-50 p-3 rounded-md mt-2"
                                         style="display: none;">
                                        <h4 class="font-medium text-gray-700 mb-1">{{ $complemento['description']['title'] }}</h4>
                                        @foreach($complemento['description']['body'] as $paragraph)
                                            <p class="mb-2">{!! $paragraph !!}</p>
                                        @endforeach
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                    <div class="mt-8 flex justify-center">
                        <div class="rounded-md shadow max-w-md w-full">
                            <a href="{{ route('activar') }}"
                               class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                Activar con complementos
                            </a>
                        </div>
                    </div>
                    <p class="mt-4 text-sm text-gray-500 text-center">
                        * Otras terminaciones de dominio disponibles a cotizar.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
