<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? config('app.name', 'Multibolsa Inmobiliaria') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="keywords"
          content="{{ $metaKeywords ?? 'inmobiliaria, bienes raíces, portal inmobiliario, sistema inmobiliario, propiedades, casas, terrenos, venta, renta' }}"/>
    <meta name="author" content="Multibolsa Inmobiliaria"/>
    <meta name="robots" content="{{ $metaRobots ?? 'index, follow' }}"/>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{{ $ogType ?? 'website' }}"/>
    <meta property="og:url" content="{{ url()->current() }}"/>
    <meta property="og:title"
          content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}"/>
    <meta property="og:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>
    <meta property="og:locale" content="es_MX"/>
    <meta property="og:site_name" content="{{ config('app.name') }}"/>

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:url" content="{{ url()->current() }}"/>
    <meta name="twitter:title"
          content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}"/>
    <meta name="twitter:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="twitter:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/mulbin-favicon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet"/>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @fluxAppearance
</head>
<body class="font-sans antialiased text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-zinc-800 min-h-screen flex flex-col">
<header class="bg-white dark:bg-zinc-900 shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('home') }}">
                        <x-application-logo class="block h-9 w-auto"/>
                    </a>
                </div>
            </div>

            <div class="flex items-center">
                @auth
                    <a href="{{ route('dashboard') }}"
                       class="text-sm text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 mr-4">Dashboard</a>
                @else
                    <a href="{{ route('login') }}"
                       class="text-sm text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 mr-4">Iniciar
                        sesión</a>
                    @if (Route::has('register'))
                        <a href="{{ route('register') }}"
                           class="text-sm text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Registrarse</a>
                    @endif
                @endauth
            </div>
        </div>
    </div>
</header>

<main class="flex-grow">
    @yield('content')
</main>

<footer class="bg-white dark:bg-zinc-900 shadow mt-auto">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="mb-4 md:mb-0">
                <p class="text-sm text-gray-600 dark:text-gray-400">&copy; {{ date('Y') }} Multibolsa Inmobiliaria S.A.
                    de C.V. Todos los derechos reservados.</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('privacidad') }}"
                   class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400">Aviso
                    de Privacidad</a>
                <a href="{{ route('eliminacion.datos') }}"
                   class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400">Eliminación
                    de Datos</a>
                <a href="{{ route('terminos.uso') }}"
                   class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400">Términos
                    de Uso</a>
            </div>
        </div>
    </div>
</footer>
@fluxScripts
</body>
</html>
