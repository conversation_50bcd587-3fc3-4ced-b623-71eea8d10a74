<x-layouts.app title="Planes de Conekta">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-2xl font-bold mb-6">Planes de Conekta</h2>

                    @if (isset($error))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ $error }}
                        </div>
                    @endif

                    @if (isset($createdPlans) && count($createdPlans) > 0)
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <h3 class="font-bold">Planes creados:</h3>
                            <ul class="list-disc pl-5 mt-2">
                                @foreach ($createdPlans as $plan)
                                    <li>{{ $plan }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if (isset($errors) && count($errors) > 0)
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <h3 class="font-bold">Errores:</h3>
                            <ul class="list-disc pl-5 mt-2">
                                @foreach ($errors as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="flex space-x-4 mb-8">
                        <a href="{{ route('conekta.plans.create') }}" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                            Crear Planes
                        </a>
                        <a href="{{ route('conekta.plans') }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                            Refrescar
                        </a>
                    </div>

                    <!-- Planes de la base de datos -->
                    <div class="mb-10">
                        <h3 class="text-xl font-bold mb-4 bg-blue-100 p-2 rounded">Planes en la Base de Datos</h3>
                        @if (isset($databasePlans) && count($databasePlans) > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white">
                                    <thead>
                                        <tr>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monto</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Moneda</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Intervalo</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frecuencia</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-blue-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descripción</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($databasePlans as $plan)
                                            <tr class="hover:bg-blue-50">
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->id ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->name ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ isset($plan->amount) ? number_format($plan->amount / 100, 2) : 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->currency ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->interval ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->frequency ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->description ?? 'N/A' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
                                No hay planes sincronizados en la base de datos.
                            </div>
                        @endif
                    </div>

                    <!-- Planes de Conekta -->
                    <div>
                        <h3 class="text-xl font-bold mb-4 bg-green-100 p-2 rounded">Planes en Conekta</h3>
                        @if (isset($conektaPlans) && count($conektaPlans) > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white">
                                    <thead>
                                        <tr>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monto</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Moneda</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Intervalo</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-green-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frecuencia</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($conektaPlans as $plan)
                                            @if (!isset($plan->fromDatabase))
                                                <tr class="hover:bg-green-50">
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ $plan->id ?? 'N/A' }}</td>
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ $plan->name ?? 'N/A' }}</td>
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ isset($plan->amount) ? number_format($plan->amount / 100, 2) : 'N/A' }}</td>
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ $plan->currency ?? 'N/A' }}</td>
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ $plan->interval ?? 'N/A' }}</td>
                                                    <td class="py-2 px-4 border-b border-gray-200">{{ $plan->frequency ?? 'N/A' }}</td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                                No hay planes disponibles en Conekta.
                            </div>
                        @endif
                    </div>

                    <!-- Tabla de planes combinados (antigua, si se desea mantener) -->
                    @if (false && isset($plans) && is_array($plans) && count($plans) > 0)
                        <div class="mt-10">
                            <h3 class="text-xl font-bold mb-4">Todos los Planes</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white">
                                    <thead>
                                        <tr>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monto</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Moneda</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Intervalo</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frecuencia</th>
                                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Origen</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($plans as $plan)
                                            <tr>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->id ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->name ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ isset($plan->amount) ? number_format($plan->amount / 100, 2) : 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->currency ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->interval ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">{{ $plan->frequency ?? 'N/A' }}</td>
                                                <td class="py-2 px-4 border-b border-gray-200">
                                                    @if (isset($plan->fromDatabase) && $plan->fromDatabase)
                                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Base de Datos</span>
                                                    @else
                                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Conekta</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
