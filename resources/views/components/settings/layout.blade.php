<div class="w-full max-w-6xl mx-auto">
    <!-- Encabezado y navegación horizontal -->
    <div class="mb-6">
        <div class="mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ $heading ?? '' }}</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $subheading ?? '' }}</p>
        </div>

        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="-mb-px flex space-x-6" aria-label="Settings">
                <a href="{{ route('settings.profile') }}"
                   class="{{ request()->routeIs('settings.profile') ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }} whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                   wire:navigate>
                    {{ __('Profile') }}
                </a>
                <a href="{{ route('settings.password') }}"
                   class="{{ request()->routeIs('settings.password') ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }} whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                   wire:navigate>
                    {{ __('Password') }}
                </a>
                <a href="{{ route('settings.payment') }}"
                   class="hidden {{ request()->routeIs('settings.payment') ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600' }} whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm"
                   wire:navigate>
                    {{ __('Billing Information') }}
                </a>
            </nav>
        </div>
    </div>

    <!-- Contenido principal -->
    <div class="w-full">
        <div class="w-full bg-white dark:bg-zinc-900 p-6 rounded-lg border border-neutral-200 dark:border-neutral-700 max-w-full">
            {{ $slot }}
        </div>
    </div>
</div>
