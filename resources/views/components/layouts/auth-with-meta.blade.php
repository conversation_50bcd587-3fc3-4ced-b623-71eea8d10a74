<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" @if(!config('app.force_light_mode')) class="dark" @endif>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="keywords"
          content="{{ $metaKeywords ?? 'inmobiliaria, bienes raíces, portal inmobiliario, sistema inmobiliario, propiedades, casas, terrenos, venta, renta' }}"/>
    <meta name="author" content="Multibolsa Inmobiliaria"/>
    <meta name="robots" content="{{ $metaRobots ?? 'index, follow' }}"/>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{{ $ogType ?? 'website' }}"/>
    <meta property="og:url" content="{{ url()->current() }}"/>
    <meta property="og:title"
          content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}"/>
    <meta property="og:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>
    <meta property="og:locale" content="es_MX"/>
    <meta property="og:site_name" content="{{ config('app.name') }}"/>

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:url" content="{{ url()->current() }}"/>
    <meta name="twitter:title"
          content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}"/>
    <meta name="twitter:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="twitter:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}"/>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/mulbin-favicon.png') }}">
    <link rel="apple-touch-icon" sizes="180x180"
          href="{{ asset('images/mulbin-apple-touch-icon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet"/>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .real-estate-bg {
            background-image: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.45)), url("https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>

<body class="min-h-screen antialiased">
<div class="flex min-h-svh">
    <!-- Imagen lateral solo visible en pantallas medianas y grandes -->
    <div class="hidden lg:flex lg:w-1/2 real-estate-bg">
        <div class="flex flex-col justify-center items-center w-full p-8">
            <div class="bg-black/30 backdrop-blur-sm p-8 rounded-xl max-w-md text-center">
                <h2 class="text-3xl font-bold text-white mb-4">Multibolsa Inmobiliaria</h2>
                <p class="text-white/90 mb-6">Conecta con profesionales del sector inmobiliario y
                    expande tu red de negocios</p>
            </div>
        </div>
    </div>

    <!-- Formulario de autenticación -->
    <div
            class="w-full lg:w-1/2 bg-white dark:bg-linear-to-b dark:from-neutral-950 dark:to-neutral-900 overflow-y-auto">
        <div class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
            <div class="flex w-full max-w-sm flex-col gap-2">
                <a href="{{ route('home') }}" class="flex flex-col items-center gap-2 font-medium"
                   wire:navigate>
                    <img src="{{ asset('images/logo-sistema-inmobiliario.png') }}"
                         alt="Sistema Inmobiliario" class="h-14 w-auto mb-1">
                </a>
                <div class="flex flex-col gap-6">
                    {{ $slot }}
                </div>
            </div>
        </div>
    </div>
</div>
@fluxScripts
</body>

</html>
