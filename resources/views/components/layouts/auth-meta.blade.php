@props([
    'title' => null,
    'metaDescription' => null,
    'metaKeywords' => null,
    'metaRobots' => 'index, follow',
    'ogType' => 'website',
    'ogImage' => null,
])

@php
    $currentRoute = request()->route()->getName() ?? '';

    // Establecer valores predeterminados según la ruta
    if (!$title) {
        if (str_contains($currentRoute, 'login')) {
            $title = 'Iniciar Sesión';
        } elseif (str_contains($currentRoute, 'register')) {
            $title = 'Registro de Profesionales Inmobiliarios';
        } elseif (str_contains($currentRoute, 'password.request')) {
            $title = 'Recuperar Contraseña';
        } elseif (str_contains($currentRoute, 'password.reset')) {
            $title = 'Restablecer Contraseña';
        } elseif (str_contains($currentRoute, 'verification.notice')) {
            $title = 'Verificar Correo Electrónico';
        } elseif (str_contains($currentRoute, 'password.confirm')) {
            $title = 'Confirmar Contraseña';
        }
    }

    if (!$metaDescription) {
        if (str_contains($currentRoute, 'login')) {
            $metaDescription =
                'Accede a tu portal inmobiliario y conecta con profesionales del sector. Gestiona propiedades y expande tu red de negocios inmobiliarios.';
        } elseif (str_contains($currentRoute, 'register')) {
            $metaDescription =
                'Crea tu cuenta en el sistema inmobiliario y obtén tu propio portal web personalizado. Conecta con profesionales y expande tu red de negocios.';
        } elseif (str_contains($currentRoute, 'password.request')) {
            $metaDescription =
                'Recupera el acceso a tu cuenta del sistema inmobiliario. Ingresa tu correo electrónico para recibir un enlace de recuperación.';
        } elseif (str_contains($currentRoute, 'password.reset')) {
            $metaDescription =
                'Restablece tu contraseña del sistema inmobiliario. Crea una nueva contraseña segura para acceder a tu cuenta.';
        } elseif (str_contains($currentRoute, 'verification.notice')) {
            $metaDescription =
                'Completa el proceso de registro verificando tu correo electrónico. Este paso es necesario para activar todas las funciones de tu cuenta inmobiliaria.';
        } elseif (str_contains($currentRoute, 'password.confirm')) {
            $metaDescription =
                'Confirma tu contraseña para acceder a áreas seguras del sistema inmobiliario. Este paso adicional protege tu información sensible.';
        } else {
            $metaDescription =
                'Accede a tu sistema inmobiliario y gestiona tus propiedades. Conecta con otros profesionales del sector inmobiliario.';
        }
    }

    if (!$metaKeywords) {
        if (str_contains($currentRoute, 'login')) {
            $metaKeywords =
                'login inmobiliario, acceso sistema inmobiliario, portal inmobiliario, ingresar mulbin';
        } elseif (str_contains($currentRoute, 'register')) {
            $metaKeywords =
                'registro inmobiliario, crear cuenta inmobiliaria, portal inmobiliario, multibolsa, sistema inmobiliario';
        } elseif (str_contains($currentRoute, 'password.request')) {
            $metaKeywords =
                'recuperar contraseña, olvidé contraseña, reset password inmobiliario, acceso sistema inmobiliario';
        } elseif (str_contains($currentRoute, 'password.reset')) {
            $metaKeywords =
                'restablecer contraseña, nueva contraseña, reset password inmobiliario, seguridad';
        } elseif (str_contains($currentRoute, 'verification.notice')) {
            $metaKeywords = 'verificar email, confirmar correo, email verificación, sistema inmobiliario';
        } elseif (str_contains($currentRoute, 'password.confirm')) {
            $metaKeywords =
                'confirmar contraseña, verificación seguridad, acceso sistema inmobiliario, seguridad';
        } else {
            $metaKeywords =
                'login inmobiliario, acceso sistema inmobiliario, registro inmobiliario, portal inmobiliario';
        }
    }

    if (!$ogImage) {
        $ogImage = asset('images/og-mulbin-2.jpg');
    }

    // Establecer robots para rutas sensibles
    if (str_contains($currentRoute, 'password') || str_contains($currentRoute, 'verification')) {
        $metaRobots = 'noindex, follow';
    }
@endphp

<x-layouts.auth-with-meta :title="$title" :metaDescription="$metaDescription" :metaKeywords="$metaKeywords" :metaRobots="$metaRobots"
    :ogType="$ogType" :ogImage="$ogImage">
    {{ $slot }}
</x-layouts.auth-with-meta>
