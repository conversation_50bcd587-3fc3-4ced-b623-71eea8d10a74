<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description"
        content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}" />
    <meta name="keywords"
        content="{{ $metaKeywords ?? 'inmobiliaria, bienes raíces, portal inmobiliario, sistema inmobiliario, propiedades, casas, terrenos, venta, renta' }}" />
    <meta name="author" content="Multibolsa Inmobiliaria" />
    <meta name="robots" content="{{ $metaRobots ?? 'index, follow' }}" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{{ $ogType ?? 'website' }}" />
    <meta property="og:url" content="{{ url()->current() }}" />
    <meta property="og:title"
        content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}" />
    <meta property="og:description"
        content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}" />
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}" />
    <meta property="og:locale" content="es_MX" />
    <meta property="og:site_name" content="{{ config('app.name') }}" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="{{ url()->current() }}" />
    <meta name="twitter:title"
        content="{{ isset($title) ? $title . ' - ' . config('app.name') : config('app.name') }}" />
    <meta name="twitter:description"
        content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}" />
    <meta name="twitter:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}" />

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}" />

    @yield('meta')

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/mulbin-favicon.png') }}">
    <link rel="apple-touch-icon" sizes="180x180"
        href="{{ asset('images/mulbin-apple-touch-icon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Alpine.js Cloak Style -->
    <style>
        [x-cloak] { display: none !important; }
    </style>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body x-data class="font-sans antialiased text-gray-900 h-full bg-white dark:bg-zinc-800">
    <div class="min-h-screen flex flex-col">
        @include('sistema-inmobiliario.partials.navigation')

        <!-- Page Heading -->
        @if (isset($header))
            <header class="bg-white shadow dark:bg-zinc-800 dark:border-b dark:border-zinc-700">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main class="flex-1 container mx-auto py-6 px-4 sm:px-6 lg:px-8">
            {{ $slot }}
        </main>

        <!-- Footer -->
        @include('sistema-inmobiliario.partials.footer')
    </div>

    <!-- Scripts de Livewire y Flux -->
    @livewireScripts
    @fluxScripts
    @stack('scripts')
</body>

</html>
