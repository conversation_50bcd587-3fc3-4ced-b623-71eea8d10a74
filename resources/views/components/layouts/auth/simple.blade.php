<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">

<head>
    @include('partials.head')
    <style>
        .real-estate-bg {
            background-image: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.45)), url("https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>

<body class="min-h-screen antialiased">
    <div class="flex min-h-svh">
        <!-- Imagen lateral solo visible en pantallas medianas y grandes -->
        <div class="hidden lg:flex lg:w-1/2 real-estate-bg">
            <div class="flex flex-col justify-center items-center w-full p-8">
                <div class="bg-black/30 backdrop-blur-sm p-8 rounded-xl max-w-md text-center">
                    <h2 class="text-3xl font-bold text-white mb-4">Multibolsa Inmobiliaria</h2>
                    <p class="text-white/90 mb-6">Conecta con profesionales del sector inmobiliario y
                        expande tu red de negocios</p>
                    <div class="flex justify-center space-x-2">
                        <span class="inline-block w-3 h-3 bg-white rounded-full opacity-70"></span>
                        <span class="inline-block w-3 h-3 bg-white rounded-full"></span>
                        <span class="inline-block w-3 h-3 bg-white rounded-full opacity-70"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Formulario de autenticación -->
        <div
            class="w-full lg:w-1/2 bg-white dark:bg-linear-to-b dark:from-neutral-950 dark:to-neutral-900 overflow-y-auto">
            <div class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
                <div class="flex w-full max-w-sm flex-col gap-2">
                    <a href="{{ route('home') }}" class="flex flex-col items-center gap-2 font-medium"
                        wire:navigate>
                        <img src="{{ asset('images/logo-sistema-inmobiliario.png') }}"
                            alt="Sistema Inmobiliario" class="h-14 w-auto mb-1">
                    </a>
                    <div class="flex flex-col gap-6">
                        {{ $slot }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @fluxScripts
</body>

</html>
