<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">

<head>
    @include('partials.head')
</head>

<body
    class="min-h-screen bg-white antialiased dark:bg-linear-to-b dark:from-neutral-950 dark:to-neutral-900">
    <div
        class="relative grid h-dvh flex-col items-center justify-center px-8 sm:px-0 lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div
            class="bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-e dark:border-neutral-800">
            <div class="absolute inset-0 bg-neutral-900"></div>
            <a href="{{ route('home') }}" class="relative z-20 flex items-center text-lg font-medium"
                wire:navigate>
                <img src="{{ asset('images/logo-sistema-inmobiliario.png') }}" alt="Multibolsa Inmobiliaria"
                    class="h-10 w-auto mr-3">
                {{ config('app.name', 'Multibolsa Inmobiliaria') }}
            </a>

            @php
                [$message, $author] = str(Illuminate\Foundation\Inspiring::quotes()->random())->explode(
                    '-',
                );
            @endphp

            <div class="relative z-20 mt-auto">
                <blockquote class="space-y-2">
                    <flux:heading size="lg">&ldquo;{{ trim($message) }}&rdquo;</flux:heading>
                    <footer>
                        <flux:heading>{{ trim($author) }}</flux:heading>
                    </footer>
                </blockquote>
            </div>
        </div>
        <div class="w-full lg:p-8">
            <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
                <a href="{{ route('home') }}"
                    class="z-20 flex flex-col items-center gap-2 font-medium lg:hidden" wire:navigate>
                    <img src="{{ asset('images/logo-sistema-inmobiliario.png') }}"
                        alt="Multibolsa Inmobiliaria" class="h-14 w-auto">
                    <span
                        class="text-lg font-bold text-indigo-600 dark:text-indigo-400">{{ config('app.name', 'Multibolsa Inmobiliaria') }}</span>
                </a>
                {{ $slot }}
            </div>
        </div>
    </div>
    @fluxScripts
</body>

</html>
