<x-layouts.app title="Activación Solicitada - Sistema Inmobiliario"
               :metaDescription="'Su solicitud para activar el Sistema Inmobiliario ha sido recibida con éxito. Recibirá sus credenciales en las próximas 24 horas hábiles.'"
               :metaKeywords="'activación exitosa, sistema inmobiliario, portal inmobiliario'"
               :metaRobots="'noindex, follow'" :ogType="'website'">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center h-24 w-24 rounded-full bg-green-100 mb-5">
                            <svg class="h-16 w-16 text-green-600" xmlns="http://www.w3.org/2000/svg"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-extrabold text-gray-900">¡Solicitud recibida!</h2>
                        <p class="mt-3 text-xl text-gray-600 max-w-3xl mx-auto">
                            Su solicitud para activar el Sistema Inmobiliario ha sido recibida con éxito.
                        </p>

                        <!-- Resumen del plan seleccionado -->
                        <div class="mt-8 max-w-md mx-auto bg-indigo-50 p-6 rounded-lg border border-indigo-100 text-left">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Resumen de su plan</h3>

                            @if (session('plan'))
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">Plan seleccionado:</span>
                                    <span class="text-sm font-bold text-gray-900">
                                        @if (session('plan') == 'SI-FREE')
                                            Plan Freemium
                                        @elseif (session('plan') == 'SI-PRO')
                                            Plan PRO
                                        @elseif (session('plan') == 'SI-PLUS')
                                            Plan PLUS
                                        @endif
                                    </span>
                                </div>

                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">Período de facturación:</span>
                                    <span class="text-sm text-gray-900">
                                        @if (session('billing_period') == 'monthly')
                                            Mensual
                                        @elseif (session('billing_period') == 'quarterly')
                                            Trimestral
                                        @elseif (session('billing_period') == 'biannual')
                                            Semestral
                                        @elseif (session('billing_period') == 'annual')
                                            Anual
                                        @endif
                                    </span>
                                </div>

                                @if (session('plan') != 'SI-FREE')
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700">Precio mensual:</span>
                                        <span class="text-sm text-gray-900">${{ number_format(session('plan_price'), 0) }} MXN</span>
                                    </div>

                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700">Total a pagar:</span>
                                        <span class="text-sm font-bold text-gray-900">${{ number_format(session('total_price'), 0) }} MXN</span>
                                    </div>
                                @else
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700">Precio:</span>
                                        <span class="text-sm text-gray-900">Gratis</span>
                                    </div>
                                @endif
                            @else
                                <p class="text-sm text-gray-600">Su solicitud de activación ha sido registrada
                                    correctamente.</p>
                            @endif
                        </div>

                        <div class="mt-8 bg-gray-50 p-6 rounded-lg max-w-2xl mx-auto">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Próximos pasos</h3>
                            <ul class="mt-3 space-y-3 text-sm text-gray-600 text-left">
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 text-indigo-500 flex-shrink-0 mr-2"
                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                    <span>Nuestro equipo revisará su solicitud en las próximas horas.</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 text-indigo-500 flex-shrink-0 mr-2"
                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                    </svg>
                                    <span>Recibirá un correo electrónico con las instrucciones de acceso en las próximas <strong>24 horas hábiles</strong>.</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 text-indigo-500 flex-shrink-0 mr-2"
                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                    <span>Podrá comenzar a utilizar el sistema inmediatamente después de recibir sus credenciales.</span>
                                </li>
                                <li>Nuestro equipo de soporte se pondrá en contacto para ayudarle con la
                                    configuración inicial.
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6">
                            <p class="text-sm text-gray-500">
                                Si tiene alguna pregunta o no recibe sus credenciales en el tiempo
                                indicado, por favor contacte a nuestro equipo de soporte a <a
                                        href="mailto:{{ env('MULBIN_SUPPORT_EMAIL') }}"
                                        class="font-medium text-indigo-600 hover:text-indigo-500">{{ env('MULBIN_SUPPORT_EMAIL') }}</a>.
                            </p>
                        </div>
                        <div class="mt-8">
                            @auth
                                <a href="{{ route('dashboard') }}"
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Ir a mi Dashboard
                                </a>
                            @else
                                <a href="{{ route('home') }}"
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Volver al inicio
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
