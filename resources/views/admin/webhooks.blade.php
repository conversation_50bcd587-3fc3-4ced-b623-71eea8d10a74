<x-layouts.app title="Webhooks de Conekta">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-2xl font-bold mb-6">Webhooks de Conekta</h2>
                    
                    @if (session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif
                    
                    @if ($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <div class="mb-6">
                        <form action="{{ route('admin.webhooks') }}" method="GET" class="flex flex-wrap gap-4 items-end">
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Tipo de Evento</label>
                                <select name="type" id="type" class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                                    <option value="">Todos</option>
                                    <option value="subscription.created" {{ request('type') == 'subscription.created' ? 'selected' : '' }}>Suscripción Creada</option>
                                    <option value="subscription.paid" {{ request('type') == 'subscription.paid' ? 'selected' : '' }}>Suscripción Pagada</option>
                                    <option value="subscription.payment_failed" {{ request('type') == 'subscription.payment_failed' ? 'selected' : '' }}>Pago Fallido</option>
                                    <option value="subscription.canceled" {{ request('type') == 'subscription.canceled' ? 'selected' : '' }}>Suscripción Cancelada</option>
                                    <option value="subscription.paused" {{ request('type') == 'subscription.paused' ? 'selected' : '' }}>Suscripción Pausada</option>
                                    <option value="subscription.resumed" {{ request('type') == 'subscription.resumed' ? 'selected' : '' }}>Suscripción Reanudada</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="cliente_ids" class="block text-sm font-medium text-gray-700 mb-1">ID de Cliente</label>
                                <input type="text" name="cliente_ids" id="cliente_ids" value="{{ request('cliente_ids') }}" class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Desde</label>
                                <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}" class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Hasta</label>
                                <input type="date" name="date_to" id="date_to" value="{{ request('date_to') }}" class="block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Filtrar
                                </button>
                                
                                <a href="{{ route('admin.webhooks') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ml-2">
                                    Limpiar
                                </a>
                            </div>
                        </form>
                    </div>
                    
                    @if (count($webhooks) > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white">
                                <thead>
                                    <tr>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha</th>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID Item</th>
                                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($webhooks as $webhook)
                                        <tr>
                                            <td class="py-2 px-4 border-b border-gray-200">{{ $webhook->id }}</td>
                                            <td class="py-2 px-4 border-b border-gray-200">{{ $webhook->created_at->format('d/m/Y H:i:s') }}</td>
                                            <td class="py-2 px-4 border-b border-gray-200">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                    @if (strpos($webhook->type, 'paid') !== false)
                                                        bg-green-100 text-green-800
                                                    @elseif (strpos($webhook->type, 'failed') !== false)
                                                        bg-red-100 text-red-800
                                                    @elseif (strpos($webhook->type, 'canceled') !== false)
                                                        bg-gray-100 text-gray-800
                                                    @elseif (strpos($webhook->type, 'created') !== false)
                                                        bg-blue-100 text-blue-800
                                                    @elseif (strpos($webhook->type, 'paused') !== false)
                                                        bg-yellow-100 text-yellow-800
                                                    @elseif (strpos($webhook->type, 'resumed') !== false)
                                                        bg-purple-100 text-purple-800
                                                    @else
                                                        bg-gray-100 text-gray-800
                                                    @endif
                                                ">
                                                    {{ $webhook->type }}
                                                </span>
                                            </td>
                                            <td class="py-2 px-4 border-b border-gray-200">{{ $webhook->cliente_ids ?? 'N/A' }}</td>
                                            <td class="py-2 px-4 border-b border-gray-200">{{ $webhook->id_item ?? 'N/A' }}</td>
                                            <td class="py-2 px-4 border-b border-gray-200">
                                                <a href="{{ route('admin.webhooks.show', $webhook->id) }}" class="text-indigo-600 hover:text-indigo-900">Ver Detalles</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            {{ $webhooks->links() }}
                        </div>
                    @else
                        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                            No hay webhooks que coincidan con los criterios de búsqueda.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
