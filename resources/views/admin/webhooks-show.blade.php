<x-layouts.app title="Detalles del Webhook">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold">Detalles del Webhook #{{ $webhook->id }}</h2>
                        <a href="{{ route('admin.webhooks') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Volver a la lista
                        </a>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">ID</p>
                                <p class="mt-1">{{ $webhook->id }}</p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500">Fecha</p>
                                <p class="mt-1">{{ $webhook->created_at->format('d/m/Y H:i:s') }}</p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500">Tipo</p>
                                <p class="mt-1">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        @if (strpos($webhook->type, 'paid') !== false)
                                            bg-green-100 text-green-800
                                        @elseif (strpos($webhook->type, 'failed') !== false)
                                            bg-red-100 text-red-800
                                        @elseif (strpos($webhook->type, 'canceled') !== false)
                                            bg-gray-100 text-gray-800
                                        @elseif (strpos($webhook->type, 'created') !== false)
                                            bg-blue-100 text-blue-800
                                        @elseif (strpos($webhook->type, 'paused') !== false)
                                            bg-yellow-100 text-yellow-800
                                        @elseif (strpos($webhook->type, 'resumed') !== false)
                                            bg-purple-100 text-purple-800
                                        @else
                                            bg-gray-100 text-gray-800
                                        @endif
                                    ">
                                        {{ $webhook->type }}
                                    </span>
                                </p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500">Origen</p>
                                <p class="mt-1">{{ $webhook->origin ?? 'N/A' }}</p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500">Cliente</p>
                                <p class="mt-1">{{ $webhook->cliente_ids ?? 'N/A' }}</p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500">ID Item</p>
                                <p class="mt-1">{{ $webhook->id_item ?? 'N/A' }}</p>
                            </div>
                            
                            @if ($webhook->cobro_id)
                            <div>
                                <p class="text-sm font-medium text-gray-500">Cobro ID</p>
                                <p class="mt-1">{{ $webhook->cobro_id }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mt-8">
                        <h3 class="text-lg font-medium mb-4">Datos del Webhook (JSON)</h3>
                        <div class="bg-gray-800 text-white p-4 rounded-lg overflow-x-auto">
                            <pre class="text-sm">{{ json_encode($webhook->data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) }}</pre>
                        </div>
                    </div>
                    
                    @if ($webhook->cliente)
                    <div class="mt-8">
                        <h3 class="text-lg font-medium mb-4">Información del Cliente</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Usuario</p>
                                    <p class="mt-1">{{ $webhook->cliente->usuario }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Nombre</p>
                                    <p class="mt-1">{{ $webhook->cliente->name }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Email</p>
                                    <p class="mt-1">{{ $webhook->cliente->email }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Teléfono</p>
                                    <p class="mt-1">{{ $webhook->cliente->telefono ?? 'N/A' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    @if ($webhook->cobro)
                    <div class="mt-8">
                        <h3 class="text-lg font-medium mb-4">Información del Cobro</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Número</p>
                                    <p class="mt-1">{{ $webhook->cobro->numero }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Fecha</p>
                                    <p class="mt-1">{{ $webhook->cobro->fecha->format('d/m/Y') }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Servicio</p>
                                    <p class="mt-1">{{ $webhook->cobro->servicio }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Descripción</p>
                                    <p class="mt-1">{{ $webhook->cobro->descripcion }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Precio</p>
                                    <p class="mt-1">${{ number_format($webhook->cobro->precio, 2) }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Estado</p>
                                    <p class="mt-1">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            {{ $webhook->cobro->pagado ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $webhook->cobro->pagado ? 'Pagado' : 'Pendiente' }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
