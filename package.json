{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "watch": "SHELL=/bin/sh chokidar 'resources/js/**/*.js' -c 'npm run build -- --watch'"}, "dependencies": {"@alpinejs/mask": "^3.14.9", "@tailwindcss/vite": "^4.0.7", "alpinejs": "^3.13.1", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "tailwindcss": "^4.0.7", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"chokidar": "^4.0.3", "chokidar-cli": "^3.0.0"}}